﻿/******************************************************************************
 * Copyright 2024 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai
 *
 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONS<PERSON>QUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "app_controller.h"
#include "app_event.h"
#include "lidar_ctl.h"
#include "ui_mainwindow.h"
#include "utils/common.h"
#include "work_model/work_model_factory.h"
#include <QDesktopServices>
#include <QFileDialog>
#include <QProgressDialog>
#include <QTextStream>

#include "matplotlibcpp.h"
namespace plt = matplotlibcpp;

// NOLINTNEXTLINE(modernize-concat-nested-namespaces)
namespace robosense
{
namespace lidar
{

AppController::AppController(MainWindow* _main_window, QObject* _parent) :
  QObject(_parent), main_window_(_main_window), ui_(main_window_->ui_)
{
  initWorkModel();
  initSignalSlots();
  initExtDevice();
}
AppController::~AppController() = default;

void AppController::initWorkModel()
{
  uint32_t index   = 1;
  auto* lidar_info = ui_->widget_lidar_info_list->getLidarInfo(index);
  updateModelParaInfo();
  initFsm();
  connect(lidar_info, &WidgetLidarInfo::signalProjectCodeChanged, this, [this]() {
    updateModelParaInfo();
    initFsm();
  });
}

void AppController::updateModelParaInfo()
{
  auto para_info = std::make_shared<ParaInfo>();

  const QMetaObject* meta_obj = para_info->metaObject();
  for (int i = 0; i < meta_obj->propertyCount(); ++i)
  {
    QMetaProperty meta_prop = meta_obj->property(i);
    QString prop_name       = meta_prop.name();
    QString name_suffix     = "_para_var_";
    if (!prop_name.endsWith(name_suffix))
    {
      continue;
    }

    QString widget_name = prop_name.left(prop_name.size() - name_suffix.size());

    auto* widget = ui_->centralwidget->findChild<QWidget*>(widget_name);
    if (widget == nullptr)
    {
      LOG_ERROR("widget {} not found", widget_name.toStdString());
      continue;
    }
    QVariant value;
    if (auto* line_edit = qobject_cast<QLineEdit*>(widget))
    {
      value = line_edit->text();
    }
    else if (auto* spin_box = qobject_cast<QSpinBox*>(widget))
    {
      value = spin_box->value();
    }
    else if (auto* double_spin_box = qobject_cast<QDoubleSpinBox*>(widget))
    {
      value = double_spin_box->value();
    }
    else if (auto* check_box = qobject_cast<QCheckBox*>(widget))
    {
      value = check_box->isChecked();
    }
    else if (auto* combo_box = qobject_cast<QComboBox*>(widget))
    {
      value = combo_box->currentText();
    }
    else
    {
      LOG_ERROR("widget {} type not supported", widget_name);
      continue;
    }

    if (!para_info->setProperty(prop_name.toUtf8(), value))
    {
      LOG_ERROR("set property failed: {}", prop_name.toStdString());
    }
    // LOG_INFO("prop_name: {}, value: {}", prop_name.toStdString(), para_info->property(prop_name.toUtf8()).toString());
  }

  for (auto& [lidar_index, model] : model_map_)
  {
    model->setParaInfo(para_info);
  }
}

void AppController::initFsm()
{
  uint32_t index              = 1;
  auto* lidar_info            = ui_->widget_lidar_info_list->getLidarInfo(index);
  auto airy_wave_signal_model = WorkModelFactory::createWorkModel(lidar_info);
  model_map_[index]           = airy_wave_signal_model;

  fsm_ = airy_wave_signal_model->createFsm();
  fsm_->setWorkModelVec({ airy_wave_signal_model });

  main_window_->setModelMap(model_map_);

  updateModelParaInfo();
}

void AppController::startUpFsm()
{
  updateModelParaInfo();
  initFsm();

  fsm_->startup(ActionState::STATE_CONNECT_LIDAR);
}

void AppController::shutDownFsm()
{
  if (fsm_)
  {
    fsm_->quit();
    // fsm_.reset();
    app()->signalUpdateTestState(TestState::ABORT);
  }
}

void AppController::initExtDevice() { extDevice()->init(); }
void AppController::initSignalSlots()
{
  // lidar info
  connect(ui_->widget_lidar_info_list, &WidgetLidarInfoList::signalButtonConnectClicked, this,
          &AppController::slotButtonConnectClicked);
  connect(ui_->widget_lidar_info_list, &WidgetLidarInfoList::signalButtonDisconnectClicked, this,
          &AppController::slotButtonDisconnectClicked);
  connect(ui_->widget_lidar_info_list, &WidgetLidarInfoList::signalButtonOpenDataFolderClicked, this,
          &AppController::slotButtonOpenDataFolderClicked);

  connect(ui_->button_update_para_info, &QPushButton::clicked, this, &AppController::slotButtonUpdateParaInfoClicked);
  connect(ui_->button_stop_motor_to_angle, &QPushButton::clicked, this,
          &AppController::slotButtonStopMotorToAngleClicked);
  connect(main_window_, &MainWindow::signalStopMotor, this, &AppController::slotButtonStopMotorToAngleClicked);
  connect(ui_->button_start_motor, &QPushButton::clicked, this, &AppController::slotButtonStartMotorClicked);
  connect(ui_->button_data_fit, &QPushButton::clicked, this, &AppController::slotButtonProcessClicked);
  connect(ui_->button_one_key_run, &QPushButton::clicked, this, &AppController::slotButtonOneKeyRunClicked);
  connect(ui_->button_rw_reg_from_file, &QPushButton::clicked, this, &AppController::slotButtonRwRegFromFileClicked);
  connect(ui_->button_zero_self_test, &QPushButton::clicked, this, &AppController::slotButtonZeroSelfTestClicked);
  connect(ui_->button_zero_calib, &QPushButton::clicked, this, &AppController::slotButtonZeroCalibClicked);
  connect(ui_->btn_zero_set, &QPushButton::clicked, this, &AppController::slotButtonZeroSetClicked);
  connect(ui_->button_write_top_flash, &QPushButton::clicked, this, &AppController::slotButtonWriteTopFlashClicked);
  connect(ui_->button_custom_process, &QPushButton::clicked, this, &AppController::slotButtonCustomProcessClicked);
  connect(ui_->button_write_csv_init, &QPushButton::clicked, this, &AppController::slotButtonWriteCsvInitClicked);
  connect(ui_->button_init_lidar, &QPushButton::clicked, this, &AppController::slotButtonInitLidarClicked);
  connect(ui_->button_one_key_collect, &QPushButton::clicked, this, &AppController::slotButtonOneKeyCollectClicked);
  connect(ui_->button_one_key_process, &QPushButton::clicked, this, &AppController::slotButtonOneKeyProcessClicked);
  connect(ui_->button_write_bit_to_lidar, &QPushButton::clicked, this,
          &AppController::slotButtonWriteBitToLidarClicked);
  connect(ui_->button_auto_set_sn, &QPushButton::clicked, this, &AppController::slotButtonAutoSetSnClicked);
  connect(ui_->button_main_open_relay, &QPushButton::clicked, this, []() { extDevice()->slotRelayTurnOn(); });
  connect(ui_->button_main_close_relay, &QPushButton::clicked, this, []() { extDevice()->slotRelayTurnOff(); });
  connect(ui_->button_rotate_light_spot, &QPushButton::clicked, this, &AppController::slotButtonRotateLightSpotClicked);
  connect(ui_->button_rotate_setup_lidar, &QPushButton::clicked, this,
          &AppController::slotButtonRotateSetupLidarClicked);
  connect(main_window_->para_widgets_.button_save_para, &QPushButton::clicked, this,
          &AppController::slotButtonUpdateParaInfoClicked);
  connect(main_window_->para_widgets_.button_reload_config_para, &QPushButton::clicked, this,
          &AppController::slotButtonReloadConfigParaClicked);
  connect(main_window_->para_widgets_.button_auto_get_eth, &QPushButton::clicked, this,
          &AppController::slotButtonAutoGetEthClicked);
  connect(ui_->button_close_eye_safe, &QPushButton::clicked, this, &AppController::slotButtonEyeSafeClicked);
  connect(ui_->button_compare, &QPushButton::clicked, main_window_, &MainWindow::slotButtonCompareClicked);
  connect(ui_->button_test, &QPushButton::clicked, this, &AppController::slotButtonTestClicked);
  connect(ui_->button_write_chn_angle, &QPushButton::clicked, this, &AppController::slotButtonWriteChnAngle);

  // process data
  connect(ui_->button_display_process_data, &QPushButton::clicked, this,
          &AppController::slotButtonDisplayProcessDataClicked);

  // vbd
  connect(main_window_, &MainWindow::signalLoadVbdFile, this, &AppController::slotVbdLoadFile);

  connect(ui_->button_vbd_load, &QPushButton::clicked, this, &AppController::slotButtonLoadVbdClicked);
  connect(ui_->button_vbd_load_and_write, &QPushButton::clicked, this,
          &AppController::slotButtonVbdLoadAndWriteClicked);
  connect(ui_->button_vbd_write_flash, &QPushButton::clicked, this, &AppController::slotButtonVbdWriteFlashClicked);
  connect(ui_->button_vbd_read, &QPushButton::clicked, this, &AppController::slotButtonVbdReadClicked);
  connect(ui_->button_vbd_calculate_intercept, &QPushButton::clicked, this,
          &AppController::slotButtonVbdCalculateInterceptClicked);
  connect(ui_->button_vbd_read_curve, &QPushButton::clicked, this, &AppController::slotButtonVbdReadCurveClicked);
}

float AppController::bcdToFloat(const uint16_t _bcd_val)
{
  // 1. 分离高 8 位和低 8 位
  uint8_t high = (_bcd_val >> 8U) & 0xFFU;  // 高 8 位（整数部分）
  uint8_t low  = _bcd_val & 0xFFU;          // 低 8 位（小数部分）

  // 2. 将高 8 位 BCD 转换为整数
  int integer_part = ((high >> 4U) * 10) + (high & 0x0FU);

  // 3. 将低 8 位 BCD 转换为小数
  float fractional_part = ((low >> 4U) * 10.F) + (low & 0x0FU);
  fractional_part /= 100.0F;  // 小数部分需要除以 100

  // 4. 合并整数和小数部分
  return static_cast<float>(integer_part) + fractional_part;
}

void AppController::slotButtonConnectClicked(const quint32 _index)
{
  if (model_map_.find(_index) == model_map_.end())
  {
    LOG_ERROR("lidar index {} not exist in list, please contact developer", _index);
    return;
  }
  if (!model_map_[_index]->scanConnectLidarAndWaitForTop())
  {
    LOG_ERROR("scan first lidar and set ip failed");
    return;
  }
  // model_map_[_index]->connectLidar();
}
void AppController::slotButtonDisconnectClicked(const quint32 _index)
{
  if (model_map_.find(_index) == model_map_.end())
  {
    LOG_ERROR("lidar index {} not exist in list, please contact developer", _index);
    return;
  }
  model_map_[_index]->disconnectLidar();
}
void AppController::slotButtonOpenDataFolderClicked(const quint32 _index)
{
  if (model_map_.find(_index) == model_map_.end())
  {
    LOG_ERROR("lidar index {} not exist in list, please contact developer", _index);
    return;
  }
  auto path = model_map_[_index]->getDataPath();
  QDesktopServices::openUrl(QUrl::fromLocalFile(path));
}

void AppController::slotButtonOneKeyRunClicked()
{
  if (ui_->button_one_key_run->text() == "一键运行")
  {
    LOG_INFO("一键运行...");
    app()->signalUpdateProgressProcessData(0);
    app()->signalUpdateProgressCollect(0);
    startUpFsm();
  }
  else
  {
    LOG_INFO("停止中...");
    shutDownFsm();
  }
}

void AppController::slotButtonTestClicked()
{

  // testCompare();
  LOG_TRACE("TRACE");
  LOG_DEBUG("DEBUG");
  LOG_INFO("INFO");
  LOG_ERROR("ERROR");
  LOG_INFO("q m level: {}", RSFSCLog::getInstance()->getQMessageBrowserLogLevel());
}

void AppController::slotButtonWriteCsvInitClicked()
{
  if (model_map_.find(1) == model_map_.end())
  {
    LOG_ERROR("lidar index {} not exist in list, please contact developer", 1);
    return;
  }
  getWorkModel()->writeCsvData(ui_->lineedit_csv_init_name->text());
}
void AppController::slotButtonUpdateParaInfoClicked()
{
  updateModelParaInfo();
  app()->signalShowInfoText("参数更新完成");
}
void AppController::slotButtonReloadConfigParaClicked()
{
  MainWindow::loadCsvAndJson();
  for (auto& [index, model] : model_map_)
  {
    model->loadConfigData();
  }
  updateModelParaInfo();
  app()->signalShowInfoText("参数重新加载完成");
}
void AppController::slotButtonAutoGetEthClicked()
{
  auto eth_name = TcpdumpUtils::getNetworkInterFaceByIP("*************");
  QMetaObject::invokeMethod(main_window_->para_widgets_.lineedit_eth_name, "setText", Qt::QueuedConnection,
                            Q_ARG(QString, QString::fromStdString(eth_name)));
}
void AppController::slotButtonStopMotorToAngleClicked()
{
  if (model_map_.find(1) == model_map_.end())
  {
    LOG_ERROR("lidar index {} not exist in list, please contact developer", 1);
    return;
  }
  float stop_angle = static_cast<float>(ui_->spinbox_stop_motor_to_angle->value());
  if (stop_angle < 0)
  {
    stop_angle += 360;
  }
  getWorkModel()->getLidarManager()->stopMotorToAngle(stop_angle);
}
void AppController::slotButtonStartMotorClicked()
{
  if (model_map_.find(1) == model_map_.end())
  {
    LOG_ERROR("lidar index {} not exist in list, please contact developer", 1);
    return;
  }
  getWorkModel()->getLidarManager()->startMotor();
}
void AppController::slotButtonRwRegFromFileClicked()
{
  // 打开一个文件选择对话框，选择一个文件
  QString file_name = QFileDialog::getOpenFileName(main_window_, "选择写入的bit文件", QDir::currentPath());
  if (file_name.isEmpty())
  {
    return;
  }

  std::vector<uint32_t> reg_addr_vec;
  std::vector<uint32_t> reg_val_vec;
  // 每一行根据下划线_分割，如果大小小于4，报错并返回
  QFile file(file_name);
  if (!file.open(QIODevice::ReadOnly | QIODevice::Text))
  {
    LOG_ERROR("open file failed: {}", file_name.toStdString());
    return;
  }
  QTextStream file_in(&file);

  // 第3列为地址，第4列为值，均为0x开头的16进制数
  while (!file_in.atEnd())
  {
    QString line     = file_in.readLine();
    QStringList list = line.split("_");
    if (list.size() < 4)
    {
      LOG_ERROR("line format error: {}", line.toStdString());
      return;
    }
    bool is_ok = false;
    reg_addr_vec.push_back(list[2].toUInt(&is_ok, 16));
    if (!is_ok)
    {
      LOG_ERROR("line format error: {}", line.toStdString());
      return;
    }
    reg_val_vec.push_back(list[3].toUInt(&is_ok, 16));
    if (!is_ok)
    {
      LOG_ERROR("line format error: {}", line.toStdString());
      return;
    }
  }

  auto lidar_manager = getWorkModel()->getLidarManager();
  for (size_t i = 0; i < reg_addr_vec.size(); ++i)
  {
    lidar_manager->writeRegData(reg_addr_vec[i], reg_val_vec[i]);
  }
  for (size_t i = 0; i < reg_addr_vec.size(); ++i)
  {
    uint32_t reg_val = 0;
    lidar_manager->readRegData(reg_addr_vec[i], reg_val);
    if (reg_val != reg_val_vec[i])
    {
      LOG_ERROR("reg_addr: 0x{:x}, write val: 0x{:x}, read val: 0x{:x}", reg_addr_vec[i], reg_val_vec[i], reg_val);
    }
  }
}

void AppController::slotButtonZeroSetClicked()
{
  auto angle = ui_->lineedit_zero_set->text().toFloat();
  getWorkModel()->getLidarManager()->writeZeroAngle(angle);

  LOG_INFO(fmt::format("zero angle write : {}", angle));
}

void AppController::slotButtonZeroSelfTestClicked()
{
  auto file_name = ui_->lineedit_path_zero->text();
  if (file_name.isEmpty())
  {
    LOG_ERROR("文件为空");
    return;
  }

  QDir dir(file_name);
  QString current_dir = dir.absolutePath();
  current_dir         = current_dir.left(file_name.lastIndexOf("/"));
  current_dir += "/";

  {
    ScopedTimer timer("load pcap file");
    if (!getWorkModel()->loadPcapZero(file_name, true))
    {
      LOG_ERROR("load pcap file failed: {}", file_name.toStdString());
      return;
    }
  }

  {
    ScopedTimer timer("zeroProcess");
    if (!getWorkModel()->processZeroTest())
    {
      LOG_ERROR("zeroProcess failed: {}", file_name.toStdString());
      return;
    }
  }
}

void AppController::slotButtonZeroCalibClicked()
{
  auto file_name = ui_->lineedit_path_zero->text();
  if (file_name.isEmpty())
  {
    LOG_ERROR("文件为空");
    return;
  }

  QDir dir(file_name);
  QString current_dir = dir.absolutePath();
  current_dir         = current_dir.left(file_name.lastIndexOf("/"));
  current_dir += "/";

  {
    ScopedTimer timer("load pcap file");
    if (!getWorkModel()->loadPcapZero(file_name))
    {
      LOG_ERROR("load pcap file failed: {}", file_name.toStdString());
      return;
    }
  }

  {
    ScopedTimer timer("zeroProcess");
    if (!getWorkModel()->processZero())
    {
      LOG_ERROR("zeroProcess failed: {}", file_name.toStdString());
      return;
    }
  }
}
void AppController::slotButtonWriteTopFlashClicked()
{
  if (model_map_.find(1) == model_map_.end())
  {
    LOG_ERROR("lidar index {} not exist in list, please contact developer", 1);
    return;
  }

  QString file_name = QFileDialog::getOpenFileName(main_window_, "Open File", QDir::currentPath(), "bit files (*.bit)");
  if (file_name.isEmpty())
  {
    return;
  }

  bool is_ok          = false;
  uint32_t start_addr = ui_->lineedit_top_flash_start_addr->text().toUInt(&is_ok, 16);
  if (!is_ok)
  {
    LOG_ERROR("start addr format error: {}", ui_->lineedit_top_flash_start_addr->text().toStdString());
    return;
  }

  uint32_t end_addr = ui_->lineedit_top_flash_end_addr->text().toUInt(&is_ok, 16);
  if (!is_ok)
  {
    LOG_ERROR("end addr format error: {}", ui_->lineedit_top_flash_end_addr->text().toStdString());
    return;
  }

  // 创建进度对话框
  QProgressDialog* progress = new QProgressDialog("Writing top flash...", "取消", 0, 100, main_window_);
  progress->setWindowModality(Qt::WindowModal);
  progress->setAutoClose(true);
  progress->setValue(0);
  // connect(progress, &QProgressDialog::canceled, this,
  //         [this]() { getWorkModel()->getLidarManager()->abortWritingFlash(); });
  connect(app(), &AppEvent::signalUpdateProgressWriteTopFlash, progress, &QProgressDialog::setValue,
          Qt::QueuedConnection);

  // if (getWorkModel()->getLidarManager()->writeTopFlash(file_name, start_addr))
  // {
  //   LOG_INFO("写入顶板flash成功!");
  // }
  // else
  // {
  //   LOG_ERROR("写入顶板flash失败!");
  // };

  progress->close();
  progress->deleteLater();
}

void AppController::slotButtonCustomProcessClicked()
{
  QString file_name = ui_->lineedit_process_file_path->text();
  QDir dir(file_name);
  QString current_dir = dir.absolutePath();
  current_dir         = current_dir.left(file_name.lastIndexOf("/"));
  current_dir += "/";

  QMetaObject::invokeMethod(ui_->button_custom_process, "setEnabled", Qt::QueuedConnection, Q_ARG(bool, false));
  {
    ScopedTimer timer("load pcap file");
    if (!getWorkModel()->loadPcapFile(file_name))
    {
      LOG_ERROR("load pcap file failed: {}", file_name.toStdString());
      QMetaObject::invokeMethod(ui_->button_custom_process, "setEnabled", Q_ARG(bool, true));
      return;
    }
  }

  {
    ScopedTimer timer("auto process all data");
    if (!getWorkModel()->autoProcessAllData())
    {
      LOG_ERROR("处理数据过程中出现错误");
    }
  }
  QMetaObject::invokeMethod(ui_->button_custom_process, "setEnabled", Qt::QueuedConnection, Q_ARG(bool, true));
}
void AppController::slotButtonInitLidarClicked()
{
  if (!getWorkModel()->initWaveCalib())
  {
    LOG_ERROR("write csv data failed");
    return;
  }
}
void AppController::slotButtonWriteChnAngle()
{
  if (!getWorkModel()->writeChnAngle())
  {
    // LOG_ERROR("write chn angle failed");
    app()->signalShowErrorText("写入通道角失败");
    return;
  }
}
void AppController::slotButtonOneKeyCollectClicked()
{
  auto check_all_state = fsm_->getStateHandler(ActionState::STATE_CHECK_ALL_STATE);
  auto connect_lidar   = fsm_->getStateHandler(ActionState::STATE_CONNECT_LIDAR);
  auto init_lidar_wave = fsm_->getStateHandler(ActionState::STATE_INIT_LIDAR_WAVE);
  auto reset_motor     = fsm_->getStateHandler(ActionState::STATE_RESET_MOTOR);
  auto collect_data    = fsm_->getStateHandler(ActionState::STATE_COLLECT_DATA);

  updateModelParaInfo();

  QMetaObject::invokeMethod(ui_->button_one_key_collect, "setEnabled", Qt::QueuedConnection, Q_ARG(bool, false));
  auto one_key_collect = [&]() {
    if (connect_lidar->handleState() == ActionState::STATE_FAIL)
    {
      LOG_ERROR("connect lidar failed");
      return false;
    }
    if (check_all_state->handleState() == ActionState::STATE_END ||
        check_all_state->handleState() == ActionState::STATE_FAIL)
    {
      LOG_ERROR("check all state failed");
      return false;
    }
    if (init_lidar_wave->handleState() == ActionState::STATE_FAIL)
    {
      LOG_ERROR("init lidar wave failed");
      return false;
    }

    if (reset_motor->handleState() == ActionState::STATE_FAIL)
    {
      LOG_ERROR("reset motor failed");
      return false;
    }

    if (collect_data->handleState() == ActionState::STATE_FAIL)
    {
      LOG_ERROR("collect data failed");
      return false;
    }
    return true;
  };

  if (one_key_collect())
  {
    app()->signalUpdateTestState(TestState::PASS);
  }
  else
  {
    app()->signalUpdateTestState(TestState::FAILED);
  }
  QMetaObject::invokeMethod(ui_->button_one_key_collect, "setEnabled", Qt::QueuedConnection, Q_ARG(bool, true));
}
void AppController::slotButtonOneKeyProcessClicked()
{
  // 创建进度对话框
  QProgressDialog* progress = new QProgressDialog("Writing top flash...", "取消", 0, 100, main_window_);
  progress->setWindowModality(Qt::WindowModal);
  progress->setAutoClose(true);
  progress->setValue(0);

  // 连接 QFutureWatcher 和进度对话框的信号槽
  connect(progress, &QProgressDialog::canceled, this, [this]() { getWorkModel()->getLidarManager()->abort(); });

  // 连接信号以更新进度对话框的进度值
  connect(AppEvent::getInstance(), &AppEvent::signalUpdateProgressWriteTopFlash, progress, &QProgressDialog::setValue,
          Qt::QueuedConnection);

  getWorkModel()->loadPcapFile();
  if (!getWorkModel()->autoProcessAllData())
  {
    LOG_ERROR("处理数据过程中出现错误");
  }
  progress->show();

  if (!getWorkModel()->writeBitToLidar())
  {
    LOG_ERROR("写入bit文件失败");
  }
  else
  {
    LOG_INFO("写入bit文件成功");
  }

  progress->close();
  progress->deleteLater();
}
void AppController::slotButtonWriteBitToLidarClicked()
{
  // 创建进度对话框
  QProgressDialog* progress = new QProgressDialog("Writing top flash...", "取消", 0, 100, main_window_);
  progress->setWindowModality(Qt::WindowModal);
  progress->setAutoClose(true);
  progress->setValue(0);

  connect(progress, &QProgressDialog::canceled, this, [this]() { getWorkModel()->getLidarManager()->abort(); });

  // 连接信号以更新进度对话框的进度值
  connect(AppEvent::getInstance(), &AppEvent::signalUpdateProgressWriteTopFlash, progress, &QProgressDialog::setValue,
          Qt::QueuedConnection);

  if (!getWorkModel()->writeBitToLidar())
  {
    LOG_ERROR("写入bit文件失败");
  }
  else
  {
    LOG_INFO("写入bit文件成功");
  }

  progress->close();
  progress->deleteLater();
}

void AppController::slotButtonAutoSetSnClicked() { getWorkModel()->connectLidar(); }

void AppController::slotButtonRotateSetupLidarClicked()
{
  extDevice()->setRotatorXSpeed(ui_->spinbox_rotate_default_speed->value());
  extDevice()->setRotatorXAngle(ui_->spinbox_rotate_setup_lidar->value());
}
void AppController::slotButtonRotateLightSpotClicked()
{
  extDevice()->setRotatorXSpeed(ui_->spinbox_rotate_default_speed->value());
  extDevice()->setRotatorXAngle(ui_->spinbox_rotate_light_spot->value());
}

void AppController::slotButtonDisplayProcessDataClicked()
{
  int chn_num = ui_->combobox_display_chn->currentText().toInt();

  if (ui_->lineedit_data_path->text().isEmpty())
  {
    LOG_ERROR("处理数据路径为空，无法生成图片");
  }

  // QMetaObject::invokeMethod(ui_->button_display_process_data, "setEnabled", Qt::QueuedConnection, Q_ARG(bool, false));

  // 在线程中创建 QEventLoop
  QEventLoop event_loop;
  // 将任务放到一个 std::thread 中运行
  std::thread process_thead([this, &event_loop, chn_num]() {
    bool result = false;
    // 判断是否为zero类型
    if (ui_->combobox_display_type->currentText() == "zero")
    {
      result =
        getWorkModel()->showZeroData(ui_->lineedit_data_path->text(), ui_->checkbox_process_show_window->isChecked(),
                                     ui_->checkbox_process_save_image->isChecked());
    }
    else
    {
      result =
        getWorkModel()->showData(chn_num, ui_->spinbox_process_num->value(), ui_->combobox_display_type->currentText(),
                                 ui_->lineedit_data_path->text(), ui_->checkbox_process_show_window->isChecked(),
                                 ui_->checkbox_process_save_image->isChecked());
    }

    if (!result)
    {
      LOG_ERROR("display process data failed");
    }

    // 退出事件循环
    QMetaObject::invokeMethod(&event_loop, "quit", Qt::QueuedConnection);
  });  // 分离线程

  event_loop.exec();
  process_thead.join();

  // QMetaObject::invokeMethod(ui_->button_display_process_data, "setEnabled", Qt::QueuedConnection, Q_ARG(bool, true));
}
void AppController::slotButtonCompareClicked()
{

  QString bit1_path = ui_->lineedit_bit1->text();

  QString bit2_path = ui_->lineedit_bit2->text();

  std::vector<uint8_t> data1;
  QFile file1(bit1_path);
  if (!file1.open(QIODevice::ReadOnly))
  {
    LOG_ERROR("open file failed: {}", bit1_path.toStdString());
    return;
  }
  data1.resize(file1.size());
  file1.read(reinterpret_cast<char*>(data1.data()), file1.size());
  file1.close();
  if (data1.size() != sizeof(mech::CombineBit))
  {
    LOG_ERROR("file size error: {}", bit1_path.toStdString());
    return;
  }
  mech::CombineBit miles_bit;
  std::memcpy(&miles_bit, data1.data(), sizeof(mech::CombineBit));
  miles_bit.toBigEndian();

  std::vector<uint8_t> data2;
  QFile file2(bit2_path);
  if (!file2.open(QIODevice::ReadOnly))
  {
    LOG_ERROR("open file failed: {}", bit2_path.toStdString());
    return;
  }
  data2.resize(file2.size());
  file2.read(reinterpret_cast<char*>(data2.data()), file2.size());
  file2.close();
  if (data2.size() != sizeof(mech::CombineBit))
  {
    LOG_ERROR("file size error: {}", bit2_path.toStdString());
    return;
  }
  mech::CombineBit david_bit {};
  std::memcpy(&david_bit, data2.data(), sizeof(mech::CombineBit));
  david_bit.toBigEndian();
  // david_bit.dynamic_bit = miles_bit.dynamic_bit;

  // write file
  // QString save_path = root_path + "data/data/30/result/1109bddc0030_miles_dynamic_combine_calib_20241101_192338.bit";
  // if (!getWorkModel()->saveFile(save_path, david_bit.arr.data(), sizeof(david_bit)))
  // {
  //   LOG_ERROR("save file failed: {}", save_path.toStdString());
  //   return;
  // }

  // 对比动标
  for (size_t i = 0; i < miles_bit.dynamic_bit.comp.size(); i++)
  {
    int channel_num      = static_cast<int>(i) + 1;
    auto& comp1          = miles_bit.dynamic_bit.comp.at(i);
    auto& comp2          = david_bit.dynamic_bit.comp.at(i);
    auto dynamic_calib_x = mech::getDynamicCalibAreaArray();
    std::vector<int> dynamic_x_vec(dynamic_calib_x.begin(), dynamic_calib_x.end());
    std::vector<int> dynamic_y_vec1(comp1.dist_val_arr.size());
    std::vector<int> dynamic_y_vec2(comp2.dist_val_arr.size());

    std::transform(comp1.dist_val_arr.begin(), comp1.dist_val_arr.end(), dynamic_y_vec1.begin(),
                   [](auto& _val) { return _val; });
    std::transform(comp2.dist_val_arr.begin(), comp2.dist_val_arr.end(), dynamic_y_vec2.begin(),
                   [](auto& _val) { return _val; });
    plt::scatter(dynamic_x_vec, dynamic_y_vec1, 1, { { "label", "miles" } });
    plt::scatter(dynamic_x_vec, dynamic_y_vec2, 1, { { "label", "david" } });
    plt::xlabel("area");
    plt::ylabel("dist");
    plt::legend();
    plt::title(fmt::format("chn {} dynamic", channel_num));
    plt::show();
  }

  // 对比静标
  std::vector<int> static_x_vec(96);
  std::iota(static_x_vec.begin(), static_x_vec.end(), 1);

  std::vector<int> static_y_diff(96);

  for (size_t i = 0; i < miles_bit.static_bit.comp.size(); i++)
  {
    auto& comp1         = miles_bit.static_bit.comp.at(i);
    auto& comp2         = david_bit.static_bit.comp.at(i);
    static_y_diff.at(i) = comp1.code2_dist - comp2.code2_dist;
  }
  // plt::scatter(static_x_vec, static_y_vec1, 1, { { "label", "static1" } });
  // plt::scatter(static_x_vec, static_y_vec2, 1, { { "label", "static2" } });
  plt::plot(static_x_vec, static_y_diff, { { "label", "static_diff" } });
  plt::xlabel("channel");
  plt::ylabel("dist");
  plt::legend();
  plt::title("static");
  plt::show();

  // 对比反标， 幅值反标
  std::vector<float> refl_x_vec(mech::REFL_CALIB_DIST.begin(), mech::REFL_CALIB_DIST.end());
  for (auto& dist : refl_x_vec)
  {
    dist = dist * 5 / 1000;
  }

  std::vector<int> refl_vec = { 10, 40, 90, 255 };

  for (int channel_num = 1; channel_num <= 96; channel_num++)
  {
    plt::subplot(1, 2, 1);
    auto& comp1 = miles_bit.refl_bit.comp_chn.at(channel_num - 1);
    auto& comp2 = david_bit.refl_bit.comp_chn.at(channel_num - 1);
    for (auto refl : refl_vec)
    {
      std::vector<float> area_y_vec1(comp1.charge.at(0).dist_comp.size());
      std::vector<float> area_y_vec2(comp2.charge.at(0).dist_comp.size());
      for (size_t i = 0; i < refl_x_vec.size(); ++i)
      {
        area_y_vec1.at(i) = comp1.charge.at(0).dist_comp.at(i).getReflChargeComp(refl);
        area_y_vec2.at(i) = comp2.charge.at(0).dist_comp.at(i).getReflChargeComp(refl);
      }
      plt::plot(refl_x_vec, area_y_vec1, { { "label", "area1" } });
      plt::plot(refl_x_vec, area_y_vec2, { { "label", "area2" } });
    }
    plt::xlabel("dist");
    plt::ylabel("area");
    plt::legend();
    plt::title(fmt::format("refl area chn {}", channel_num));

    plt::subplot(1, 2, 2);
    for (auto refl : refl_vec)
    {
      std::vector<float> amp_y_vec1(comp1.charge.at(0).dist_comp.size());
      std::vector<float> amp_y_vec2(comp2.charge.at(0).dist_comp.size());
      for (size_t i = 0; i < refl_x_vec.size(); ++i)
      {
        amp_y_vec1.at(i) = comp1.charge.at(1).dist_comp.at(i).getReflChargeComp(refl);
        amp_y_vec2.at(i) = comp2.charge.at(1).dist_comp.at(i).getReflChargeComp(refl);
      }
      plt::plot(refl_x_vec, amp_y_vec1, { { "label", "amp1" } });
      plt::plot(refl_x_vec, amp_y_vec2, { { "label", "amp2" } });
    }
    plt::xlabel("dist");
    plt::ylabel("amp");
    plt::legend();
    plt::title(fmt::format("refl amp chn {}", channel_num));
    plt::show();
  }
}

void AppController::slotButtonEyeSafeClicked()
{
  if (!getWorkModel()->getLidarManager()->setEyesSafe(0))
  {
    LOG_ERROR("close eye safe failed");
    return;
  }
  LOG_INFO("关闭人眼安全成功");
}

void AppController::slotButtonProcessClicked()
{
  getWorkModel()->loadPcapFile();
  if (!getWorkModel()->autoProcessAllData())
  {
    LOG_ERROR("处理数据过程中出现错误");
  }
}

void AppController::slotVbdLoadFile()
{
  QString file_path = ui_->lineedit_vbd_file_path->text();
  if (file_path.isEmpty())
  {
    LOG_ERROR("vbd file path is empty");
    return;
  }
  app()->loadVbdFile(file_path);
}

bool AppController::slotButtonLoadVbdClicked()
{
  double vbd = 0;
  if (!app()->isVbdFileLoaded())
  {
    if (!app()->loadVbdFile(ui_->lineedit_vbd_file_path->text()))
    {
      return false;
    }
  }
  if (!getWorkModel()->scanConnectLidarAndWaitForTop())
  {
    return false;
  }
  auto lidar_sn = getWorkModel()->getLidarManager()->getLidarInfo()->getLidarSN();
  QMetaObject::invokeMethod(ui_->lineedit_vbd_sn, "setText", Qt::BlockingQueuedConnection, Q_ARG(QString, lidar_sn));

  if (!app()->loadVbdBySn(lidar_sn, vbd))
  {
    return false;
  }
  LOG_INFO("vbd: {}", vbd);
  QMetaObject::invokeMethod(ui_->spinbox_vbd, "setValue", Qt::BlockingQueuedConnection, Q_ARG(double, vbd));
  std::this_thread::sleep_for(std::chrono::milliseconds(1000));
  return true;
}
bool AppController::slotButtonVbdLoadAndWriteClicked()
{
  if (!slotButtonLoadVbdClicked())
  {
    return false;
  }
  if (!slotButtonVbdReadCurveClicked())
  {
    return false;
  }
  if (!slotButtonVbdCalculateInterceptClicked())
  {
    return false;
  }
  if (!slotButtonVbdWriteFlashClicked())
  {
    return false;
  }
  return true;
}
bool AppController::slotButtonVbdWriteFlashClicked()
{
  uint32_t vbd_intercept_hex = ui_->spinbox_vbd_intercept_hex->value();
  uint32_t vbd_err_hex       = ui_->spinbox_vbd_err_hex->value();
  if (!getWorkModel()->writeVbdGdi(vbd_intercept_hex, vbd_err_hex))
  {
    LOG_ERROR("写入vbd失败");
    return false;
  }
  LOG_INFO("写入vbd成功");
  // 保存vbd数据到文件中
  QString file_path = ui_->lineedit_vbd_save_dir->text() + QString("/%1.csv").arg(ui_->lineedit_vbd_sn->text());
  QFile file(file_path);
  if (!file.open(QIODevice::WriteOnly))
  {
    LOG_ERROR("打开文件失败，err_msg: {}，文件路径：{}", file.errorString(), file_path.toStdString());
    return false;
  }
  QTextStream out(&file);
  out << "vbd,vbd_err_float,vbd_err_hex,vbd_intercept,vbd_intercept_hex,vbd_v0,vbd_v1,vbd_v2" << endl;
  out << fmt::format("{},{},{:#x},{},{:#x},{:#x},{:#x},{:#x}", ui_->spinbox_vbd->value(), ui_->spinbox_vbd_err->value(),
                     ui_->spinbox_vbd_err_hex->value(), ui_->spinbox_vbd_intercept->value(),
                     ui_->spinbox_vbd_intercept_hex->value(), ui_->spinbox_vbd_v0->value(),
                     ui_->spinbox_vbd_v1->value(), ui_->spinbox_vbd_v2->value())
           .c_str()
      << endl;
  file.close();
  LOG_INFO("保存vbd数据成功");
  return true;
}
bool AppController::slotButtonVbdReadClicked()
{
  if (!getWorkModel()->getLidarManager()->connect())
  {
    LOG_ERROR("连接失败");
    return false;
  }
  uint32_t vbd_intercept_hex = 0;
  uint32_t vbd_err_hex       = 0;
  if (!getWorkModel()->readVbd(vbd_intercept_hex, vbd_err_hex))
  {
    LOG_ERROR("读取vbd失败");
    return false;
  }
  LOG_INFO("vbd_intercept_hex: {:#x}, vbd_err_hex: {:#x}", vbd_intercept_hex, vbd_err_hex);
  QMetaObject::invokeMethod(ui_->spinbox_vbd_intercept_read, "setValue", Qt::BlockingQueuedConnection,
                            Q_ARG(int, vbd_intercept_hex));
  QMetaObject::invokeMethod(ui_->spinbox_vbd_err_read, "setValue", Qt::BlockingQueuedConnection,
                            Q_ARG(int, vbd_err_hex));

  uint32_t calib_status = 0;
  if (!getWorkModel()->readTopRegDataByKey("calib_status", calib_status, 1))
  {
    LOG_ERROR("读取calib_status失败");
    return false;
  }
  // bit0 反标, bit1 动标, bit2 静标, bit3 近距离, bit4 gdi, bit5 绝标
  // LOG_INFO("calib_status: {:#x}", calib_status);
  LOG_INFO("CRC 反标: {}, 动标: {}, 静标: {}, 近距离: {}, GDI: {}, 绝标: {}", calib_status & 0x01U,
           (calib_status >> 1U) & 0x01U, (calib_status >> 2U) & 0x01U, (calib_status >> 3U) & 0x01U,
           (calib_status >> 4U) & 0x01U, (calib_status >> 5U) & 0x01U);
  return true;
}
bool AppController::slotButtonVbdReadCurveClicked()
{
  if (!getWorkModel()->getLidarManager()->connect())
  {
    LOG_ERROR("连接失败");
    return false;
  }

  uint16_t vbd_v0 = 0;
  uint16_t vbd_v1 = 0;
  uint16_t vbd_v2 = 0;
  if (!getWorkModel()->readVbdCurve(vbd_v0, vbd_v1, vbd_v2))
  {
    LOG_ERROR("读取vbd曲线失败");
    return false;
  }
  LOG_INFO("v0: {:#x}, v1: {:#x}, v2: {:#x}", vbd_v0, vbd_v1, vbd_v2);
  QMetaObject::invokeMethod(ui_->spinbox_vbd_v0, "setValue", Qt::BlockingQueuedConnection,
                            Q_ARG(int, static_cast<int>(vbd_v0)));
  QMetaObject::invokeMethod(ui_->spinbox_vbd_v1, "setValue", Qt::BlockingQueuedConnection,
                            Q_ARG(int, static_cast<int>(vbd_v1)));
  QMetaObject::invokeMethod(ui_->spinbox_vbd_v2, "setValue", Qt::BlockingQueuedConnection,
                            Q_ARG(int, static_cast<int>(vbd_v2)));

  std::this_thread::sleep_for(std::chrono::milliseconds(10));
  return true;
}
bool AppController::slotButtonVbdCalculateInterceptClicked()
{

  double vbd_err = ui_->spinbox_vbd->value() - (-20.51802);
  LOG_INFO("vbd_err: {}", vbd_err);
  double vbd_err_trans       = std::clamp(vbd_err, -1.0, 1.0 - (1.0 / 32768.0F));
  int16_t vbd_err_trans_int  = static_cast<int16_t>(std::round(vbd_err_trans * 32768.0));
  uint16_t vbd_err_trans_hex = static_cast<uint16_t>(vbd_err_trans_int);
  LOG_INFO("vbd_err_trans_int: {}", vbd_err_trans_int);
  QMetaObject::invokeMethod(ui_->spinbox_vbd_err_hex, "setValue", Qt::BlockingQueuedConnection,
                            Q_ARG(int, vbd_err_trans_hex));
  QMetaObject::invokeMethod(ui_->spinbox_vbd_err, "setValue", Qt::BlockingQueuedConnection, Q_ARG(double, vbd_err));
  QMetaObject::invokeMethod(ui_->spinbox_vbd_err_hex, "setValue", Qt::BlockingQueuedConnection,
                            Q_ARG(int, vbd_err_trans_hex));

  float vbd_v0_float = bcdToFloat(ui_->spinbox_vbd_v0->value());
  float vbd_v1_float = bcdToFloat(ui_->spinbox_vbd_v1->value());
  float vbd_v2_float = bcdToFloat(ui_->spinbox_vbd_v2->value());
  LOG_INFO("v0: {}, v1: {}, v2: {}", vbd_v0_float, vbd_v1_float, vbd_v2_float);

  double v3_50 = ((vbd_v1_float - vbd_v0_float) / 125 * 90) + vbd_v0_float;

  auto v_t = -v3_50 - 0.36F - vbd_err;

  auto vbd_intercept = ((-8.12 - v_t) * 1000 / 3.3 / 5.4545) - (ui_->spin_box_vbd_cal_k->value() * 90);

  auto vbd_intercept_hex = static_cast<int>(std::round(vbd_intercept * 256));

  LOG_INDEX_INFO("vbd_intercept: {}, vbd_intercept_hex: {:#x}", vbd_intercept, vbd_intercept_hex);
  QMetaObject::invokeMethod(ui_->spinbox_vbd_intercept, "setValue", Qt::BlockingQueuedConnection,
                            Q_ARG(double, vbd_intercept));
  QMetaObject::invokeMethod(ui_->spinbox_vbd_intercept_hex, "setValue", Qt::BlockingQueuedConnection,
                            Q_ARG(int, vbd_intercept_hex));
  std::this_thread::sleep_for(std::chrono::milliseconds(10));
  return true;
}

}  // namespace lidar
}  // namespace robosense