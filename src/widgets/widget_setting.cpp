﻿/******************************************************************************
 * Copyright 2020 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#include "widget_setting.h"
#include "app_event.h"
// #include "bocic_rotator_controller/bocic_rotator.h"
#include "config.h"
#include "parameter_setting.h"
// #include "rotator_controller_factory.h"
#include "rsfsc_log/rsfsc_log.h"
#include <QMessageBox>
#include <QSettings>

namespace robosense  // NOLINT(modernize-concat-nested-namespaces)
{
namespace lidar
{

WidgetSetting::WidgetSetting(QWidget* _parent, bool _is_use_password) :
  QDialog(_parent),
  widget_content_(new QWidget(this)),
  layout_main_(new QVBoxLayout),
  parameter_setting_(nullptr),
  user_authority_(nullptr)
{
  QHBoxLayout* layout_admin = new QHBoxLayout;

  setupSettingWidget();
  readLayout();

  QVBoxLayout* layout_setting = new QVBoxLayout;
  widget_content_->setLayout(layout_setting);

  layout_main_->addLayout(layout_admin);
  layout_main_->addWidget(widget_content_);

  this->setLayout(layout_main_);
  this->setWindowTitle("标定参数设置");

  if (_is_use_password)
  {
    lineedit_admin_password_ = new QLineEdit(this);
    layout_admin->addWidget(new QLabel("管理员密码:", this));
    lineedit_admin_password_->setEchoMode(QLineEdit::Password);
    lineedit_admin_password_->setToolTip("测试密码为123");
    layout_admin->addWidget(lineedit_admin_password_);

    pushbutton_admin_switch_ = new QPushButton("登录", this);
    pushbutton_admin_switch_->setFocusPolicy(Qt::NoFocus);
    layout_admin->addWidget(pushbutton_admin_switch_);
    QObject::connect(pushbutton_admin_switch_, &QPushButton::clicked, this, [this]() {
      if (lineedit_admin_password_->text() == "123")
      {
        parameter_setting_->setParaTreeEnable(true);
        pushbutton_save_param_->setEnabled(true);
      }
      else
      {
        parameter_setting_->setParaTreeEnable(false);
        pushbutton_save_param_->setEnabled(false);
        QMessageBox::warning(this, "错误", "密码错误");
      }
    });
    parameter_setting_->setParaTreeEnable(false);
    pushbutton_save_param_->setEnabled(false);
  }
  else
  {
    auto* spacer = new QSpacerItem(0, 0, QSizePolicy::Expanding, QSizePolicy::Minimum);
    layout_admin->addSpacerItem(spacer);
  }
  pushbutton_save_param_ = new QPushButton("保存参数", this);
  pushbutton_save_param_->setFocusPolicy(Qt::NoFocus);
  layout_admin->addWidget(pushbutton_save_param_);

  QObject::connect(pushbutton_save_param_, &QPushButton::clicked, AppEvent::getInstance(), &AppEvent::signalSaveParam);
  QObject::connect(pushbutton_save_param_, &QPushButton::clicked, this, &WidgetSetting::slotWriteSetting);
  QObject::connect(this, &WidgetSetting::signalSaveParamFinished, AppEvent::getInstance(),
                   &AppEvent::signalSaveParamFinished);

  this->setWindowFlags(this->windowFlags() & ~Qt::WindowMinMaxButtonsHint);
}

WidgetSetting::~WidgetSetting() {}

void WidgetSetting::show()
{
  if (parameter_setting_ != nullptr)
  {
    parameter_setting_->loadPara();
  }
  if (isVisible())
  {
    raise();
    activateWindow();
    return;
  }
  QDialog::show();
}

void WidgetSetting::slotUpdateAllWidgetState(robosense::lidar::rsfsc_lib::UserAuthority* _user_authority)
{
  if (_user_authority != nullptr)
  {
    user_authority_ = _user_authority;
  }

  bool is_technician_can_change =
    user_authority_->isNotLessThan(robosense::lidar::rsfsc_lib::UserAuthority::Level::LEVEL_TECHNICIAN);

  bool is_admin = is_technician_can_change;

  this->setEnabled(is_admin);
}

void WidgetSetting::setupSettingWidget()
{
  parameter_setting_ = new ParameterSetting(this);

  parameter_setting_->registerIntPara(
    { "lidar_para", "lidar_num", "雷达参数", "雷达数量", "设置雷达数量", 1, 1, 16, 1 });
  ParaStringInfo para_info;
  para_info.group             = "rotator_para";
  para_info.group_display_str = "转台参数";
  para_info.key               = "port_name";
  para_info.key_display_str   = "串口";
  para_info.default_val       = "ttyUSB0";
  parameter_setting_->registerStringPara(para_info);

  ParaOptionInfo para_option_info;
  para_option_info.group             = "rotator_para";
  para_option_info.group_display_str = "转台参数";
  para_option_info.key               = "rotator_ctrl_type";
  para_option_info.key_display_str   = "控制器类型";
  para_option_info.default_val       = 2;
  int count                          = 0;
  // for (const auto& type_name : RotatorControllerFactory::ROTATOR_CONTROLLER_TYPE_NAME)
  // {
  //   para_option_info.para_list << QString::fromStdString(type_name);
  //   para_option_info.para_val_list << count++;
  // }
  // parameter_setting_->registerOptionPara(para_option_info);

  // para_option_info.key             = "rotator_motor_type";
  // para_option_info.key_display_str = "电机类型";
  // para_option_info.default_val     = 1;
  // para_option_info.para_list.clear();
  // para_option_info.para_val_list.clear();
  // for (const auto& model_name : BocicRotator::BOCIC_ROTATOR_MODEL_NAME)
  // {
  //   para_option_info.para_list << QString::fromStdString(model_name);
  //   para_option_info.para_val_list << QString::fromStdString(model_name);
  // }
  parameter_setting_->registerOptionPara(para_option_info);

  ParaIntInfo para_int_info;
  para_int_info.group             = "rotator_para";
  para_int_info.group_display_str = "转台参数";
  para_int_info.key               = "rotator_speed";
  para_int_info.key_display_str   = "电机转速";
  para_int_info.default_val       = 5000;
  para_int_info.min_val           = 0;
  para_int_info.max_val           = 200000;
  para_int_info.step              = 1;
  parameter_setting_->registerIntPara(para_int_info);

  para_int_info.group           = "collect_para";
  para_info.group_display_str   = "采集参数";
  para_int_info.key             = "collect_data_count";
  para_int_info.key_display_str = "采集次数";
  para_int_info.default_val     = 50;
  para_int_info.min_val         = 1;
  para_int_info.max_val         = 50000;
  para_int_info.step            = 1;
  parameter_setting_->registerIntPara(para_int_info);

  para_int_info.key             = "collect_channel_count";
  para_int_info.key_display_str = "通道数量";
  para_int_info.default_val     = 48;
  para_int_info.min_val         = 1;
  para_int_info.max_val         = 48;
  para_int_info.step            = 1;
  parameter_setting_->registerIntPara(para_int_info);

  ParaStringInfo para_string_info;
  para_string_info.group             = "collect_para";
  para_string_info.group_display_str = "采集参数";
  para_string_info.key               = "collect_gain_list";
  para_string_info.key_display_str   = "增益充能列表";
  para_string_info.default_val       = "1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16";
  parameter_setting_->registerStringPara(para_string_info);

  parameter_setting_->loadPara();
  parameter_setting_->showParaTree();
  layout_main_->addWidget(parameter_setting_);
}

void WidgetSetting::slotWriteSetting()
{
  if (parameter_setting_ == nullptr)
  {
    return;
  }
  // rsfsc_lib::RSFSCQSettings settings("RoboSense", setting_file_name_);
  if (parameter_setting_->savePara())
  {
    parameter_setting_->loadPara();
    writeLayout();
    signalSaveParamFinished();
    QMessageBox::information(this, "提示", "参数保存成功");
  }
  else
  {
    QMessageBox::warning(this, "错误", "参数保存失败");
  }
}

bool WidgetSetting::getPara(const QString& _para_key, QVariant& _para_value)
{
  return parameter_setting_->getPara(_para_key, _para_value);
}

void WidgetSetting::closeEvent(QCloseEvent* _event)
{
  writeLayout();
  QDialog::closeEvent(_event);
}

void WidgetSetting::writeLayout()
{
  QString suffix(PROJECT_NAME);
  suffix += QString::fromUtf8("/widget_setting");
  QSettings settings(QSettings::IniFormat, QSettings::UserScope, "RoboSense", suffix);
  settings.setValue("geometry", saveGeometry());
}

void WidgetSetting::readLayout()
{
  QString suffix(PROJECT_NAME);
  suffix += QString::fromUtf8("/widget_setting");
  QSettings settings(QSettings::IniFormat, QSettings::UserScope, "RoboSense", suffix);
  QByteArray saved_geometry = settings.value("geometry").toByteArray();
  if (saved_geometry.isEmpty())
  {
    RSFSCLog::getInstance()->debug("No saved geometry found." + settings.fileName().toStdString());
    return;
  }

  RSFSCLog::getInstance()->debug("Restoring geometry: ");
  bool restored = restoreGeometry(saved_geometry);
  if (!restored)
  {
    RSFSCLog::getInstance()->warn("Failed to restore geometry.");
  }
}

bool WidgetSetting::makeSureChange(const QString& _change_str)
{
  QString dlg_title = "Question消息框";

  const QString& str_info = _change_str;

  QMessageBox::StandardButton default_btn = QMessageBox::NoButton;  //缺省按钮

  QMessageBox::StandardButton result = QMessageBox::NoButton;  // initialize result to NoButton
  result = QMessageBox::question(this, dlg_title, str_info, QMessageBox::Yes | QMessageBox::No, default_btn);

  return result == QMessageBox::Yes;
}

}  // namespace lidar
}  // namespace robosense
