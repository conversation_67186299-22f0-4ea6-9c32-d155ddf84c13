﻿/******************************************************************************
 * Copyright 2020 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#ifndef WIDGET_SETTING_H
#define WIDGET_SETTING_H
#include "parameter_setting.h"
#include <QDialog>
#include <QLabel>
#include <QLineEdit>
#include <QObject>
#include <QPushButton>
#include <QSettings>
#include <rsfsc_msg.h>

namespace robosense  // NOLINT(modernize-concat-nested-namespaces)
{
namespace lidar
{

class WidgetSetting : public QDialog
{
  Q_OBJECT
public:
  WidgetSetting(const WidgetSetting&) = delete;
  WidgetSetting(WidgetSetting&&)      = delete;
  WidgetSetting& operator=(const WidgetSetting&) = delete;
  WidgetSetting& operator=(WidgetSetting&&) = delete;
  explicit WidgetSetting(QWidget* _parent, bool _is_use_password = true);
  ~WidgetSetting() override;

  void show();

Q_SIGNALS:
  void signalSaveParamFinished();

public:
  void slotUpdateAllWidgetState(robosense::lidar::rsfsc_lib::UserAuthority* _user_authority = nullptr);
  void slotWriteSetting();

  bool getPara(const QString& _para_key, QVariant& _para_value);

private:
  void closeEvent(QCloseEvent* _event) override;
  void writeLayout();
  void readLayout();

  void setupSettingWidget();
  bool makeSureChange(const QString& _change_str);

private:
  QWidget* widget_content_;
  QVBoxLayout* layout_main_;

  QPushButton* pushbutton_save_param_;
  QString setting_file_name_;
  QPushButton* pushbutton_admin_switch_;
  QLineEdit* lineedit_admin_password_;
  ParameterSetting* parameter_setting_;

  bool is_change_ = false;

  robosense::lidar::rsfsc_lib::UserAuthority* user_authority_;
};

}  // namespace lidar
}  // namespace robosense

#endif  // WIDGET_SETTING_H
