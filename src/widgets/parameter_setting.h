﻿/******************************************************************************
 * Copyright 2020 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#ifndef PARAMETER_SETTING_H
#define PARAMETER_SETTING_H

#include <QEvent>
#include <QMap>
#include <QObject>
#include <QSpinBox>
#include <QTreeWidget>
#include <QWidget>
#include <QtWidgets>
#include <memory>
#include <qobjectdefs.h>
#include <qvariant.h>
#include <qvector.h>
#include <qwidget.h>

class PathSelectControl : public QWidget
{
  Q_OBJECT
public:
  explicit PathSelectControl(bool _is_folder, QWidget* _parent = nullptr);
  ~PathSelectControl() override;

public:
  void selectPath();
  QString getCurrentPath() { return current_path_; }
  void setCurrentPath(const QString& _path);

private Q_SLOTS:
  void slotSelectPath();

Q_SIGNALS:
  void signalSelectFinish();

private:
  QString current_path_;
  QLineEdit* path_edit_    = nullptr;
  QPushButton* sel_button_ = nullptr;
  bool is_folder_          = false;
};

class ParaTreeWidget : public QTreeWidget
{
public:
  ParaTreeWidget(QWidget* _parent = nullptr) : QTreeWidget(_parent)
  {
    //安装事件过滤器
    Q_FOREACH (QAbstractSpinBox* sb, this->findChildren<QAbstractSpinBox*>())
    {
      sb->installEventFilter(this);
    }

    Q_FOREACH (QComboBox* cb, this->findChildren<QComboBox*>())
    {
      cb->installEventFilter(this);
    }
  }
  virtual ~ParaTreeWidget() {};

protected:
  virtual bool eventFilter(QObject* _obj, QEvent* _event) override
  {
    //屏蔽 spinbox 和 combobox 的滚轮事件
    if (_obj->inherits("QAbstractSpinBox") || _obj->inherits("QComboBox"))
    {
      if (_event->type() == QEvent::Wheel)
      {
        return true;
      }
    }

    return QTreeWidget::eventFilter(_obj, _event);
  }
};

enum ParaType
{
  PARA_INT,
  PARA_DOUBLE,
  PARA_STRING,
  PARA_OPTION,
  PARA_BOOL,
  PARA_PATH,
};

struct ParaIntInfo
{
  QString group;              // 存储的分组
  QString key;                // 存储的主键
  QString group_display_str;  // 分组显示名称
  QString key_display_str;    // 主键显示名称
  QString tooltip;            // 提示
  int default_val = 0;        // 默认值
  int min_val     = 0;        // 最小值
  int max_val     = 0;        // 最大值
  int step        = 1;        // 步长
};

struct ParaDoubleInfo
{
  QString group;              // 存储的分组
  QString key;                // 存储的主键
  QString group_display_str;  // 分组显示名称
  QString key_display_str;    // 主键显示名称
  QString tooltip;            // 提示
  double default_val = 0;     // 默认值
  double min_val     = 0;     // 最小值
  double max_val     = 0;     // 最大值
  int decimal_num    = 3;     // 小数点后位数
  double step        = 0.1;   // 步长
};

struct ParaStringInfo
{
  QString group;              // 存储的分组
  QString key;                // 存储的主键
  QString group_display_str;  // 分组显示名称
  QString key_display_str;    // 主键显示名称
  QString tooltip;            // 提示
  QString default_val;        // 默认值
  bool is_password = false;
};

struct ParaOptionInfo
{
  QString group;               // 存储的分组
  QString key;                 // 存储的主键
  QString group_display_str;   // 分组显示名称
  QString key_display_str;     // 主键显示名称
  QString tooltip;             // 提示
  int default_val = 0;         // 默认值
  QStringList para_list;       // 参数列表
  QVariantList para_val_list;  // 参数列表value
};

struct ParaBoolInfo
{
  QString group;              // 存储的分组
  QString key;                // 存储的主键
  QString group_display_str;  // 分组显示名称
  QString key_display_str;    // 主键显示名称
  QString tooltip;            // 提示
  bool default_val = false;   // 默认值
};

struct ParaPathInfo
{
  QString group;              // 存储的分组
  QString key;                // 存储的主键
  QString group_display_str;  // 分组显示名称
  QString key_display_str;    // 主键显示名称
  QString tooltip;            // 提示
  QString default_val;        // 默认值
  bool is_folder = false;
};

struct ParaDisplay
{
  QString group;              // 存储的分组
  QString key;                // 存储的主键
  QString group_display_str;  // 分组显示名称
  QString key_display_str;    // 主键显示名称
  QString tooltip;            // 提示
  QWidget* widget = nullptr;  // 控件
};

struct ParaInt
{
  int default_val = 0;
  int min_val     = 0;
  int max_val     = 0;
  int step        = 1;
  int cur_val     = 0;
};

struct ParaDouble
{
  double default_val = 0;  //参数默认值
  double min_val     = 0;  //参数最小值
  double max_val     = 0;  //参数最大值
  int decimal_num    = 3;  //小数点后位数
  double step        = 0.1;
  double cur_val     = 0;  //当前数值
};

struct ParaString
{
  QString default_val;  //参数默认值
  bool is_password = false;
  QString cur_val;  //当前数值
};

struct ParaOption
{
  int default_val = 0;         //参数默认值
  QStringList para_list;       //参数列表
  QVariantList para_val_list;  //参数列表value
  int cur_val = 0;             //当前数值
};

struct ParaBool
{
  bool default_val = false;  //参数默认值
  bool cur_val     = false;  //当前数值
};

struct ParaPath
{
  QString default_val;  //参数默认值
  bool is_folder = false;
  QString cur_val;  //当前数值
};

class ParaItem
{
  friend class ParaIntItem;
  friend class ParaDoubleItem;
  friend class ParaStringItem;
  friend class ParaOptionItem;
  friend class ParaBoolItem;
  friend class ParaPathItem;

public:
  ParaItem() = default;
  explicit ParaItem(const ParaDisplay& _para_disp) : para_display_(_para_disp) {}
  ParaItem(ParaItem&&)      = default;
  ParaItem(const ParaItem&) = default;
  ParaItem& operator=(ParaItem&&) = default;
  ParaItem& operator=(const ParaItem&) = default;
  virtual ~ParaItem()                  = default;
  virtual bool isValid() { return false; }
  virtual QVariant getCurrentValue() { return widget_->property("value"); }
  virtual QVariant getDefaultValue() { return widget_->property("default_value"); }
  virtual bool setCurrentValue(const QVariant& /*_value*/) { return false; }
  virtual bool createWidget(QWidget* /*_parent*/) { return false; }
  [[nodiscard]] QString getParaGroup() const { return para_display_.group; }
  [[nodiscard]] QString getParaGroupDisplayStr() const { return para_display_.group_display_str; }
  [[nodiscard]] QString getParaKey() const { return para_display_.key; }
  [[nodiscard]] QString getParaKeyDisplayStr() const { return para_display_.key_display_str; }
  [[nodiscard]] QString getParaFullKey() const { return para_display_.group + "/" + para_display_.key; }
  QWidget* getWidget() { return widget_; }
  virtual void updateWidget() {}
  virtual void widgetValueToCurrent() {}

private:
  ParaDisplay para_display_;
  QWidget* widget_ = nullptr;
};

class ParaIntItem : public ParaItem
{
  ParaDisplay para_display_;
  ParaInt para_int_;

public:
  explicit ParaIntItem(const ParaIntInfo& _para_int_info) :
    ParaItem({ _para_int_info.group, _para_int_info.key, _para_int_info.group_display_str,
               _para_int_info.key_display_str, _para_int_info.tooltip }),
    para_int_({ _para_int_info.default_val, _para_int_info.min_val, _para_int_info.max_val, _para_int_info.step })
  {
    setCurrentValue(_para_int_info.default_val);
  }
  explicit ParaIntItem(const ParaDisplay& _para_disp, const ParaInt& _para_int) :
    ParaItem(_para_disp), para_int_(_para_int)
  {}
  ParaIntItem()                   = default;
  ParaIntItem(ParaIntItem&&)      = default;
  ParaIntItem(const ParaIntItem&) = default;
  ParaIntItem& operator=(ParaIntItem&&) = default;
  ParaIntItem& operator=(const ParaIntItem&) = default;
  ~ParaIntItem() override                    = default;
  bool isValid() override;
  QVariant getCurrentValue() override;
  QVariant getDefaultValue() override;
  bool setCurrentValue(const QVariant& _value) override;
  bool createWidget(QWidget* _parent) override;
  void updateWidget() override;
  void widgetValueToCurrent() override;
};

class ParaDoubleItem : public ParaItem
{
private:
  ParaDouble para_double_;

public:
  explicit ParaDoubleItem(const ParaDoubleInfo& _para_double_info) :
    ParaItem({ _para_double_info.group, _para_double_info.key, _para_double_info.group_display_str,
               _para_double_info.key_display_str, _para_double_info.tooltip }),
    para_double_({ _para_double_info.default_val, _para_double_info.min_val, _para_double_info.max_val,
                   _para_double_info.decimal_num, _para_double_info.step })
  {
    setCurrentValue(_para_double_info.default_val);
  }
  explicit ParaDoubleItem(const ParaDisplay& _para_disp, const ParaDouble& _para_double) :
    ParaItem(_para_disp), para_double_(_para_double)
  {}
  ParaDoubleItem()                      = default;
  ParaDoubleItem(ParaDoubleItem&&)      = default;
  ParaDoubleItem(const ParaDoubleItem&) = default;
  ParaDoubleItem& operator=(ParaDoubleItem&&) = default;
  ParaDoubleItem& operator=(const ParaDoubleItem&) = default;
  ~ParaDoubleItem() override                       = default;
  bool isValid() override;
  QVariant getCurrentValue() override;
  QVariant getDefaultValue() override;
  bool setCurrentValue(const QVariant& _value) override;
  bool createWidget(QWidget* _parent) override;
  void updateWidget() override;
  void widgetValueToCurrent() override;
};

class ParaStringItem : public ParaItem
{
private:
  ParaString para_string_;

public:
  explicit ParaStringItem(const ParaStringInfo& _para_string_info) :
    ParaItem({ _para_string_info.group, _para_string_info.key, _para_string_info.group_display_str,
               _para_string_info.key_display_str, _para_string_info.tooltip }),
    para_string_({ _para_string_info.default_val, _para_string_info.is_password })
  {
    setCurrentValue(_para_string_info.default_val);
  }
  explicit ParaStringItem(const ParaDisplay& _para_disp, const ParaString& _para_string) :
    ParaItem(_para_disp), para_string_(_para_string)
  {}

  ParaStringItem()                      = default;
  ParaStringItem(ParaStringItem&&)      = default;
  ParaStringItem(const ParaStringItem&) = default;
  ParaStringItem& operator=(ParaStringItem&&) = default;
  ParaStringItem& operator=(const ParaStringItem&) = default;
  ~ParaStringItem() override                       = default;

  bool isValid() override;
  QVariant getCurrentValue() override;
  QVariant getDefaultValue() override;
  bool setCurrentValue(const QVariant& _value) override;
  bool createWidget(QWidget* _parent) override;
  void updateWidget() override;
  void widgetValueToCurrent() override;
};

class ParaBoolItem : public ParaItem
{
private:
  ParaBool para_bool_;

public:
  explicit ParaBoolItem(const ParaBoolInfo& _para_bool_info) :
    ParaItem({ _para_bool_info.group, _para_bool_info.key, _para_bool_info.group_display_str,
               _para_bool_info.key_display_str, _para_bool_info.tooltip }),
    para_bool_({ _para_bool_info.default_val })
  {
    setCurrentValue(_para_bool_info.default_val);
  }
  explicit ParaBoolItem(const ParaDisplay& _para_disp, const ParaBool& _para_bool) :
    ParaItem(_para_disp), para_bool_(_para_bool)
  {}

  ParaBoolItem()                    = default;
  ParaBoolItem(ParaBoolItem&&)      = default;
  ParaBoolItem(const ParaBoolItem&) = default;
  ParaBoolItem& operator=(ParaBoolItem&&) = default;
  ParaBoolItem& operator=(const ParaBoolItem&) = default;
  ~ParaBoolItem() override                     = default;
  bool isValid() override;
  QVariant getCurrentValue() override;
  QVariant getDefaultValue() override;
  bool setCurrentValue(const QVariant& _value) override;
  bool createWidget(QWidget* _parent) override;
  void updateWidget() override;
  void widgetValueToCurrent() override;
};

class ParaOptionItem : public ParaItem
{
private:
  ParaOption para_option_;

public:
  explicit ParaOptionItem(const ParaOptionInfo& _para_option_info) :
    ParaItem({ _para_option_info.group, _para_option_info.key, _para_option_info.group_display_str,
               _para_option_info.key_display_str, _para_option_info.tooltip }),
    para_option_({ _para_option_info.default_val, _para_option_info.para_list, _para_option_info.para_val_list })
  {
    setCurrentValue(_para_option_info.default_val);
  }
  explicit ParaOptionItem(const ParaDisplay& _para_disp, const ParaOption& _para_option) :
    ParaItem(_para_disp), para_option_(_para_option)
  {}

  ParaOptionItem()                      = default;
  ParaOptionItem(ParaOptionItem&&)      = default;
  ParaOptionItem(const ParaOptionItem&) = default;
  ParaOptionItem& operator=(ParaOptionItem&&) = default;
  ParaOptionItem& operator=(const ParaOptionItem&) = default;
  ~ParaOptionItem() override                       = default;

  bool isValid() override;
  QVariant getCurrentValue() override;
  QVariant getDefaultValue() override;
  bool setCurrentValue(const QVariant& _value) override;
  bool createWidget(QWidget* _parent) override;
  void updateWidget() override;
  void widgetValueToCurrent() override;
};

class ParaPathItem : public ParaItem
{
private:
  ParaPath para_path_;

public:
  explicit ParaPathItem(const ParaPathInfo& _para_path_info) :
    ParaItem({ _para_path_info.group, _para_path_info.key, _para_path_info.group_display_str,
               _para_path_info.key_display_str, _para_path_info.tooltip }),
    para_path_({ _para_path_info.default_val, _para_path_info.is_folder })
  {
    setCurrentValue(_para_path_info.default_val);
  }
  explicit ParaPathItem(const ParaDisplay& _para_disp, const ParaPath& _para_path) :
    ParaItem(_para_disp), para_path_(_para_path)
  {}

  ParaPathItem()                    = default;
  ParaPathItem(ParaPathItem&&)      = default;
  ParaPathItem(const ParaPathItem&) = default;
  ParaPathItem& operator=(ParaPathItem&&) = default;
  ParaPathItem& operator=(const ParaPathItem&) = default;
  ~ParaPathItem() override                     = default;

  bool isValid() override;
  QVariant getCurrentValue() override;
  QVariant getDefaultValue() override;
  bool setCurrentValue(const QVariant& _value) override;
  bool createWidget(QWidget* _parent) override;
  void updateWidget() override;
  void widgetValueToCurrent() override;
};

Q_DECLARE_METATYPE(ParaDisplay)
Q_DECLARE_METATYPE(ParaItem)
Q_DECLARE_METATYPE(ParaIntItem)
Q_DECLARE_METATYPE(ParaDoubleItem)
Q_DECLARE_METATYPE(ParaStringItem)
Q_DECLARE_METATYPE(ParaOptionItem)
Q_DECLARE_METATYPE(ParaBoolItem)
Q_DECLARE_METATYPE(ParaPathItem)

class ParameterSetting : public QWidget
{
  Q_OBJECT
public:
  explicit ParameterSetting(QWidget* _parent = nullptr, const QString& _para_settings_name = "para_settings");
  ParameterSetting(const ParameterSetting&) = delete;
  ParameterSetting(ParameterSetting&&)      = delete;
  ParameterSetting& operator=(const ParameterSetting&) = delete;
  ParameterSetting& operator=(ParameterSetting&&) = delete;
  virtual ~ParameterSetting();

public:
  bool registerIntPara(const ParaIntInfo& _para_int_info);
  bool registerDoublePara(const ParaDoubleInfo& _para_double_info);
  bool registerStringPara(const ParaStringInfo& _para_string_info);
  bool registerOptionPara(const ParaOptionInfo& _para_option_info);
  bool registerBoolPara(const ParaBoolInfo& _para_bool_info);
  bool registerPathPara(const ParaPathInfo& _para_path_info);

  void setParaTreeEnable(bool _is_enable);
  void showParaTree();
  void updateParaTree();
  void loadPara();
  bool savePara();

  bool getPara(const QString& _para_key, QVariant& _para_value);

private:
  std::unique_ptr<ParaTreeWidget> para_tree_;
  QString para_path_ { "" };
  QMap<QString, std::shared_ptr<ParaItem>> para_item_map_;
  QVector<std::shared_ptr<ParaItem>> para_item_vec_;
};

#endif  // PARAMETER_SETTING_H
