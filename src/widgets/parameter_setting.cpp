﻿/******************************************************************************
 * Copyright 2020 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#include "parameter_setting.h"
#include "config.h"
#include "rsfsc_log/rsfsc_log.h"
#include "rsfsc_qsettings.h"
#include <QFileDialog>
#include <QtCore/QTextCodec>
#include <memory>

ParameterSetting::ParameterSetting(QWidget* _parent, const QString& _para_settings_name) : QWidget(_parent)
{
  para_path_.append(PROJECT_NAME).append("/").append(_para_settings_name);
  this->setWindowFlags(Qt::WindowStaysOnTopHint);
  //this->setWindowModality(Qt::WindowModal);  //阻挡父亲窗口内其他控件，除非本dialog关闭
  this->setAttribute(Qt::WA_ShowModal, true);

  para_tree_ = std::make_unique<ParaTreeWidget>(this);

  para_tree_->setColumnCount(2);

  para_tree_->header()->setSectionResizeMode(QHeaderView::ResizeToContents);  //自适应列宽
  para_tree_->header()->setDefaultAlignment(Qt::AlignCenter);                 //表头居中
  para_tree_->setHeaderLabels({ "名称", "参数值" });                          //表头名称
  para_tree_->setStyleSheet(("QTreeWidget::item {border:1px solid #E8E8E8}"
                             "QTreeWidget::item::selected {background-color:#4682B4}"));

  QHBoxLayout* layout = new QHBoxLayout;
  layout->addWidget(para_tree_.get());
  layout->setMargin(0);
  this->setLayout(layout);
}

ParameterSetting::~ParameterSetting() = default;

bool ParameterSetting::registerIntPara(const ParaIntInfo& _para_int_info)
{
  if (para_item_map_.find(_para_int_info.key) != para_item_map_.end())
  {
    robosense::lidar::RSFSCLog::getInstance()->error("{}参数注册失败,参数已经注册过了",
                                                     _para_int_info.key.toStdString());
    return false;
  }
  auto para_int_item_ptr = std::make_shared<ParaIntItem>(_para_int_info);
  if (!para_int_item_ptr->isValid())
  {
    robosense::lidar::RSFSCLog::getInstance()->error("{}参数注册失败,参数不合法",
                                                     para_int_item_ptr->getParaKey().toStdString());
    return false;
  }

  para_item_map_[_para_int_info.key] = para_int_item_ptr;
  para_item_map_[_para_int_info.key]->createWidget(this);
  para_item_vec_.push_back(para_int_item_ptr);
  return true;
}

bool ParameterSetting::registerDoublePara(const ParaDoubleInfo& _para_double_info)
{
  if (para_item_map_.find(_para_double_info.key) != para_item_map_.end())
  {
    robosense::lidar::RSFSCLog::getInstance()->error("{}参数注册失败,参数已经注册过了",
                                                     _para_double_info.key.toStdString());
    return false;
  }
  auto para_double_item_ptr = std::make_shared<ParaDoubleItem>(_para_double_info);
  if (!para_double_item_ptr->isValid())
  {
    robosense::lidar::RSFSCLog::getInstance()->error("{}参数注册失败,参数不合法",
                                                     para_double_item_ptr->getParaKey().toStdString());
    return false;
  }

  para_item_map_[_para_double_info.key] = para_double_item_ptr;
  para_item_map_[_para_double_info.key]->createWidget(this);
  para_item_vec_.push_back(para_double_item_ptr);

  return true;
}
bool ParameterSetting::registerStringPara(const ParaStringInfo& _para_string_info)
{
  if (para_item_map_.find(_para_string_info.key) != para_item_map_.end())
  {
    robosense::lidar::RSFSCLog::getInstance()->error("{}参数注册失败,参数已经注册过了",
                                                     _para_string_info.key.toStdString());
    return false;
  }
  auto para_string_item_ptr = std::make_shared<ParaStringItem>(_para_string_info);
  if (!para_string_item_ptr->isValid())
  {
    robosense::lidar::RSFSCLog::getInstance()->error("{}参数注册失败,参数不合法",
                                                     para_string_item_ptr->getParaKey().toStdString());
    return false;
  }

  para_item_map_[_para_string_info.key] = para_string_item_ptr;
  para_item_map_[_para_string_info.key]->createWidget(this);
  para_item_vec_.push_back(para_string_item_ptr);

  return true;
}
bool ParameterSetting::registerOptionPara(const ParaOptionInfo& _para_option_info)
{
  if (para_item_map_.find(_para_option_info.key) != para_item_map_.end())
  {
    robosense::lidar::RSFSCLog::getInstance()->error("{}参数注册失败,参数已经注册过了",
                                                     _para_option_info.key.toStdString());
    return false;
  }
  auto para_option_item_ptr = std::make_shared<ParaOptionItem>(_para_option_info);
  if (!para_option_item_ptr->isValid())
  {
    robosense::lidar::RSFSCLog::getInstance()->error("{}参数注册失败,参数不合法",
                                                     para_option_item_ptr->getParaKey().toStdString());
    return false;
  }

  para_item_map_[_para_option_info.key] = para_option_item_ptr;
  para_item_map_[_para_option_info.key]->createWidget(this);
  para_item_vec_.push_back(para_option_item_ptr);

  return true;
}
bool ParameterSetting::registerBoolPara(const ParaBoolInfo& _para_bool_info)
{
  if (para_item_map_.find(_para_bool_info.key) != para_item_map_.end())
  {
    robosense::lidar::RSFSCLog::getInstance()->error("{}参数注册失败,参数已经注册过了",
                                                     _para_bool_info.key.toStdString());
    return false;
  }
  auto para_bool_item_ptr = std::make_shared<ParaBoolItem>(_para_bool_info);
  if (!para_bool_item_ptr->isValid())
  {
    robosense::lidar::RSFSCLog::getInstance()->error("{}参数注册失败,参数不合法",
                                                     para_bool_item_ptr->getParaKey().toStdString());
    return false;
  }

  para_item_map_[_para_bool_info.key] = para_bool_item_ptr;
  para_item_map_[_para_bool_info.key]->createWidget(this);
  para_item_vec_.push_back(para_bool_item_ptr);

  return true;
}
bool ParameterSetting::registerPathPara(const ParaPathInfo& _para_path_info)
{
  if (para_item_map_.find(_para_path_info.key) != para_item_map_.end())
  {
    robosense::lidar::RSFSCLog::getInstance()->error("{}参数注册失败,参数已经注册过了",
                                                     _para_path_info.key.toStdString());
    return false;
  }
  auto para_path_item_ptr = std::make_shared<ParaPathItem>(_para_path_info);
  if (!para_path_item_ptr->isValid())
  {
    robosense::lidar::RSFSCLog::getInstance()->error("{}参数注册失败,参数不合法",
                                                     para_path_item_ptr->getParaKey().toStdString());
    return false;
  }

  para_item_map_[_para_path_info.key] = para_path_item_ptr;
  para_item_map_[_para_path_info.key]->createWidget(this);
  para_item_vec_.push_back(para_path_item_ptr);

  return true;
}

void ParameterSetting::setParaTreeEnable(bool _enable) { para_tree_->setEnabled(_enable); }

void ParameterSetting::showParaTree()
{
  para_tree_->clear();
  int tree_row_count = 0;

  QHash<QString, QTreeWidgetItem*> group_item_map;
  for (const auto& para_item : para_item_vec_)
  {
    QString group = para_item->getParaGroup();
    if (!group_item_map.contains(group))
    {
      group_item_map[group] = new QTreeWidgetItem(para_tree_.get());
      group_item_map[group]->setText(0, para_item->getParaGroupDisplayStr());
      para_tree_->addTopLevelItem(group_item_map[group]);
    }
    QTreeWidgetItem* group_item = group_item_map[group];
    QTreeWidgetItem* key_item   = new QTreeWidgetItem(group_item);
    auto* key_widget            = para_item->getWidget();
    if (key_widget == nullptr)
    {
      robosense::lidar::RSFSCLog::getInstance()->error("{} para widget has not created, display error",
                                                       para_item->getParaKey().toStdString());
      continue;
    }

    key_item->setText(0, para_item->getParaKeyDisplayStr());
    para_tree_->setItemWidget(key_item, 1, key_widget);
    group_item->addChild(key_item);
    ++tree_row_count;
  }
  para_tree_->expandAll();

  this->setWindowTitle(PROJECT_NAME);
  int widget_height = tree_row_count * 30 + 50;
  this->resize(300, widget_height);
  this->show();
}

void ParameterSetting::loadPara()
{
  if (para_item_map_.empty())
  {
    robosense::lidar::RSFSCLog::getInstance()->error("load para fail, map is empty!");
    return;
  }

  robosense::lidar::rsfsc_lib::RSFSCQSettings setting("RoboSense", para_path_);
  setting.setIniCodec(QTextCodec::codecForName("UTF_8"));

  for (const QString& key : para_item_map_.keys())
  {
    auto& para_item  = para_item_map_[key];
    QString full_key = para_item->getParaFullKey();
    if (setting.contains(full_key))
    {
      QVariant value = setting.value(full_key);
      if (value.isValid())
      {
        para_item->setCurrentValue(value);
        para_item->updateWidget();
      }
    }
    else
    {
      setting.setValue(full_key, para_item->getDefaultValue());
    }
  }
}

bool ParameterSetting::savePara()
{
  if (para_item_map_.empty())
  {
    robosense::lidar::RSFSCLog::getInstance()->error("save para fail, map is empty!");
    return false;
  }

  robosense::lidar::rsfsc_lib::RSFSCQSettings setting("RoboSense", para_path_);
  setting.setIniCodec(QTextCodec::codecForName("UTF_8"));

  for (const QString& key : para_item_map_.keys())
  {
    auto& para_item = para_item_map_[key];
    para_item->widgetValueToCurrent();
    QString full_key = para_item->getParaFullKey();

    setting.setValue(full_key, para_item->getCurrentValue());
  }
  return true;
}

bool ParameterSetting::getPara(const QString& _para_key, QVariant& _para_value)
{
  if (para_item_map_.find(_para_key) == para_item_map_.end())
  {
    robosense::lidar::RSFSCLog::getInstance()->error("{}参数获取失败,参数未注册", _para_key.toStdString());
    return false;
  }
  auto& para_item  = para_item_map_[_para_key];
  bool can_convert = _para_value.canConvert(_para_value.userType());

  _para_value = para_item->getCurrentValue();

  return can_convert;
}

////////////////////////////////////////////////////////////////////////////////////////////////////////////
PathSelectControl::PathSelectControl(bool _is_folder, QWidget* _parent) :
  QWidget(_parent), path_edit_(new QLineEdit(this)), is_folder_(_is_folder)
{

  QHBoxLayout* layout = new QHBoxLayout;

  path_edit_->setReadOnly(true);
  sel_button_ = new QPushButton("...", this);
  sel_button_->setMaximumWidth(30);
  layout->addWidget(path_edit_);
  layout->addWidget(sel_button_);
  layout->setContentsMargins(0, 0, 0, 0);
  this->setLayout(layout);

  connect(sel_button_, &QPushButton::clicked, this, &PathSelectControl::selectPath);
  connect(path_edit_, &QLineEdit::editingFinished, this, &PathSelectControl::slotSelectPath);
}

PathSelectControl::~PathSelectControl() = default;

void PathSelectControl::selectPath()
{
  QString dir_name;
  QFileDialog file_dialog;

  if (is_folder_)
  {
    dir_name = QFileDialog::getExistingDirectory(this, "选择文件夹", "./");
  }
  else
  {
    dir_name = QFileDialog::getOpenFileName(this, "选择文件", "./", "ALL(*.*)");
  }
  current_path_ = dir_name;
  path_edit_->setText(dir_name);

  signalSelectFinish();
}

void PathSelectControl::setCurrentPath(const QString& _path)
{
  current_path_ = _path;
  path_edit_->setText(_path);
}

void PathSelectControl::slotSelectPath()
{
  QString dir = path_edit_->text();
  setCurrentPath(dir);

  signalSelectFinish();
}

bool ParaIntItem::isValid()
{
  if (para_int_.max_val < para_int_.min_val)
  {
    robosense::lidar::RSFSCLog::getInstance()->error("{} max_val < min_val, max: {}, min: {}",
                                                     getParaKey().toStdString(), para_int_.max_val, para_int_.min_val);
    return false;
  }
  if (para_int_.cur_val < para_int_.min_val || para_int_.cur_val > para_int_.max_val)
  {
    robosense::lidar::RSFSCLog::getInstance()->error(
      "{} cur_val is not in range, cur_val: {}, min_val: {}, max_val: {}", getParaKey().toStdString(),
      para_int_.cur_val, para_int_.min_val, para_int_.max_val);
    return false;
  }

  return true;
}

QVariant ParaIntItem::getCurrentValue() { return para_int_.cur_val; }
QVariant ParaIntItem::getDefaultValue() { return para_int_.default_val; }

bool ParaIntItem::setCurrentValue(const QVariant& _value)
{
  if (_value.canConvert<int>())
  {
    para_int_.cur_val = _value.toInt();
    return true;
  }
  return false;
}

bool ParaIntItem::createWidget(QWidget* _parent)
{
  QSpinBox* spin_box = new QSpinBox(_parent);
  spin_box->setRange(para_int_.min_val, para_int_.max_val);
  spin_box->setSingleStep(para_int_.step);
  spin_box->setValue(para_int_.cur_val);
  spin_box->setToolTip(para_display_.tooltip);
  spin_box->setObjectName(para_display_.key);
  spin_box->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);
  widget_ = spin_box;
  return true;
}

void ParaIntItem::updateWidget()
{
  if (widget_ == nullptr)
  {
    robosense::lidar::RSFSCLog::getInstance()->error("{} setting widget is nullptr", getParaKey().toStdString());
  }
  QSpinBox* spin_box = dynamic_cast<QSpinBox*>(widget_);
  if (spin_box != nullptr)
  {
    spin_box->setValue(para_int_.cur_val);
  }
}

void ParaIntItem::widgetValueToCurrent()
{
  if (widget_ == nullptr)
  {
    robosense::lidar::RSFSCLog::getInstance()->error("{} setting widget is nullptr", getParaKey().toStdString());
  }
  QSpinBox* spin_box = dynamic_cast<QSpinBox*>(widget_);
  if (spin_box != nullptr)
  {
    para_int_.cur_val = spin_box->value();
  }
}

bool ParaDoubleItem::isValid()
{
  if (para_double_.max_val < para_double_.min_val)
  {
    robosense::lidar::RSFSCLog::getInstance()->error(
      "{} max_val < min_val, max: {}, min: {}", getParaKey().toStdString(), para_double_.max_val, para_double_.min_val);
    return false;
  }
  if (para_double_.cur_val < para_double_.min_val || para_double_.cur_val > para_double_.max_val)
  {
    robosense::lidar::RSFSCLog::getInstance()->error(
      "{} cur_val is not in range, cur_val: {}, min_val: {}, max_val: {}", getParaKey().toStdString(),
      para_double_.cur_val, para_double_.min_val, para_double_.max_val);
    return false;
  }
  return true;
}

QVariant ParaDoubleItem::getCurrentValue() { return para_double_.cur_val; }
QVariant ParaDoubleItem::getDefaultValue() { return para_double_.default_val; }

bool ParaDoubleItem::setCurrentValue(const QVariant& _value)
{
  if (_value.canConvert<double>())
  {
    para_double_.cur_val = _value.toDouble();
    return true;
  }
  return false;
}

bool ParaDoubleItem::createWidget(QWidget* _parent)
{
  QDoubleSpinBox* spin_box = new QDoubleSpinBox(_parent);
  spin_box->setRange(para_double_.min_val, para_double_.max_val);
  spin_box->setDecimals(para_double_.decimal_num);
  spin_box->setSingleStep(para_double_.step);
  spin_box->setKeyboardTracking(false);
  spin_box->setValue(para_double_.default_val);
  spin_box->setToolTip(para_display_.tooltip);
  widget_ = spin_box;
  return true;
}

void ParaDoubleItem::updateWidget()
{
  if (widget_ == nullptr)
  {
    robosense::lidar::RSFSCLog::getInstance()->error("{} setting widget is nullptr", getParaKey().toStdString());
  }
  QDoubleSpinBox* spin_box = dynamic_cast<QDoubleSpinBox*>(widget_);
  if (spin_box != nullptr)
  {
    spin_box->setValue(para_double_.cur_val);
  }
}

void ParaDoubleItem::widgetValueToCurrent()
{
  if (widget_ == nullptr)
  {
    robosense::lidar::RSFSCLog::getInstance()->error("{} setting widget is nullptr", getParaKey().toStdString());
  }
  QDoubleSpinBox* spin_box = dynamic_cast<QDoubleSpinBox*>(widget_);
  if (spin_box != nullptr)
  {
    para_double_.cur_val = spin_box->text().toDouble();
  }
}

bool ParaStringItem::isValid() { return true; }

QVariant ParaStringItem::getCurrentValue() { return para_string_.cur_val; }
QVariant ParaStringItem::getDefaultValue() { return para_string_.default_val; }

bool ParaStringItem::setCurrentValue(const QVariant& _value)
{
  if (_value.canConvert<QString>())
  {
    para_string_.cur_val = _value.toString();
    return true;
  }
  return false;
}

bool ParaStringItem::createWidget(QWidget* _parent)
{
  QLineEdit* line_edit = new QLineEdit(_parent);
  line_edit->setText(para_string_.default_val);
  line_edit->setToolTip(para_display_.tooltip);
  line_edit->setObjectName(para_display_.key);
  line_edit->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);
  if (para_string_.is_password)
  {
    line_edit->setEchoMode(QLineEdit::Password);
  }
  widget_ = line_edit;
  return true;
}

void ParaStringItem::updateWidget()
{
  if (widget_ == nullptr)
  {
    robosense::lidar::RSFSCLog::getInstance()->error("{} setting widget is nullptr", getParaKey().toStdString());
  }
  QLineEdit* line_edit = dynamic_cast<QLineEdit*>(widget_);
  if (line_edit != nullptr)
  {
    line_edit->setText(para_string_.cur_val);
  }
}

void ParaStringItem::widgetValueToCurrent()
{
  if (widget_ == nullptr)
  {
    robosense::lidar::RSFSCLog::getInstance()->error("{} setting widget is nullptr", getParaKey().toStdString());
  }
  QLineEdit* line_edit = dynamic_cast<QLineEdit*>(widget_);
  if (line_edit != nullptr)
  {
    para_string_.cur_val = line_edit->text();
  }
}

bool ParaBoolItem::isValid() { return true; }

QVariant ParaBoolItem::getCurrentValue() { return para_bool_.cur_val; }
QVariant ParaBoolItem::getDefaultValue() { return para_bool_.default_val; }

bool ParaBoolItem::setCurrentValue(const QVariant& _value)
{
  if (_value.canConvert<bool>())
  {
    para_bool_.cur_val = _value.toBool();
    return true;
  }
  return false;
}

bool ParaBoolItem::createWidget(QWidget* _parent)
{
  QCheckBox* check_box = new QCheckBox(_parent);
  check_box->setChecked(para_bool_.default_val);
  check_box->setToolTip(para_display_.tooltip);
  check_box->setObjectName(para_display_.key);
  check_box->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);
  widget_ = check_box;
  return true;
}

void ParaBoolItem::updateWidget()
{
  if (widget_ == nullptr)
  {
    robosense::lidar::RSFSCLog::getInstance()->error("{} setting widget is nullptr", getParaKey().toStdString());
  }
  QCheckBox* check_box = dynamic_cast<QCheckBox*>(widget_);
  if (check_box != nullptr)
  {
    check_box->setChecked(para_bool_.cur_val);
  }
}

void ParaBoolItem::widgetValueToCurrent()
{
  if (widget_ == nullptr)
  {
    robosense::lidar::RSFSCLog::getInstance()->error("{} setting widget is nullptr", getParaKey().toStdString());
  }
  QCheckBox* check_box = dynamic_cast<QCheckBox*>(widget_);
  if (check_box != nullptr)
  {
    para_bool_.cur_val = check_box->isChecked();
  }
}

bool ParaOptionItem::isValid()
{
  if (para_option_.para_list.empty())
  {
    robosense::lidar::RSFSCLog::getInstance()->error("{} para_list is empty", getParaKey().toStdString());
    return false;
  }
  return true;
}
bool ParaPathItem::isValid() { return true; }

QVariant ParaOptionItem::getCurrentValue()
{
  if (!para_option_.para_val_list.empty() && para_option_.cur_val >= 0 &&
      para_option_.cur_val < para_option_.para_val_list.size())
  {
    return para_option_.para_val_list[para_option_.cur_val];
  }
  return para_option_.cur_val;
}
QVariant ParaOptionItem::getDefaultValue()
{
  if (!para_option_.para_val_list.empty() && para_option_.default_val >= 0 &&
      para_option_.default_val < para_option_.para_val_list.size())
  {
    return para_option_.para_val_list[para_option_.default_val];
  }
  return para_option_.default_val;
}
QVariant ParaPathItem::getCurrentValue() { return para_path_.cur_val; }
QVariant ParaPathItem::getDefaultValue() { return para_path_.default_val; }

bool ParaOptionItem::setCurrentValue(const QVariant& _value)
{
  if (!para_option_.para_val_list.empty())
  {
    if (_value.canConvert<QString>())
    {
      QString new_value = _value.toString();
      for (int i = 0; i < para_option_.para_val_list.size(); ++i)
      {
        if (new_value == para_option_.para_val_list[i])
        {
          para_option_.cur_val = i;
          return true;
        }
      }
    }
  }
  if (_value.canConvert<int>())
  {
    int new_value = _value.toInt();
    if (new_value >= 0 && new_value < para_option_.para_list.size())
    {
      para_option_.cur_val = new_value;
      return true;
    }
  }
  return false;
}
bool ParaPathItem::setCurrentValue(const QVariant& _value)
{
  if (_value.canConvert<QString>())
  {
    para_path_.cur_val = _value.toString();
    return true;
  }
  return false;
}

bool ParaOptionItem::createWidget(QWidget* _parent)
{
  QComboBox* combo_box = new QComboBox(_parent);
  combo_box->addItems(para_option_.para_list);
  combo_box->setCurrentIndex(para_option_.default_val);
  combo_box->setToolTip(para_display_.tooltip);
  combo_box->setObjectName(para_display_.key);
  combo_box->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);
  widget_ = combo_box;
  return true;
}
bool ParaPathItem::createWidget(QWidget* _parent)
{
  PathSelectControl* path_select_control = new PathSelectControl(para_path_.is_folder, _parent);
  path_select_control->setCurrentPath(para_path_.default_val);
  path_select_control->setToolTip(para_display_.tooltip);
  path_select_control->setObjectName(para_display_.key);
  path_select_control->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);
  widget_ = path_select_control;
  return true;
}
void ParaOptionItem::updateWidget()
{
  if (widget_ == nullptr)
  {
    robosense::lidar::RSFSCLog::getInstance()->error("{} setting widget is nullptr", getParaKey().toStdString());
  }
  QComboBox* combo_box = dynamic_cast<QComboBox*>(widget_);
  if (combo_box != nullptr)
  {
    combo_box->setCurrentIndex(para_option_.cur_val);
  }
}
void ParaPathItem::updateWidget()
{
  if (widget_ == nullptr)
  {
    robosense::lidar::RSFSCLog::getInstance()->error("{} setting widget is nullptr", getParaKey().toStdString());
    return;
  }
  PathSelectControl* path_select_control = dynamic_cast<PathSelectControl*>(widget_);
  if (path_select_control != nullptr)
  {
    path_select_control->setCurrentPath(para_path_.cur_val);
  }
}
void ParaOptionItem::widgetValueToCurrent()
{
  if (widget_ == nullptr)
  {
    robosense::lidar::RSFSCLog::getInstance()->error("{} setting widget is nullptr", getParaKey().toStdString());
    return;
  }
  QComboBox* combo_box = dynamic_cast<QComboBox*>(widget_);
  if (combo_box != nullptr)
  {
    para_option_.cur_val = combo_box->currentIndex();
  }
}
void ParaPathItem::widgetValueToCurrent()
{
  if (widget_ == nullptr)
  {
    robosense::lidar::RSFSCLog::getInstance()->error("{} setting widget is nullptr", getParaKey().toStdString());
    return;
  }
  PathSelectControl* path_select_control = dynamic_cast<PathSelectControl*>(widget_);
  if (path_select_control != nullptr)
  {
    para_path_.cur_val = path_select_control->getCurrentPath();
  }
}
