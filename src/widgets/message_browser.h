﻿/******************************************************************************
 * Copyright 2020 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
/**
 * @file   message_browser.h
 * <AUTHOR> Chen (<EMAIL>), Andysen Shen (<EMAIL>)
 * @brief  You can use MessageBrowser with RSFSCLog to show log in UI
 * @version 1.0.1
 * @date 2023-11-17
 * 
 * Copyright (c) 2022, RoboSense Co.,Ltd. - www.robosense.ai
 * All Rights Reserved.
 *  
 * If you find any BUG or improvement in RSFSCLog, please contact the authors, so we can share your idea  
 * 
*/
#ifndef RSFSCLOG_MESSAGE_BROWSER_H
#define RSFSCLOG_MESSAGE_BROWSER_H

#include <QtCore/QObject>
#include <QtCore/QString>
#include <QtWidgets/QWidget>

#include <array>

class QTextBrowser;

namespace robosense
{
namespace lidar
{
namespace rsfsc_lib
{

class MessageBrowser final : public QWidget
{
  Q_OBJECT
public:
  explicit MessageBrowser(QWidget* _parent);
  explicit MessageBrowser(const QString& _project_name, QWidget* _parent);
  explicit MessageBrowser(const MessageBrowser&) = delete;
  explicit MessageBrowser(MessageBrowser&&)      = delete;
  MessageBrowser& operator=(MessageBrowser&&) = delete;
  MessageBrowser& operator=(const MessageBrowser&) = delete;
  ~MessageBrowser() final;

protected Q_SLOTS:
  void slotShowMessage(const QString& _msg);
  /****************************************************************
   * @brief     make sure all error text message is showed through this function
   * 
   * @param     msg                
  ****************************************************************/
  void slotShowErrorText(const QString& _msg);
  /****************************************************************
   * @brief     make sure all info text message is showed through this function
   * 
   * @param     msg                
  ****************************************************************/

  void slotShowInfoText(const QString& _msg);
  void slotShowInfoVariant(const QString& _name, const QVariant& _value);
  /****************************************************************
   * @brief     make sure all warning text message is showed through this function
   * 
   * @param     msg                
  ****************************************************************/
  void slotShowWarningText(const QString& _msg);

  void slotClear();
  void slotHold();

private:
  void readSetting();
  void closeEvent(QCloseEvent* _event) override;
  void showEvent(QShowEvent* _event) override;
  enum TextType
  {
    TEXT_TYPE_TRACE = 0,
    TEXT_TYPE_DEBUG,
    TEXT_TYPE_INFO,
    TEXT_TYPE_WARNING,
    TEXT_TYPE_ERROR,
    TEXT_TYPE_SIZE
  };
  void refresh();
  static int parserMessage(const QString& _msg, QString& _valid_msg);

  QTextBrowser* text_browser_;
  bool is_refresh_;
  QString project_name_;

  constexpr static std::array<const char*, TEXT_TYPE_SIZE> TEXT_COLOR = { "lightGray", "gray", "black", "blue", "red" };
  constexpr static std::array<const char*, TEXT_TYPE_SIZE> TEXT_TYPE_STR = { "T", "D", "I", "W", "E" };
  constexpr static std::array<const char*, TEXT_TYPE_SIZE> TEXT_TYPE_NAME = { "跟踪", "调试", "正常", "警告", "错误" };
};
}  // namespace rsfsc_lib
}  // namespace lidar
}  // namespace robosense

#endif  // RSFSCLOG_MESSAGE_BROWSER_H