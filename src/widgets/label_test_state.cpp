﻿/******************************************************************************
 * Copyright 2021 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#include "label_test_state.h"

#include "app_event.h"
#include <QtWidgets/QSizePolicy>

// NOLINTNEXTLINE
namespace robosense
{
namespace lidar
{

LabelTestState::LabelTestState(QWidget* _parent) : QLabel(_parent)
{
  QSizePolicy size_policy = this->sizePolicy();
  size_policy.setVerticalPolicy(QSizePolicy::Expanding);
  this->setSizePolicy(size_policy);
  this->setAlignment(Qt::AlignCenter);

  setState(TestState::NOT_START);
  // 设置字体的大小
  QFont font;
  font.setWeight(QFont::Bold);
  font.setPointSize(25);
  this->setFont(font);

  connect(app(), &AppEvent::signalUpdateTestState, this, &LabelTestState::slotUpdateState, Qt::QueuedConnection);
}

LabelTestState::~LabelTestState() = default;

void LabelTestState::slotUpdateState(const TestState _ts) { setState(_ts); }
void LabelTestState::setState(const TestState _ts)
{
  QFont font;
  font.setWeight(QFont::Bold);
  font.setPointSize(25);
  switch (_ts)
  {
  case TestState::NOT_START:
    this->setText("未开始");
    this->setStyleSheet("background-color: rgb(180,180,180);color: rgb(0, 0, 0);");
    break;
  case TestState::RUNNING:
    this->setText("运行中");
    this->setStyleSheet("background-color: rgb(155, 0, 155);color: rgb(100, 255, 100);");
    break;
  case TestState::ABORT:
    this->setText("中断");
    this->setStyleSheet("background-color: rgb(255, 165, 0);color: rgb(0, 0, 0);");
    break;
  case TestState::FAILED:
    this->setText("失败");
    this->setStyleSheet("background-color: rgb(255, 0, 0);color: rgb(0, 255, 255);");
    break;
  case TestState::PASS:
    this->setText("成功");
    this->setStyleSheet("background-color: rgb(0, 128, 0);color: rgb(0, 0, 0);");
    break;

  default: break;
  }
  this->setFont(font);
  current_test_state_ = _ts;
}

}  // namespace lidar
}  // namespace robosense