﻿/******************************************************************************
 * Copyright 2024 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai
 *
 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONS<PERSON>QUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef WIDGET_LIDAR_INFO_LIST_H
#define WIDGET_LIDAR_INFO_LIST_H

#include "project_lidar_info.h"
#include <QVBoxLayout>
#include <QWidget>

class QPushButton;

class WidgetLidarInfoList : public QWidget
{
  Q_OBJECT
  friend class MainWindow;

public:
  struct WidgetInfo
  {
    WidgetLidarInfo* lidar;
    QPushButton* button_connect;
    QPushButton* button_open_data_folder;
  };

  explicit WidgetLidarInfoList(QWidget* _parent = nullptr);
  explicit WidgetLidarInfoList(const int _lidar_num, QWidget* _parent = nullptr);
  explicit WidgetLidarInfoList(WidgetLidarInfoList&&)      = delete;
  explicit WidgetLidarInfoList(const WidgetLidarInfoList&) = delete;
  WidgetLidarInfoList& operator=(WidgetLidarInfoList&&) = delete;
  WidgetLidarInfoList& operator=(const WidgetLidarInfoList&) = delete;
  ~WidgetLidarInfoList() override;

public:
  void createLidarInfo();
  WidgetLidarInfo* getLidarInfo(const uint32_t _index);

public Q_SLOTS:
  void slotConnecting(const quint32 _index);
  void slotConnected(const quint32 _index);
  void slotDisconnected(const quint32 _index);
  void slotDisconnecting(const quint32 _index);

  /*
   * @brief     更新lidar数量
   * 
   * @param     _lidar_num     需要显示的雷达数量
  **/
  void updateLidarInfoList(int _lidar_num);
  void createLidarInfo(const uint32_t _index);
Q_SIGNALS:
  void signalButtonConnectClicked(const quint32 _index);
  void signalButtonDisconnectClicked(const quint32 _index);

  void signalButtonOpenDataFolderClicked(const quint32 _index);

private:
  void setLidarInfoState(const uint32_t _index, const bool _enable);

  std::map<uint32_t, WidgetInfo> widget_info_map_;
  QVBoxLayout* layout_;
  int lidar_num_      = 0;
  int curr_lidar_num_ = 0;
  QString root_path_;
};
#endif  // WIDGET_LIDAR_INFO_LIST_H