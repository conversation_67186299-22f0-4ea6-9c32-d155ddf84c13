﻿/******************************************************************************
 * Copyright 2024 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai
 *
 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONS<PERSON>QUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "app_event.h"

#include "rsfsc_log/rsfsc_log_macro.h"

#include "ui_mainwindow.h"
#include "ui_widget_ext_device.h"

#include "mainwindow.h"
#include <QTextStream>

// NOLINTNEXTLINE(modernize-concat-nested-namespaces)
namespace robosense
{
namespace lidar
{

QString AppEvent::getFactoryLoc() { return main_window_->para_widgets_.combobox_factory_loc->currentText(); }
QString AppEvent::getLineNum() { return main_window_->para_widgets_.combobox_line_num->currentText(); }
QString AppEvent::getEthName() { return main_window_->para_widgets_.lineedit_eth_name->text(); }

// bool loadVbdFile(const QString& _vbd_file_path);
//   bool loadVbdBySn(const QString& _sn, double& _vbd);

bool AppEvent::loadVbdFile(const QString& _vbd_file_path)
{
  QFile file(_vbd_file_path);
  if (!file.open(QIODevice::ReadOnly | QIODevice::Text))
  {
    LOG_ERROR("打开文件失败, 文件路径: {}", _vbd_file_path);
    return false;
  }
  QTextStream inp(&file);
  while (!inp.atEnd())
  {
    QString line       = inp.readLine();
    QStringList fields = line.split(",");
    if (fields.size() < 3)
    {
      LOG_ERROR("文件格式错误, 行: {}", line);
      return false;
    }
    vbd_map_[fields[0].toStdString()] = fields[2].toDouble();
  }
  return true;
}

bool AppEvent::loadVbdBySn(const QString& _sn, double& _vbd)
{
  auto iter = vbd_map_.find(_sn.toStdString());
  if (iter == vbd_map_.end())
  {
    LOG_ERROR("未找到sn: {}", _sn);
    return false;
  }
  _vbd = iter->second;
  return true;
}
}  // namespace lidar
}  // namespace robosense