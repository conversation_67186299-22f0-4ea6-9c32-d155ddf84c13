﻿/******************************************************************************
 * Copyright 2024 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai
 *
 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONS<PERSON>QUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "utils/pcap_utils.h"

#ifdef _WIN32
#  include <winsock2.h>
#else
#  include <net/ethernet.h>  // 提供以太网头部结构
#  include <netinet/ether.h>
#  include <netinet/ip.h>   // 提供ip头部结构
#  include <netinet/udp.h>  // 提供udp头部结构
#endif

#if defined(_MSC_VER)  // MSVC
#  include <intrin.h>
#  define BSWAP16 _byteswap_ushort
#  define BSWAP32 _byteswap_ulong
#  define BSWAP64 _byteswap_uint64
#elif defined(__GNUC__) || defined(__clang__)  // GCC or Clang
#  define BSWAP16 __builtin_bswap16
#  define BSWAP32 __builtin_bswap32
#  define BSWAP64 __builtin_bswap64
#else
#  error "Unsupported compiler"
#endif

struct SllHeader
{
  uint16_t pkt_type;
  uint16_t addr_type;
  uint16_t addr_len;
  std::array<uint8_t, 6> source;
  uint16_t unused;
  uint16_t protocol;
} __attribute__((packed));

bool PcapUtils::loadOfflinePcapFile(const std::string& _file_path)
{
  std::array<char, PCAP_ERRBUF_SIZE> err_buf {};
  pcap_handle_ =
    std::unique_ptr<pcap_t, decltype(&pcap_close)>(pcap_open_offline(_file_path.c_str(), err_buf.data()), pcap_close);
  if (pcap_handle_ == nullptr)
  {
    LOG_ERROR("Error opening pcap file: {}", err_buf.data());
    return false;
  }
  data_link_type_ = pcap_datalink(pcap_handle_.get());
  if (data_link_type_ == DLT_EN10MB)
  {
    LOG_INFO("数据链路类型: DLT_EN10MB");
  }
  else if (data_link_type_ == DLT_LINUX_SLL)
  {
    LOG_INFO("数据链路类型: DLT_LINUX_SLL");
  }
  else
  {
    LOG_ERROR("数据链路类型: {}", data_link_type_);
    return false;
  }

  pkthdr_ = {};

  LOG_INFO("Successfully opened pcap file: {}", _file_path);
  return true;
}

const uint8_t* PcapUtils::getNextOfflinePacket()
{
  if (pcap_handle_ == nullptr)
  {
    LOG_ERROR("获取数据包失败, err_msg: 尚未加载pcap文件");
    return {};
  }

  const uint8_t* packet = nullptr;

  packet = pcap_next(pcap_handle_.get(), &pkthdr_);
  if (packet == nullptr)
  {
    LOG_INFO("已经读取到文件末尾");
    return {};
  }
  // LOG_INFO("pkt len: {}, caplen: {}, ts.tv_sec: {}, ts.tv_usec: {}", pkthdr_.len, pkthdr_.caplen, pkthdr_.ts.tv_sec,
  //          pkthdr_.ts.tv_usec);

  return packet;
}

PcapUtils::UdpPacketInfo PcapUtils::getNextOfflineUdpPacket(const PacketFilter& _filter)
{
  auto ip_src  = inet_addr(_filter.ip_src.c_str());
  auto ip_dst  = inet_addr(_filter.ip_dst.c_str());
  int port_src = _filter.port_src > 0 ? htons(static_cast<uint16_t>(_filter.port_src)) : -1;
  int port_dst = _filter.port_dst > 0 ? htons(static_cast<uint16_t>(_filter.port_dst)) : -1;

  const uint8_t* packet = nullptr;
  while ((packet = getNextOfflinePacket()) != nullptr)
  {
    size_t offset       = 0;
    const ip* ip_header = nullptr;
    uint16_t eth_type   = 0;
    if (data_link_type_ == DLT_EN10MB)
    {
      const ether_header* eth_header = reinterpret_cast<const ether_header*>(packet);
      eth_type                       = eth_header->ether_type;
      offset += sizeof(ether_header);
    }
    else
    {
      const SllHeader* sll_header = reinterpret_cast<const SllHeader*>(packet);
      eth_type                    = sll_header->protocol;
      offset += sizeof(SllHeader);
    }
    ip_header = reinterpret_cast<const ip*>(packet + offset);

    if (ntohs(eth_type) != ETHERTYPE_IP)
    {
      // LOG_INFO("eth_header->ether_type unmatched : {}, ETHERTYPE_IP: {}", ntohs(eth_type), ETHERTYPE_IP);
      continue;
    }

    if (!_filter.ip_src.empty() && ip_src != ip_header->ip_src.s_addr)
    {
      // LOG_INFO("ip_src unmatched : {}, eth_udp_packet->ip_header.src_ip.s_addr: {}", ip_src, ip_header->ip_src.s_addr);
      continue;
    }
    if (!_filter.ip_src.empty() && ip_dst != ip_header->ip_dst.s_addr)
    {
      // LOG_INFO("ip_dst unmatched : {}, eth_udp_packet->ip_header.dst_ip.s_addr: {}", ip_dst, ip_header->ip_dst.s_addr);
      continue;
    }

    if (ip_header->ip_p != IPPROTO_UDP)
    {
      // LOG_INFO("protocol unmatched : {}, eth_udp_packet->ip_header.protocol: {}", IPPROTO_UDP, ip_header->ip_p);
      continue;
    }

    offset += ip_header->ip_hl * 4;
    const udphdr* udp_header = reinterpret_cast<const udphdr*>(packet + offset);

    if (_filter.port_src > 0 && port_src != udp_header->uh_sport)
    {
      // LOG_INFO("port_src unmatched : {}, eth_udp_packet->udp_header.src_port: {}",
      //  BSWAP16(static_cast<uint16_t>(port_src)), BSWAP16(static_cast<uint16_t>(udp_header->uh_sport)));
      continue;
    }
    if (_filter.port_dst > 0 && port_dst != udp_header->uh_dport)
    {
      // LOG_INFO("port_dst unmatched : {}, eth_udp_packet->udp_header.dst_port: {}",
      //  BSWAP16(static_cast<uint16_t>(port_dst)), BSWAP16(static_cast<uint16_t>(udp_header->uh_dport)));
      continue;
    }

    // if (_filter.data_length > 0 &&
    //     _filter.data_length != (ntohs(eth_udp_packet->udp_header.length) - sizeof(UdpHeader)))
    // {
    //   LOG_INFO("data_length unmatched : {}, eth_udp_packet->udp_header.length: {}", _filter.data_length,
    //            ntohs(eth_udp_packet->udp_header.length) - sizeof(UdpHeader));
    //   continue;
    // }

    if (_filter.data_length > 0 && _filter.data_length != ntohs(udp_header->uh_ulen) - sizeof(udphdr))
    {
      LOG_INFO("data_length unmatched : {}, eth_udp_packet->udp_header.length: {}", _filter.data_length,
               ntohs(udp_header->uh_ulen) - sizeof(udphdr));
      continue;
    }

    UdpPacketInfo udp_packet_info;
    udp_packet_info.ip_header  = ip_header;
    udp_packet_info.udp_header = udp_header;

    offset += sizeof(udphdr);
    udp_packet_info.udp_data = packet + offset;
    return udp_packet_info;
  }

  // LOG_INFO("未找到相关包");
  return {};
}