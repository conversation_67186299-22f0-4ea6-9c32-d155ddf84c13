﻿set(CMAKE_CURRENT_SOURCE_DIR @CMAKE_CURRENT_SOURCE_DIR@)
set(CMAKE_CURRENT_BINARY_DIR @CMAKE_CURRENT_BINARY_DIR@)
set(OUTPUT_PREFIX @OUTPUT_PREFIX@)

message(STATUS "Running post-cpack script")
message(STATUS "OUTPUT_PREFIX: ${OUTPUT_PREFIX}")

file(COPY "${CMAKE_CURRENT_SOURCE_DIR}/cmake/cpack/cpack_files/80-hasp.rules"
     DESTINATION "${OUTPUT_PREFIX}/")
file(COPY "${CMAKE_CURRENT_SOURCE_DIR}/cmake/cpack/cpack_files/Thorlabs.rules"
     DESTINATION "${OUTPUT_PREFIX}/")
file(COPY "${CMAKE_CURRENT_SOURCE_DIR}/cmake/cpack/cpack_files/create_md5.sh"
     DESTINATION "${OUTPUT_PREFIX}/")
file(COPY "${CMAKE_CURRENT_BINARY_DIR}/install.sh" DESTINATION "${OUTPUT_PREFIX}/")
file(COPY "${CMAKE_CURRENT_SOURCE_DIR}/doc/验证报告"
     DESTINATION "${OUTPUT_PREFIX}/doc/")
file(COPY "${CMAKE_CURRENT_SOURCE_DIR}/CHANGELOG.md"
     DESTINATION "${OUTPUT_PREFIX}/")
file(COPY "${CMAKE_CURRENT_SOURCE_DIR}/README.md"
     DESTINATION "${OUTPUT_PREFIX}/")