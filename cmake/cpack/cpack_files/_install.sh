#! /usr/bin/env bash
###############################################################################
# Copyright 2024 RoboSense All rights reserved.
# Suteng Innovation Technology Co., Ltd. www.robosense.ai

# This software is provided to you directly by RoboSense and might
# only be used to access RoboSense LiDAR. Any compilation,
# modification, exploration, reproduction and redistribution are
# restricted without RoboSense's prior consent.

# THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
# WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
# OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
# DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
# INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CO<PERSON><PERSON>Q<PERSON>NTIAL DAMAGES
# (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
# SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
# HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
# STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
# IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
# POSSIBILITY OF SUCH DAMAGE.
###############################################################################

PROGRAM_NAME="@PROJECT_NAME@"
IS_USE_QT_SERIALPORT="@USE_QT_SERIALPORT@"
IS_USE_QT_CONCURRENT="@USE_QT_CONCURRENT@"
IS_USE_QT_CHARTS="@USE_QT_CHARTS@"

# don's allow current folder path contain illegal character
dir_name=$(
  cd $(dirname $0)
  pwd
)
echo "Current path is: ${dir_name}"
if [[ "${dir_name}" =~ [^-\/\_\.a-zA-Z0-9] ]]; then
  echo -e "\033[31m Can not install in current path \033[0m"
  echo -e "\033[31m Please move to another path without illegal character \033[0m"
  echo -e "The path should only contain [a-zA-Z0-9_-./]"
  exit -1
fi

# don't allow to run this shell with sudo
if [[ $EUID -eq 0 ]]; then
  echo -e "\033[31m Error: don't use 'sudo' to run this shell, you should run it like: \033[0m"
  echo -e "\033[32m ./install.sh \033[0m"
  exit 1
fi

check_password() {
    echo "$1" | sudo -S true 2>/dev/null
    return $?
}
printf "[sudo] password for ${USER}: "
read -s PASSWORD
check_password "$PASSWORD"
if [ $? -ne 0 ]; then
    echo "The password is incorrect. Exiting."
    exit 1
fi

if md5sum -c ./md5sum.txt; then
  echo "md5sum is right, continue..."
else
  echo -e "\033[31m Error: md5sum is not right, install packages may be damaged, please re download the files. \033[0m"
  exit 1
fi

dir_name=$(
  cd $(dirname $0)
  pwd
)
echo "Current path change to: ${dir_name}"

# for usb key
echo ${PASSWORD} | sudo -S cp 80-hasp.rules /etc/udev/rules.d/
# for thorlabs optical power meter
echo ${PASSWORD} | sudo -S cp Thorlabs.rules /etc/udev/rules.d/

rule_file="/etc/udev/rules.d/80-rotator-control.rules"
# you need to update this rule when a new type of usb is introduced
# idVendor is the first ID in lsusb
# idProduct is the second one
echo "KERNEL==\"ttyUSB[0-9]*\", SUBSYSTEMS==\"usb\", ATTRS{idVendor}==\"067b\", ATTRS{idProduct}==\"2303\", MODE=\"0666\"" | sudo tee ${rule_file}
echo "KERNEL==\"ttyUSB[0-9]*\", SUBSYSTEMS==\"usb\", ATTRS{idVendor}==\"0403\", ATTRS{idProduct}==\"6001\", MODE=\"0666\"" | sudo tee -a ${rule_file}
echo "KERNEL==\"ttyUSB[0-9]*\", SUBSYSTEMS==\"usb\", ATTRS{idVendor}==\"0403\", ATTRS{idProduct}==\"6015\", MODE=\"0666\"" | sudo tee -a ${rule_file}

# usb_serial_rule_file="/etc/udev/rules.d/usb_serial.rules"
# you need to update this rule when a new type of usb is introduced
# idVendor is the first ID in lsusb
# idProduct is the second one
# echo "KERNEL==\"ttyUSB[0-9]*\", SUBSYSTEMS==\"usb\", ATTRS{idVendor}==\"1a86\", ATTRS{idProduct}==\"7523\", MODE=\"0666\"" | sudo tee ${usb_serial_rule_file}
# echo ${PASSWORD} | sudo -S chmod 666 usb_serial_rule_file？
sudo usermod -aG dialout $USER

echo ${PASSWORD} | sudo -S service udev reload
echo ${PASSWORD} | sudo -S service udev restart

# user add to tcpdump
sudo usermod -aG tcpdump $USER
sudo setcap cap_net_raw,cap_net_admin=eip /usr/sbin/tcpdump

if [ -n "$(find ${PWD}/ -maxdepth 1 -name ${PROGRAM_NAME}*.deb)" ]; then
  echo "find install deb, install it...."
  
  # 检查是否已经安装了该软件
  if dpkg -l | grep -q ${PROGRAM_NAME}; then
    echo ${PASSWORD} | sudo -S dpkg -r ${PROGRAM_NAME}
  fi

  sleep 6
  
  echo ${PASSWORD} | sudo -S dpkg -i ${PROGRAM_NAME}*.deb
else
  echo -e "\033[31m Error: can not find deb to install. \033[0m"
fi
