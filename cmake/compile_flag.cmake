﻿# Detect Windows platform and set Windows-specific definitions
if(WIN32)
  add_definitions(-D_WIN32_WINNT=0x0A00)
endif()

# Check if MSVC is being used
if(MSVC)
  message(STATUS "MSVC detected")

  # Set UTF-8 encoding
  add_compile_options("/utf-8")

  # Set compiler flags for release and debug configurations
  set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} /O2 /W3 /P")
  string(APPEND CMAKE_CXX_FLAGS_DEBUG "/Od /W3 /Zi")
else()
  message(STATUS "Non-MSVC compiler detected")

  # General compiler options
  add_compile_options(-Wall)

  # Set compiler flags for release and debug configurations
  string(APPEND CMAKE_CXX_FLAGS_RELEASE " -O3")
  string(APPEND CMAKE_CXX_FLAGS_DEBUG " -O0 -g3 -ggdb")

  # Check if the compiler is Clang and set Clang-specific flags
  if(CMAKE_CXX_COMPILER_ID MATCHES "Clang")
    message(STATUS "Clang compiler detected")
    string(APPEND CMAKE_CXX_FLAGS_DEBUG " -fstandalone-debug")
  endif()
endif()