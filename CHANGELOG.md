﻿# Changelog
## v3.20.9 2025-06-12
### Fix
- 修正支持0360连接

## v3.20.8 2025-06-12
### Fix
- 修正支持新crc的0351机器

## v3.20.7 2025-05-28
### Fix
- 修正割草版的标志位与寄存器

## v3.20.6 2025-05-26
### Feat
- 适配割草版本的处理流程，减少48线处理
- 增加标定版本号为`0x4`
- 增加0度角、回波初始化时候初始化的寄存器

## v3.20.5 2025-05-24
### Fix
- 修正积分值字段名重复，使得`mes`不完全显示的问题
- 积分值`NG`割草版本

## v3.20.4 2025-05-23
### Fix
- 修正测绘版积分值ng的问题

## v3.20.3 2025-05-22
### Fix
- 修正修正积分值被意外清除的问题

## v3.20.2 2025-05-22
### Fix
- 修正IP池连接时候端口出错的问题

## v3.20.1 2025-05-22
### Feat
- **协议适配**：更新通信协议以支持多项目场景（重大变更）
- **版本管控**：增加`mes`获取版本功能
- **雷达识别**：
  - 新增割草版雷达设备识别
  - 检测到非割草版雷达积分值`NG`时，自动切换至割草版积分值判定
- **积分值优化**：
  - 更新正装/侧装割草版积分标准
  - 支持积分值文件多版本标准显示
  - 优化文件名格式（包含标准名及进分支结果）

## v3.19.12 test 2025-05-12
### Feat
- 新增积分值标准，正装版、侧装版、测绘版
- 新增积分值卡控下限到文件
- 新增过程文件名包含正装侧装测绘版本字段
- 新增根据不同版本执行不同的积分值标准功能

### Fix
- 修正静标靶板真值

## v3.19.11 test 2025-05-09
### Fix
- 修正HHL缺少10m真值的问题

## v3.19.10 test 2025-05-08
### Fix
- 修正识别安装版本出错的问题

## v3.19.9 test 2025-05-08
### Fix
- 适配测绘版本雷达
- 当积分值`NG`也能继续往下走

## v3.19.8 test 2025-05-06
### Fix
- 修正零度角画图失败的问题

## v3.19.7 test 2025-05-06
### Fix
- 放宽积分值标准，最小积分值比例从0.55放宽到0.495
- 放宽通道积分值标准，从0.7放宽到0.63

## v3.19.6 2025-04-29
### Fix
- 优化动标数据延伸，通过添加10m、20m的10%、40%、90%数据，加入到动标当中去拟合

## v3.19.5 2025-04-28
### Fix
- 修正数据保存

## v3.19.4 2025-04-28
### Fix
- 添加根据迭代次数的配置表增加反射率数值
- 标定版本号增加到`0x03`

## v3.19.3 2025-04-25
### Fix
- 添加生成结果评估的报告输出

## v3.19.2 2025-04-25
### Fix
- 去除 3m 处反射率的 k 和 b 特殊处理

## v3.19.1 2025-04-24
### Fix
- 删除初始化时候的通道角写入功能

## v3.18.14 2025-04-22
### Fix
- 优化筛选20m靶板时忽略500以下的测距值
- 解决初始化过程中，关闭出现阻塞的问题
### Feat
- 添加相机

## v3.18.13 2025-04-21
### Fix
- 修正零度角标定失败也保存数据

## v3.18.12 2025-04-19
### Fix
- 零度角标定添加寄存器初始化关闭拖点去除算法

## v3.18.10 2025-04-19
### Fix
- 修复静标误差

## v3.18.9 2025-04-18
### Fix
- 更新1.4的通道角

## v3.18.8 2025-04-18
### Fix
- 增加积分值B类判断

## v3.18.7 2025-04-17
### Fix
- 放宽`SS`筛靶20m范围的最小值

## v3.18.5 2025-04-16
### Fix
- 去除回读校验

## v3.18.4 2025-04-17
### Feat
- 修正初始化寄存器校验失败不影响流程
- 增加重新加载配置文件不需要重启

## v3.18.3 2025-04-16
### Feat
- 初始化时增加两`459`寄存器的写入
- 增加寄存器回读校验

## v3.18.2 2025-04-15
### Fix
- 修正一次零度角读取参数失败的问题
- 修正断开连接失败时候再次重新连接会出现`invalid argument`的问题

## v3.18.1 2025-04-14
### Fix
- 更新适配4.12固件

## v3.17.5 2025-04-11
### Fix
- 修正回波之后零度角参数加载失败的问题

## v3.17.4 2025-04-09
### Fix
- 修正下调零度角标定的高反检测值

## v3.17.3 2025-04-08
### Fix
- 修正零度角高反范围计算中心，提高零度角的精度

## v3.17.2 2025-04-08
### Fix
- 修正获取网卡时候边缘的空格影响tcpdump抓包

## v3.17.1 2025-04-02
### Fix
- 去除10m高反经验值的调整
- 解决zero的标定包被自检包覆盖的问题
- 添加零度角新方案，通过高反拿到关键角度
- 修正tcpdump抓取的pcap,rs view无法解析的问题
- 添加零度角稳态误差，修正自检功能

## v3.16.13 2025-03-24
### Fix
- 修正面积分层导致的统计数据为空的问题

## v3.16.12 2025-03-18
### Fix
- 修正unique program

## v3.16.11 2025-03-18
### Feat
- 增加局域网扫描连接

## v3.16.10 2025-03-12
### Fix
- 修正HHL code0数据飘出的问题

## v3.16.9 2025-03-11
### Feat
- 增加ip池适配
- 修正HHL靶板配置

## v3.16.8 2025-03-05
### Feat
- 修正绝标超出范围的问题

## v3.16.7 2025-03-05
### Feat
- 增加积分值数据为0的时候窗口提示异常

## v3.16.6 2025-03-05
### Feat
- 增加反标远距离经验延伸
- 动标增加去除散点

## v3.16.5 2025-03-05
### Feat
- 增加vbd的斜率K的阈值开放

## v3.16.4 2025-02-25
### Feat
- 修正91~93通道的窜扰问题导致的取靶板中心数据为空的问题

## v3.16.3 2025-02-25
### Feat
- 修正积分值PASS类的判断

## v3.16.2 2025-02-24
### Feat
- 增加C类积分值标准

## v3.16.1 2025-02-24
### Feat
- 增加vbd crc的校验
- 增加vbd的sn自动设置

## v3.15.14 2025-02-21
### Fix
- 修正1.2m、0.2m和3m处不修正反射率
- 恢复1.2m与3m的筛靶位置
- 修正1.2m与3m使用均值面积作为反标

## v3.15.13 2025-02-21
### Fix
- 修正0.2m的反射率修正+500

## v3.15.12 2025-02-20
### Fix
- 修正只有>91通道才修正10m板

## v3.15.11 2025-02-20
### Fix
- 调整10m高反面积与幅值为固定值

## v3.15.10 2025-02-20
### Fix
- 修正画图错误

## v3.15.9 2025-02-20
### Fix
- 修正画图

## v3.15.8 2025-02-20
### Fix
- 修正幅值反标卡控20数值以上算非递增

## v3.15.7 2025-02-20
### Feat
- 新增支持靶板单独位置比例设定
- 修正去除弱信号边缘2%反射率靶板边缘数据，多去除0.01
- 1.27m和3m靶板的90选取为附近的最大值
- 增加1.27m中90反射率面积低于40的时候，采取40反射率面积+500的形式

### Fix
- 修正进度条显示问题
- 修正标定位数据取消时间命名

## v3.15.6 2025-02-19
### Fix
- 解决一键运行的时候vbd文件需要重新加载的问题

## v3.15.5 2025-02-19
### Feat
- 增加vbd 459曲线的多次读取
- 适配gdi写入的起始地址
### Fix
- 修正3m内不进行幅值反标的检查
- 取消积分值A、B类的区分

## v3.15.4 2025-02-18
### Feat
- 增加vbd转换和写入flash

## v3.15.3 2025-02-15
### Feat
- 增加全靶板的测距校验
- 增加0217版本固件的支持
### Fix
- 调整筛选靶板结束点的满足40%即可

## v3.15.2 2025-02-13
### Fix
- 修正标定结束后自动到上料口
- 修正标定结束后继电器断电

## v3.15.1 2025-02-13
### Feat
- 增加靶板数据`config`修改
- 增加`pcap`的文件名包含厂区名
- 增加根据`pcap`厂区名自动处理

## v3.14.2 2025-02-12
### Fixed
- 修正所有通道筛选出10m板失败，则不生成积分值文件
- 修正积分值类型B的判定
- 修正标定结束后恢复寄存器关闭发射

## v3.14.1 2025-02-10
### Feat
- 增加反标标定版本
- 增加反标`GDI`寄存器
- 增加初始化`vbd`截距的配置
- 增加积分值结果输出
- 增加幅值反标校验

## v3.13.6 2025-01-23
### Fixed
- 修正一键运行未勾选回波时候，不进行初始化
- 修正绝标画图
- 添加可选的动标角度补偿

## v3.13.5 2025-01-22
### Fixed
- 修正积分值NG的判定, 增加A和B与NG的区分
- 修正开放零度角阈值从300~308

## v3.13.4 2025-01-21
### Fixed
- 优化修正93通道增加串扰点的去除

## v3.13.3 2025-01-20
### Fixed
- 修正93通道去除20m串扰点

## v3.13.0 2025-01-18
### Fixed
- 修正绝标

## v3.12.5 2025-01-17
### Fixed
- 更新适配`SS`20m真值

## v3.12.4 2025-01-16
### Fixed
- 适配ss更换靶板
- 增加反射率递增检查

## v3.12.3 2025-01-15
### Fixed
- 开放反射率阈值

## v3.12.1 2025-01-15
### Fixed
- 增加bit文件单独写入
- 修正验证反标90和40的区分度

## v3.11.8 2025-01-13
### Fixed
- 修正ss靶板真值距离

## v3.11.7 2025-01-13
### Fixed
- 调整动静绝反保存的bit文件为大端存储

## v3.11.6 2025-01-13
### Fixed
- 优化`SS`的`d08`固件适配
- 根据新优化场地进行筛板适配

## v3.11.5 2025-01-13
### Fixed
- 修正零度角卡控阈值为303~305
- 修正每个页面都显示日志窗口

## v3.11.4 2025-01-13
### Fixed
- 修正靶板卡严

## v3.11.3 2025-01-12
### Fixed
- 修正积分值NG仍然继续标定

## v3.11.2 2025-01-12
### Fixed
- 修正一键运行未正确停止的问题
- 修正`SN`为空时，支持一键运行

## v3.11.1 2025-01-12
### Added
- 增加版本卡控

## v3.11.0 2025-01-12
### Fixed
- 修正深汕筛板阈值问题

## v3.10.0 2025-01-12
### Fixed
- 修正动标靶板筛选时候的弱信号少的问题
- 修正使用补偿距离来筛选靶板
- 修正动标补偿超限阈值

## v3.9.0 2025-01-11
### Added
- 增加显示零度角数据图片

## v3.8.4 2025-01-10
### Added
- 适配深汕靶板场地

## v3.8.3 2025-01-10
### Added
- 增加零度角的阈值卡控
- HHL零度角受治具影响，需要对标定结果修正90°

## v3.7.0 2025-01-09
### Added
- 增加零度角的阈值卡控
- HHL零度角受治具影响，需要对标定结果修正90°
### Fixed
- 修正反标只使用1编码

## v3.6.0 2025-01-09
### Fixed
- 修正反标只使用1编码

## v3.7.0 2025-01-09
### Fixed
- 修正标定时候的寄存器

## v3.5.0 2025-01-08
### Fixed
- 适配1.8日固件

## v3.4.2 2025-01-07
### Fixed
- 适配c06固件

## v3.4.1 2025-01-06
### Fixed
- 修正高反等效问题
- 修正bit文件对比显示问题

## v3.4.0 2025-01-06
### Fixed
- 修正一键运行时候单独零度角或回波状态机无法停止的问题
- 修正靶板距离范围

### Added
- 增加零度角的角度确认
- 增加48线通道角度的写入

## v3.3.2 2025-01-02
- 删除`B1`停止转台指令，修正为`A1`指令
- 增加转台默认速度

## v3.3.1 2025-01-02
- 删除1.5m绝标
- 增加北光转台收发时间

## v3.3.0 2024-12-27
- 修正转台速度
- 转台连接失败提示
- 增加1.5m绝标处理
- 修正结束时候光斑仍在靶板上，导致筛选不到结束点的问题

## v3.2.2 2024-12-26
- 修复ui崩溃的问题

## v3.2.1 2024-12-26
- 修正画图时pandas出错的问题

## v3.2.0 2024-12-25
- 修改`ui`框架为`MVC`的框架
- 增加日志库拷贝
- 更新优化状态机流程
- 更新获取参数方式
- 更新采集时候的光斑角度确认
- 修正静标图像显示范围

## v3.1.3 2024-12-23
- 增加SS的3m板末端修正

## v3.1.2 2024-12-23
- 增加10m优先筛选，供光学分析

## v3.1.1 2024-12-20
- 修正通过距离来筛选靶板

## v3.1.0 2024-12-17
### Feat
- 增加`20m` `10%`反射率的动标数据弱面积数据延伸
- 修改静标靶板选择使用为`3m`靶板
- 删除经验绝标

## v3.0.2 2024-12-16
### Feat
- 修正上传失败的问题

## v3.0.1 2024-12-10
### Feat
- 适配统一标定格式
- 增加经验绝标数据格式
- 增加绝标数据格式位置

## v2.9.2 2024-12-10
### Fixed
- 修正动标靶板筛选距离

## v2.9.1 2024-12-10
### Fixed
- 修正积分值`NG`却显示`PASS`的问题

## v2.9.0 2024-12-07
### Feat
- 增加反标中对静标和动标的补偿
- 增加根据动标靶板offset修正角度
- 增加动标曲线外推

## v2.8.0 2024-12-06
### Feat
- 适配HHL的B1雷达的标定

## v2.7.1 2024-12-06
### Feat
- 增加根据转台角度筛选靶板
- 恢复错误日志显示
- 增加积分值NG的时候

### Fixed
- 修正失败显示
- 修正多个失败未显示的问题

## v2.7.0 2024-12-04
### Feat
- 增加转台上料口，查光斑口
- 增加标定运行的状态显示

## v2.6.0 2024-12-03
### Feat
- 修正通过角度来筛选靶板

## v2.5.1 2024-12-03
### Fixed
- 适配了B1雷达

## v2.4.2 2024-11-29
### Fixed
- 修正连接时候等待顶板版本

## v2.4.1 2024-11-29
### Fixed
- 修正连接连接时候获取msop超时的问题

## v2.4.0 2024-11-28
### Fixed
- 连接后等待接收到msop包再完成连接

## v2.3.5 2024-11-21
### Fixed
- 适配903固件，适配ss量产场地

# Changelog
## v2.3.4 2024-11-20
### Fixed
- 更新适配反标距离，修正动标靶板筛选处的异常数值

# Changelog
## v2.3.3 2024-11-18
### Fixed
- 去除负限位

# Changelog
## v2.3.1 2024-11-15
### Fixed
- 增加crc校验

# Changelog
## v2.3.0 2024-11-15
### Fixed
- 修复参数更新失败问题，修复获取角度失败的问题

## v2.2.3 2024-11-14
### Fixed
- 修复参数更新失败问题，修复获取角度失败的问题

## v2.2.2 2024-11-14
### Fixed
- 增加转台回零功能，增加获取角度功能

## v2.2.0 2024-11-12
### Fixed
- 修正零度角和回波连接冲突的问题

## v2.1.7 2024-11-12
### Fixed
- 调整去除202e和20ab寄存器

## v2.1.6 2024-11-12
### Added
- 增加继电器
- 减慢asd转台的速度

## v2.1.4 2024-11-08
### Added
- 增加动标数据超出靶板数据的特殊处理

## v2.1.3 2024-11-08
### Added
- 增加转台手动控制界面

## v2.1.2 2024-11-08
### Added
- 增加转台类型适配

## v2.1.1 2024-11-08
### Fixed
- 防止动标拟合崩溃的问题

## v2.1.0 2024-11-07
### Added
- 添加转台类型选择

## v2.0.6 2024-11-07
### Added
- 增加处理，防止动标拟合崩溃

## v2.0.5 2024-11-07
### Added
- 增加打包cpack

## v0.3.0 2024-10-14
### Added
- 初版版本
