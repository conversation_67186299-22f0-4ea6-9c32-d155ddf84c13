﻿cmake_minimum_required(VERSION 3.15.0)
cmake_policy(SET CMP0048 NEW)

project(rotator_driver VERSION 3.0.6)
# add_compile_options(-std=c++11)

set(CMAKE_BUILD_TYPE RelWithDebInfo)

find_package(
  Qt5
  COMPONENTS Widgets <PERSON><PERSON>Port
  REQUIRED)
add_definitions(-DQT_NO_KEYWORDS -g)

set(CMAKE_EXPORT_COMPILE_COMMANDS ON)
option(BUILD_ROTATOR_DRIVER_EXAMPLE "build example or not" OFF)
option(BUILD_ROTATOR_DRIVER_QT_EXAMPLE "build qt example or not" OFF)
option(BUILD_ROTATOR_DRIVER_TEST "build gtest or not" OFF)

add_definitions(-DCURRENT_PROJECT_NAME="${CMAKE_PROJECT_NAME}")

find_package(Git QUIET)
execute_process(COMMAND ${GIT_EXECUTABLE} config core.hooksPath .githooks
                WORKING_DIRECTORY "${CMAKE_CURRENT_SOURCE_DIR}")

set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)
set(CMAKE_AUTOUIC ON)

include(cmake/FindRSFSCLog.cmake)

# =========================
# Create Object Library
# =========================
qt5_wrap_cpp(MOC_FILES_MC600_ROTATOR_CONTROLLER include/mc600_rotator_controller/mc600_rotator_controller.h)

set(SOURCE_FILES
    src/asd_rotator_controller/asd_rotator_controller.cpp
    src/bocic_rotator_controller/bocic_rotator_controller_command.cpp
    src/bocic_rotator_controller/bocic_rotator_controller.cpp
    src/bocic_rotator_controller/bocic_rotator.cpp
    src/cl_01a_rotator_controller/cl_01a_rotator_controller.cpp
    src/mc600_rotator_controller/mc600_rotator_controller.cpp
    src/rotator_controller_factory.cpp
    src/rotator_controller_monitor.cpp
    src/rotator_controller_share_data.cpp
    include/rotator_controller_monitor.h
    include/share_data_interface.h
    include/rotator_view/rotator_control_view.h
    src/rotator_view/rotator_control_view.cpp
    ${MOC_FILES_MC600_ROTATOR_CONTROLLER})

add_library(rotator_driver_object OBJECT ${SOURCE_FILES})
target_include_directories(rotator_driver_object PUBLIC include)
target_include_directories(rotator_driver_object PRIVATE src)
target_include_directories(rotator_driver_object PRIVATE SYSTEM ${Qt5SerialPort_INCLUDE_DIRS}
                                                         ${Qt5Widgets_INCLUDE_DIRS})
target_compile_options(rotator_driver_object PUBLIC -fPIC)

# =========================
# Create Shared Library
# =========================
add_library(rotator_driver_shared SHARED $<TARGET_OBJECTS:rotator_driver_object>)
target_include_directories(rotator_driver_shared PUBLIC include)
set_target_properties(rotator_driver_shared PROPERTIES VERSION ${PROJECT_VERSION} OUTPUT_NAME rotator_driver)
target_link_libraries(rotator_driver_shared Qt5::SerialPort Qt5::Core Qt5::Widgets)

# =========================
# Create Static Library
# =========================
add_library(rotator_driver_static STATIC $<TARGET_OBJECTS:rotator_driver_object>)
target_include_directories(rotator_driver_static PUBLIC include)
set_target_properties(rotator_driver_static PROPERTIES VERSION ${PROJECT_VERSION} OUTPUT_NAME rotator_driver)
target_link_libraries(rotator_driver_static Qt5::SerialPort Qt5::Core Qt5::Widgets)

# =========================
# Create Example
# =========================
if(BUILD_ROTATOR_DRIVER_EXAMPLE)
  add_subdirectory(example)
endif(BUILD_ROTATOR_DRIVER_EXAMPLE)

# =========================
# Create Unit Test
# =========================
if(BUILD_ROTATOR_DRIVER_TEST)
  add_subdirectory(test)
endif(BUILD_ROTATOR_DRIVER_TEST)
