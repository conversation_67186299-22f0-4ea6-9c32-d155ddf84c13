﻿/******************************************************************************
 * Copyright 2022 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#include "cl_01a_rotator_controller/cl_01a_rotator_controller.h"
#include <array>
#include <cstddef>
#include <string>
#include <thread>
#if __has_include("rsfsc_lib/include/rsfsc_log.h")
#  include "rsfsc_lib/include/rsfsc_log.h"
#elif __has_include("rsfsc_lib/rsfsc_log.h")
#  include "rsfsc_lib/rsfsc_log.h"
#elif __has_include("rsfsc_log/rsfsc_log.h")
#  include "rsfsc_log/rsfsc_log.h"
#else
#  include "rsfsc_log.h"
#endif
namespace robosense
{
namespace lidar
{
Cl01ARotatorController::Cl01ARotatorController(const std::string& _port_name) :
  serial_port_(new QSerialPort()), speed_(400), min_range_(-5), max_range_(350)
{
  serial_port_->setPortName(_port_name.data());
}

Cl01ARotatorController::~Cl01ARotatorController()
{
  disconnect();
  if (serial_port_ != nullptr)
  {
    delete serial_port_;
    serial_port_ = nullptr;
  }
}

bool Cl01ARotatorController::connect()
{
  if (serial_port_ == nullptr)
  {
    return false;
  }
  if (!serial_port_->isOpen())
  {
    if (serial_port_->open(QIODevice::ReadWrite))
    {
      serial_port_->setBaudRate(QSerialPort::Baud115200);
      serial_port_->setDataBits(QSerialPort::Data8);
      serial_port_->setStopBits(QSerialPort::OneStop);
      serial_port_->setFlowControl(QSerialPort::NoFlowControl);
      serial_port_->setParity(QSerialPort::NoParity);
      serial_port_->clearError();
      serial_port_->clear();
      RSFSCLog::getInstance()->info(u8"连接串口成功");
      return true;
    }
    RSFSCLog::getInstance()->error(u8"打开串口失败");
    return false;
  }
  RSFSCLog::getInstance()->info(u8"串口已连接");
  return true;
}

bool Cl01ARotatorController::disconnect()
{
  if (serial_port_ == nullptr)
  {
    return false;
  }
  if (serial_port_->isOpen())
  {
    serial_port_->clear();
    serial_port_->close();
    RSFSCLog::getInstance()->info(u8"关闭串口成功");
    return true;
  }
  RSFSCLog::getInstance()->info(u8"串口未连接");
  return true;
}

bool Cl01ARotatorController::addRotator(const RotatorAxis /*_axis*/,
                                        const std::string& /*_rotator_type*/,
                                        bool /*_use_zero_limit*/)
{
  RSFSCLog::getInstance()->error(u8"当前控制器不支持");
  return false;
}

bool Cl01ARotatorController::rotateAngle(const float /*_angle_in_degree*/, const RotatorAxis /*_axis*/)
{
  RSFSCLog::getInstance()->error(u8"当前控制器不支持");
  return false;
}

bool Cl01ARotatorController::rotateTwoAxis(const float /*_angle_in_degree1*/,
                                           const RotatorAxis /*_axis1*/,
                                           const float /*_angle_in_degree2*/,
                                           const RotatorAxis /*_axis2*/)
{
  RSFSCLog::getInstance()->error(u8"当前控制器不支持");
  return false;
}

bool Cl01ARotatorController::getCurrentAngle(float& /*_angle_in_degree*/, const RotatorAxis /*_axis*/)
{
  RSFSCLog::getInstance()->error(u8"当前控制器不支持");
  return false;
}

bool Cl01ARotatorController::move(const float _position_in_millimeter, const RotatorAxis /*_axis*/)
{
  if (!serial_port_->isOpen())
  {
    RSFSCLog::getInstance()->error(u8"串口未连接");
    return false;
  }
  uint64_t pluse = 0;
  if (_position_in_millimeter < 0)
  {
    pluse = 0x100000000 + static_cast<int>(_position_in_millimeter);
  }
  else
  {
    pluse = static_cast<uint64_t>(_position_in_millimeter);
  }
  unsigned char speed_h = speed_ / 0x100;
  unsigned char speed_l = speed_ % 0x100;
  unsigned char step4   = pluse / 0x1000000;
  unsigned char step3   = pluse / 0x10000 % 0x100;
  unsigned char step2   = pluse / 0x100 % 0x100;
  unsigned char step1   = pluse % 0x100;

  std::array<const unsigned char, 10> cmd = { 0x55, 0xaa, 0x07, speed_l, speed_h, step1, step2, step3, step4, 0xc3 };
  QByteArray send_data;
  send_data.resize(10);
  for (int i = 0; i < 10; i++)
  {
    send_data[i] = static_cast<char>(cmd.at(i));
  }
  writeCommand(send_data);
  return true;
}

bool Cl01ARotatorController::getCurrentPosition(float& _position_in_millimeter, const RotatorAxis /*_axis*/)
{
  if (!serial_port_->isOpen())
  {
    RSFSCLog::getInstance()->error(u8"串口未连接");
    return false;
  }
  std::array<const unsigned char, 10> cmd = { 0x55, 0xaa, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc3 };
  QByteArray send_data;
  send_data.resize(10);
  for (int i = 0; i < 10; i++)
  {
    send_data[i] = static_cast<char>(cmd.at(i));
  }
  writeCommand(send_data);
  QByteArray receive_data = readData();
  char* char_position     = new char[4] { receive_data[3], receive_data[2], receive_data[1], receive_data[0] };
  _position_in_millimeter = static_cast<float>(*reinterpret_cast<int*>(char_position));  //NOLINT
  return true;
}

bool Cl01ARotatorController::resetZeroPosition(const RotatorAxis _axis) { return move(0, _axis); }

bool Cl01ARotatorController::stopRotator(const RotatorAxis /*_axis*/)
{
  if (!serial_port_->isOpen())
  {
    RSFSCLog::getInstance()->error(u8"串口未连接");
    return false;
  }
  std::array<const unsigned char, 10> cmd = { 0x55, 0xaa, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc3 };
  QByteArray send_data;
  send_data.resize(10);
  for (int i = 0; i < 10; i++)
  {
    send_data[i] = static_cast<char>(cmd.at(i));
  }
  return true;
}

bool Cl01ARotatorController::setRotatorSpeed(const int _speed, const RotatorAxis /*_axis*/)
{
  if (!serial_port_->isOpen())
  {
    RSFSCLog::getInstance()->error(u8"串口未连接");
    return false;
  }
  speed_ = _speed < MIN_SPEED ? MIN_SPEED : _speed;
  speed_ = _speed > MAX_SPEED ? MAX_SPEED : _speed;
  return true;
}

bool Cl01ARotatorController::isMoving(bool& _is_moving, const RotatorAxis /*_axis*/)
{
  if (!serial_port_->isOpen())
  {
    RSFSCLog::getInstance()->error(u8"串口未连接");
    return false;
  }
  _is_moving                              = true;
  std::array<const unsigned char, 10> cmd = { 0x55, 0xaa, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc3 };
  QByteArray send_data;
  send_data.resize(10);
  for (int i = 0; i < 10; i++)
  {
    send_data[i] = static_cast<char>(cmd.at(i));
  }
  writeCommand(send_data);
  QByteArray receive_data = readData();
  if (receive_data.size() == 8)
  {
    if (receive_data[7] == static_cast<char>(0x20) || receive_data[7] == static_cast<char>(0))
    {
      _is_moving = false;
    }
    return true;
  }
  RSFSCLog::getInstance()->error(u8"获取电机停止状态失败");
  return false;
}
// NOLINTNEXTLINE(bugprone-easily-swappable-parameters)
bool Cl01ARotatorController::setSoftLimit(const float /*_min_range*/,
                                          const float /*_max_range*/,
                                          const RotatorAxis /*_axis*/)
{
  // TODO: implement this function
  RSFSCLog::getInstance()->error(u8"此类型"
                                 "转台尚未实现软限位功能");
  return false;
}
bool Cl01ARotatorController::setAcceleration(const int _acc, const RotatorAxis _axis)
{
  // TODO: implement this function
  RSFSCLog::getInstance()->error(u8"此类型转台尚未实现加速度设置功能");
  return false;
}
bool Cl01ARotatorController::writeCommand(const QByteArray& _command)
{
  std::string print_info;
  for (char value : _command)
  {
    print_info += std::to_string(static_cast<uint8_t>(value));
    print_info += " ";
  }
  RSFSCLog::getInstance()->debug(u8"上位机写入命令[" + print_info + "]");
  serial_port_->write(_command);
  return serial_port_->waitForBytesWritten(1000);
}

QByteArray Cl01ARotatorController::readData()
{
  std::this_thread::sleep_for(std::chrono::milliseconds(200));
  QByteArray data;
  if (serial_port_->waitForReadyRead(1000))
  {
    data = serial_port_->readAll();
    std::string print_info;
    for (auto value : data)
    {
      print_info += std::to_string(static_cast<uint8_t>(value));
      print_info += " ";
    }
    RSFSCLog::getInstance()->debug(u8"电机控制器回复[" + print_info + "]");
  }
  return data;
}
}  // namespace lidar
}  // namespace robosense