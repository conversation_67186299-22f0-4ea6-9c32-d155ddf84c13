﻿/******************************************************************************
 * Copyright 2023 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#include "rotator_view/rotator_control_view.h"
#include "bocic_rotator_controller/bocic_rotator.h"
#include "qboxlayout.h"
#include "qdialog.h"
#include "qfont.h"
#include "qlabel.h"
#include "qpushbutton.h"
#include "qvalidator.h"
#include "rotator_controller_factory.h"
#include "rotator_controller_share_data.h"
#if __has_include("rsfsc_lib/include/rsfsc_log.h")
#  include "rsfsc_lib/include/rsfsc_log.h"
#elif __has_include("rsfsc_lib/rsfsc_log.h")
#  include "rsfsc_lib/rsfsc_log.h"
#elif __has_include("rsfsc_log/rsfsc_log.h")
#  include "rsfsc_log/rsfsc_log.h"
#else
#  include "rsfsc_log.h"
#endif
#include <QtCore/QSettings>
#include <QtWidgets/QComboBox>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QVBoxLayout>
#include <string>
namespace robosense
{
namespace lidar
{
RotatorControlView::RotatorControlView(int _thread_id, QWidget* _parent) :
  QDialog(_parent),
  combobox_rotator_controller_type_(new QComboBox(this)),
  combobox_rotator_type_(new QComboBox(this)),
  lineedit_com_name_(new QLineEdit(this)),
  pushbutton_connect_(new QPushButton(u8"连接", this)),
  pushbutton_reset_(new QPushButton(u8"复位", this)),
  pushbutton_set_speed_(new QPushButton(u8"设置速度为：", this)),
  lineedit_speed_(new QLineEdit(this)),
  pushbutton_move_(new QPushButton(u8"移动至", this)),
  lineedit_aim_point_(new QLineEdit(this)),
  thread_id_(_thread_id),
  controller_type_(RotatorControllerFactory::RotatorControllerType::UNKNOW)
{
  setting_file_name_ = QString("%1/setting_rotator_driver").arg(CURRENT_PROJECT_NAME);
  initLayout();
  connectAllWidget();
  readSetting();
}

RotatorControlView::~RotatorControlView()
{
  writeSetting();
  if (monitor_ != nullptr)
  {
    monitor_->stop();
  }
}

void RotatorControlView::initLayout()
{
  QLabel* label_title = new QLabel(this);
  QString title       = QString(u8"转台%1手动控制界面").arg(thread_id_);
  label_title->setText(title);
  QFont title_font;
  title_font.setBold(true);
  title_font.setPointSize(14);
  label_title->setFont(title_font);

  QHBoxLayout* layout_info = new QHBoxLayout;
  layout_info->addWidget(new QLabel(u8"控制器类型：", this));
  QStringList controller_type = {};
  for (const auto* type_name : RotatorControllerFactory::ROTATOR_CONTROLLER_TYPE_NAME)
  {
    controller_type << type_name;
  }
  combobox_rotator_controller_type_->addItems(controller_type);
  layout_info->addWidget(combobox_rotator_controller_type_);
  layout_info->addWidget(new QLabel(u8"电机类型：", this));
  layout_info->addWidget(combobox_rotator_type_);
  layout_info->addWidget(new QLabel(u8"串口号：", this));
  layout_info->addWidget(lineedit_com_name_);

  QHBoxLayout* layout_function = new QHBoxLayout;
  layout_function->addWidget(pushbutton_connect_);
  layout_function->addWidget(pushbutton_reset_);
  layout_function->addWidget(pushbutton_set_speed_);
  lineedit_speed_->setValidator(new QIntValidator(lineedit_speed_));
  layout_function->addWidget(lineedit_speed_);
  layout_function->addWidget(new QLabel(u8"pulses/s"));
  layout_function->addWidget(pushbutton_move_);
  lineedit_aim_point_->setValidator(new QDoubleValidator(lineedit_aim_point_));
  layout_function->addWidget(lineedit_aim_point_);
  layout_function->addWidget(new QLabel(u8"mm/°"));

  QVBoxLayout* layout_main = new QVBoxLayout;
  layout_main->addWidget(label_title);
  layout_main->addLayout(layout_info);
  layout_main->addLayout(layout_function);

  this->setLayout(layout_main);
  updateAllWidget();
}

void RotatorControlView::connectAllWidget()
{
  connect(combobox_rotator_controller_type_, static_cast<void (QComboBox::*)(int)>(&QComboBox::currentIndexChanged),
          this, &RotatorControlView::slotUpdateRotatorController);
  connect(pushbutton_connect_, &QPushButton::clicked, this, &RotatorControlView::slotConnectController);
  connect(pushbutton_reset_, &QPushButton::clicked, this, &RotatorControlView::slotResetRotator);
  connect(pushbutton_set_speed_, &QPushButton::clicked, this, &RotatorControlView::slotSetRotatorSpeed);
  connect(pushbutton_move_, &QPushButton::clicked, this, &RotatorControlView::slotRotateRotator);
  connect(this, &RotatorControlView::signalStopMove, this, &RotatorControlView::slotStopMoveEnableWidget);
}

void RotatorControlView::updateAllWidget()
{
  if (pushbutton_connect_->text() == u8"连接")
  {
    combobox_rotator_controller_type_->setEnabled(true);
    combobox_rotator_type_->setEnabled(true);
    lineedit_com_name_->setEnabled(true);
    pushbutton_reset_->setEnabled(false);
    pushbutton_set_speed_->setEnabled(false);
    lineedit_speed_->setEnabled(false);
    pushbutton_move_->setEnabled(false);
    lineedit_aim_point_->setEnabled(false);
  }
  else
  {
    combobox_rotator_controller_type_->setEnabled(false);
    combobox_rotator_type_->setEnabled(false);
    lineedit_com_name_->setEnabled(false);
    pushbutton_reset_->setEnabled(true);
    pushbutton_set_speed_->setEnabled(true);
    lineedit_speed_->setEnabled(true);
    pushbutton_move_->setEnabled(true);
    lineedit_aim_point_->setEnabled(true);
  }
}

void RotatorControlView::startMoveDisableWidget() { moveWidgetEnable(false); }

void RotatorControlView::readSetting()
{
  RSFSCLog::getInstance()->info(u8"当前转台操作读取配置文件路径：" + setting_file_name_.toStdString());
  QSettings settings("RoboSense", setting_file_name_);
  combobox_rotator_controller_type_->setCurrentIndex(
    settings.value(("rotator_controller_type" + std::to_string(thread_id_)).data(), "0").toInt());
  combobox_rotator_type_->setCurrentIndex(
    settings.value(("rotator_type" + std::to_string(thread_id_)).data(), "-1").toInt());
  lineedit_com_name_->setText(settings.value(("com_name" + std::to_string(thread_id_)).data(), "rsUSB0").toString());
}

void RotatorControlView::writeSetting()
{
  RSFSCLog::getInstance()->info(u8"当前转台操作写入配置文件路径：" + setting_file_name_.toStdString());
  QSettings settings("RoboSense", setting_file_name_);
  settings.setValue(("rotator_controller_type" + std::to_string(thread_id_)).data(),
                    combobox_rotator_controller_type_->currentIndex());
  settings.setValue(("rotator_type" + std::to_string(thread_id_)).data(), combobox_rotator_type_->currentIndex());
  settings.setValue(("com_name" + std::to_string(thread_id_)).data(), lineedit_com_name_->text());
}

void RotatorControlView::moveWidgetEnable(bool _status)
{
  pushbutton_connect_->setEnabled(_status);
  pushbutton_reset_->setEnabled(_status);
  pushbutton_set_speed_->setEnabled(_status);
  lineedit_speed_->setEnabled(_status);
  pushbutton_move_->setEnabled(_status);
  lineedit_aim_point_->setEnabled(_status);
}

void RotatorControlView::slotUpdateRotatorController(int _index)
{
  combobox_rotator_type_->clear();
  controller_type_ = static_cast<RotatorControllerFactory::RotatorControllerType>(_index);
  if (controller_type_ >= RotatorControllerFactory::RotatorControllerType::SC101 &&
      controller_type_ < RotatorControllerFactory::RotatorControllerType::UNKNOW)
  {
    QStringList rotator_type = {};
    for (const auto* type_name : BocicRotator::BOCIC_ROTATOR_MODEL_NAME)
    {
      rotator_type << type_name;
    }
    combobox_rotator_type_->addItems(rotator_type);
  }
}

void RotatorControlView::slotConnectController()
{
  pushbutton_connect_->setEnabled(false);
  if (pushbutton_connect_->text() == u8"连接")
  {
    std::string rotator_type = combobox_rotator_type_->currentText().toStdString();
    std::string com_name     = lineedit_com_name_->text().toStdString();
    monitor_.reset(new RotatorControllerMonitor(thread_id_, controller_type_, com_name));
    monitor_->start();
    RotatorControllerShareData::getInstance()->addRotatorShareData(thread_id_);
    if (!RotatorControllerShareData::getInstance()->connect(thread_id_))
    {
      RSFSCLog::getInstance()->error(u8"连接电机控制器失败，请检查当前配置与硬件连接是否正常");
      pushbutton_connect_->setEnabled(true);
      return;
    }
    if (controller_type_ >= RotatorControllerFactory::RotatorControllerType::SC101 &&
        controller_type_ < RotatorControllerFactory::RotatorControllerType::UNKNOW)
    {
      if (!RotatorControllerShareData::getInstance()->addRotator(thread_id_, RotatorControllerInterface::RotatorAxis::X,
                                                                 rotator_type, true))
      {
        RSFSCLog::getInstance()->error(u8"添加电机失败，请检查当前配置与硬件连接是否正常");
        RotatorControllerShareData::getInstance()->disconnect(thread_id_);
        pushbutton_connect_->setEnabled(true);
        return;
      }
    }
    pushbutton_connect_->setText(u8"断开");
  }
  else
  {
    if (!RotatorControllerShareData::getInstance()->disconnect(thread_id_))
    {
      RSFSCLog::getInstance()->error(u8"断开电机控制器连接失败，请检查当前配置与硬件连接是否正常");
      pushbutton_connect_->setEnabled(true);
      return;
    }
    monitor_.reset();
    pushbutton_connect_->setText(u8"连接");
  }
  pushbutton_connect_->setEnabled(true);
  updateAllWidget();
}

void RotatorControlView::slotResetRotator()
{
  startMoveDisableWidget();
  QThread* thread = QThread::create([=]() {
    int rotator_request_id = RotatorControllerShareData::getInstance()->resetZeroPosition(thread_id_);
    if (!RotatorControllerShareData::getInstance()->getIsSuccessByID(thread_id_, rotator_request_id))
    {
      RSFSCLog::getInstance()->error(u8"复位电机失败");
      signalStopMove();
      return;
    }
    bool is_moving = true;
    while (is_moving)
    {
      RotatorControllerShareData::getInstance()->isMoving(thread_id_, is_moving);
      std::this_thread::sleep_for(std::chrono::milliseconds(200));
    }
    signalStopMove();
  });
  connect(thread, &QThread::finished, thread, &QThread::deleteLater);
  thread->start();
}

void RotatorControlView::slotSetRotatorSpeed()
{
  int speed = lineedit_speed_->text().toInt();
  if (!RotatorControllerShareData::getInstance()->setRotatorSpeed(thread_id_, speed))
  {
    RSFSCLog::getInstance()->error(u8"设置电机运动速度失败");
  }
}

void RotatorControlView::slotRotateRotator()
{
  startMoveDisableWidget();
  float aim_point = lineedit_aim_point_->text().toFloat();
  QThread* thread = QThread::create([=]() {
    int rotator_request_id = 0;
    if (controller_type_ == RotatorControllerFactory::RotatorControllerType::CL_01A)
    {
      rotator_request_id = RotatorControllerShareData::getInstance()->move(thread_id_, aim_point);
    }
    else
    {
      rotator_request_id = RotatorControllerShareData::getInstance()->rotateAngle(thread_id_, aim_point);
    }
    if (!RotatorControllerShareData::getInstance()->getIsSuccessByID(thread_id_, rotator_request_id))
    {
      RSFSCLog::getInstance()->error(u8"电机运行失败");
      signalStopMove();
      return;
    }
    bool is_moving = true;
    while (is_moving)
    {
      RotatorControllerShareData::getInstance()->isMoving(thread_id_, is_moving);
      std::this_thread::sleep_for(std::chrono::milliseconds(200));
    }
    signalStopMove();
  });
  connect(thread, &QThread::finished, thread, &QThread::deleteLater);
  thread->start();
}

void RotatorControlView::slotStopMoveEnableWidget() { moveWidgetEnable(true); }
}  // namespace lidar
}  // namespace robosense