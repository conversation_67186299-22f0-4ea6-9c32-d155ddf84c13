﻿/******************************************************************************
 * Copyright 2022 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#include "rotator_controller_factory.h"
#include "asd_rotator_controller/asd_rotator_controller.h"
#include "bocic_rotator_controller/bocic_rotator_controller.h"
#include "cl_01a_rotator_controller/cl_01a_rotator_controller.h"
#include "mc600_rotator_controller/mc600_rotator_controller.h"
#include <map>
// NOLINTNEXTLINE(modernize-concat-nested-namespaces)
namespace robosense
{
namespace lidar
{
#if __cplusplus < 201703L
constexpr std::array<const char*, RotatorControllerFactory::RotatorControllerType::UNKNOW>
  RotatorControllerFactory::ROTATOR_CONTROLLER_TYPE_NAME;
#endif
RotatorControllerInterface* RotatorControllerFactory::createRotatorController(
  RotatorControllerType _type,
  std::initializer_list<std::string> _hardware_param)
{
  std::string port_name;
  if (_hardware_param.size() == 1)
  {
    port_name = *_hardware_param.begin();
  }
  switch (_type)
  {
  case ASD: return new AsdRotatorController(port_name);
  case CL_01A: return new Cl01ARotatorController(port_name);
  case SC101:
  case SC111:
  case SC121: return new BocicRotatorController(port_name, 1);
  case SC102:
  case SC112:
  case SC122: return new BocicRotatorController(port_name, 2);
  case SC103:
  case SC113:
  case SC123: return new BocicRotatorController(port_name, 3);
  case MC600: return new MC600RotatorController(port_name, 4);
  case UNKNOW: break;
  }
  return nullptr;
}

RotatorControllerFactory::RotatorControllerType RotatorControllerFactory::stringTypeToEnum(
  const std::string& _string_type)
{
  std::map<std::string, RotatorControllerFactory::RotatorControllerType> type_map = {
    { "ASD", RotatorControllerFactory::RotatorControllerType::ASD },
    { "CL_01A", RotatorControllerFactory::RotatorControllerType::CL_01A },
    { "SC101", RotatorControllerFactory::RotatorControllerType::SC101 },
    { "SC102", RotatorControllerFactory::RotatorControllerType::SC102 },
    { "SC103", RotatorControllerFactory::RotatorControllerType::SC103 },
    { "SC111", RotatorControllerFactory::RotatorControllerType::SC111 },
    { "SC112", RotatorControllerFactory::RotatorControllerType::SC112 },
    { "SC113", RotatorControllerFactory::RotatorControllerType::SC113 },
    { "SC121", RotatorControllerFactory::RotatorControllerType::SC121 },
    { "SC122", RotatorControllerFactory::RotatorControllerType::SC122 },
    { "SC123", RotatorControllerFactory::RotatorControllerType::SC123 },
    { "MC600", RotatorControllerFactory::RotatorControllerType::MC600 },
  };
  if (type_map.find(_string_type) == type_map.end())
  {
    return RotatorControllerFactory::RotatorControllerType::UNKNOW;
  }
  return type_map[_string_type];
}
}  // namespace lidar
}  // namespace robosense