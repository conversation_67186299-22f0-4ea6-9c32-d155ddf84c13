﻿/******************************************************************************
 * Copyright 2022 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#include "bocic_rotator_controller/bocic_rotator.h"
#include <map>
// NOLINTNEXTLINE(modernize-concat-nested-namespaces)
namespace robosense
{
namespace lidar
{
#if __cplusplus < 201703L
constexpr std::array<const char*, BocicRotator::BocicRotatorModel::UNKNOW> BocicRotator::BOCIC_ROTATOR_MODEL_NAME;
#endif
BocicRotator::BocicRotator(const BocicRotatorModel _rotator_type, bool _use_zero_limit) :
  use_zero_limit_(_use_zero_limit)
{
  switch (_rotator_type)
  {
  case MRS101:
  {
    type_               = BocicRotatorType::ROTATION_STAGE;
    subdivision_number_ = 8;
    has_zero_limit_     = true;
    min_range_          = 0.0F;
    max_range_          = 350.0F;
    transmission_ratio_ = 90;
    max_speed_          = 50;
    break;
  }
  case MRS102:
  {
    type_               = BocicRotatorType::ROTATION_STAGE;
    subdivision_number_ = 8;
    has_zero_limit_     = true;
    min_range_          = 0.0F;
    max_range_          = 350.0F;
    transmission_ratio_ = 180;
    max_speed_          = 25;
    break;
  }
  case MRS103:
  {
    type_               = BocicRotatorType::ROTATION_STAGE;
    subdivision_number_ = 8;
    has_zero_limit_     = true;
    min_range_          = 0.0F;
    max_range_          = 350.0F;
    transmission_ratio_ = 180;
    max_speed_          = 15;
    break;
  }
  case MRS104:
  {
    type_               = BocicRotatorType::ROTATION_STAGE;
    subdivision_number_ = 8;
    has_zero_limit_     = true;
    min_range_          = 0.0F;
    max_range_          = 350.0F;
    transmission_ratio_ = 270;
    max_speed_          = 12.5;
    break;
  }
  case MRS105:
  {
    type_               = BocicRotatorType::ROTATION_STAGE;
    subdivision_number_ = 8;
    has_zero_limit_     = true;
    min_range_          = 0.0F;
    max_range_          = 350.0F;
    transmission_ratio_ = 720;
    max_speed_          = 6;
    break;
  }
  case MRS402:
  {
    type_               = BocicRotatorType::ROTATION_STAGE;
    subdivision_number_ = 8;
    has_zero_limit_     = true;
    min_range_          = 0.0F;
    max_range_          = 350.0F;
    transmission_ratio_ = 90;
    max_speed_          = 15;
    break;
  }
  // 由于实际零位不在水平倾角上，最小角度和最大角度分别由-29.9与29.9设置为0与60
  // TODO: [修改MGC系列其他电机角度限制，实际限制以实物为准]
  case MGC102:
  {
    type_               = BocicRotatorType::ROTATION_STAGE;
    subdivision_number_ = 8;
    has_zero_limit_     = true;
    min_range_          = 0.0F;
    max_range_          = 30.0F;
    transmission_ratio_ = 494;
    max_speed_          = 15;
    break;
  }
  case MGC103:
  {
    type_               = BocicRotatorType::ROTATION_STAGE;
    subdivision_number_ = 8;
    has_zero_limit_     = false;
    min_range_          = 0.0F;
    max_range_          = 60.0F;
    transmission_ratio_ = 270;
    max_speed_          = 15;
    break;
  }
  case MGC104:
  {
    type_               = BocicRotatorType::ROTATION_STAGE;
    subdivision_number_ = 8;
    has_zero_limit_     = true;
    min_range_          = 0.0F;
    max_range_          = 88.0F;
    transmission_ratio_ = 444;
    max_speed_          = 5;
    break;
  }
  case MTS101:
  {
    type_               = BocicRotatorType::STRAIGHT_LINE_STAGE;
    subdivision_number_ = 8;
    has_zero_limit_     = true;
    min_range_          = 0.0F;
    max_range_          = 50.0F;
    transmission_ratio_ = 4000;
    max_speed_          = 20;
    break;
  }
  case MTS102:
  {
    type_               = BocicRotatorType::STRAIGHT_LINE_STAGE;
    subdivision_number_ = 8;
    has_zero_limit_     = true;
    min_range_          = 0.0F;
    max_range_          = 100.0F;
    transmission_ratio_ = 4000;
    max_speed_          = 20;
    break;
  }
  case MTS103:
  {
    type_               = BocicRotatorType::STRAIGHT_LINE_STAGE;
    subdivision_number_ = 8;
    has_zero_limit_     = true;
    min_range_          = 0.0F;
    max_range_          = 150.0F;
    transmission_ratio_ = 4000;
    max_speed_          = 20;
    break;
  }
  case MTS104:
  {
    type_               = BocicRotatorType::STRAIGHT_LINE_STAGE;
    subdivision_number_ = 8;
    has_zero_limit_     = true;
    min_range_          = 0.0F;
    max_range_          = 200.0F;
    transmission_ratio_ = 4000;
    max_speed_          = 40;
    break;
  }
  case MTS105:
  {
    type_               = BocicRotatorType::STRAIGHT_LINE_STAGE;
    subdivision_number_ = 8;
    has_zero_limit_     = true;
    min_range_          = 0.0F;
    max_range_          = 250.0F;
    transmission_ratio_ = 4000;
    max_speed_          = 40;
    break;
  }
  case MTS106:
  {
    type_               = BocicRotatorType::STRAIGHT_LINE_STAGE;
    subdivision_number_ = 8;
    has_zero_limit_     = true;
    min_range_          = 0.0F;
    max_range_          = 300.0F;
    transmission_ratio_ = 4000;
    max_speed_          = 40;
    break;
  }
  case UNKNOW:
  {
    type_               = BocicRotatorType::ROTATION_STAGE;
    subdivision_number_ = 8;
    has_zero_limit_     = true;
    min_range_          = 0.0F;
    max_range_          = 350.0F;
    transmission_ratio_ = 90;
    max_speed_          = 50;
    break;
  }
  }
}

BocicRotator::BocicRotatorModel BocicRotator::stringModelToRotator(const std::string& _string_model)
{
  std::map<std::string, BocicRotatorModel> model_map = {
    { "MRS101", BocicRotatorModel::MRS101 }, { "MRS102", BocicRotatorModel::MRS102 },
    { "MRS103", BocicRotatorModel::MRS103 }, { "MRS104", BocicRotatorModel::MRS104 },
    { "MRS105", BocicRotatorModel::MRS105 }, { "MRS402", BocicRotatorModel::MRS402 },
    { "MGC102", BocicRotatorModel::MGC102 }, { "MGC103", BocicRotatorModel::MGC103 },
    { "MGC104", BocicRotatorModel::MGC104 }, { "MTS101", BocicRotatorModel::MTS101 },
    { "MTS102", BocicRotatorModel::MTS102 }, { "MTS103", BocicRotatorModel::MTS103 },
    { "MTS104", BocicRotatorModel::MTS104 }, { "MTS105", BocicRotatorModel::MTS105 },
    { "MTS106", BocicRotatorModel::MTS106 },
  };
  if (model_map.find(_string_model) == model_map.end())
  {
    return BocicRotatorModel::MRS102;
  }
  return model_map[_string_model];
}

BocicRotator::BocicRotatorType BocicRotator::getType() const { return type_; }
int BocicRotator::getTransmissionRatio() const { return transmission_ratio_; }
int BocicRotator::getSubdivisionNumber() const { return subdivision_number_; }
bool BocicRotator::getHasZeroLimit() const { return has_zero_limit_; }
bool BocicRotator::getUseZeroLimit() const { return use_zero_limit_; }
float BocicRotator::getMinRange() const { return min_range_; }
float BocicRotator::getMaxRange() const { return max_range_; }
float BocicRotator::getMaxSpeed() const { return max_speed_; }
void BocicRotator::setMinRange(const float _min_range) { min_range_ = _min_range; }
void BocicRotator::setMaxRange(const float _max_range) { max_range_ = _max_range; }
}  // namespace lidar
}  // namespace robosense
