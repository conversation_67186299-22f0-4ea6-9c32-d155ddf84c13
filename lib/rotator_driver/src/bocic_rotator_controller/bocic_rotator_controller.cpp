﻿/******************************************************************************
 * Copyright 2022 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#include "bocic_rotator_controller/bocic_rotator_controller.h"
#include "bocic_rotator_controller/bocic_rotator.h"
#include "bocic_rotator_controller/bocic_rotator_controller_command.h"
#include <chrono>
#include <memory>
#include <thread>
#if __has_include("rsfsc_lib/include/rsfsc_log.h")
#  include "rsfsc_lib/include/rsfsc_log.h"
#elif __has_include("rsfsc_lib/rsfsc_log.h")
#  include "rsfsc_lib/rsfsc_log.h"
#elif __has_include("rsfsc_log/rsfsc_log.h")
#  include "rsfsc_log/rsfsc_log.h"
#else
#  include "rsfsc_log.h"
#endif

namespace robosense
{
namespace lidar
{
BocicRotatorController::BocicRotatorController(std::string& _port_name, uint32_t _support_rotator_number) :
  support_rotator_number_(_support_rotator_number), serial_port_(new QSerialPort())
{
  serial_port_->setPortName(_port_name.data());
}

BocicRotatorController::~BocicRotatorController()
{
  disconnect();
  if (serial_port_ != nullptr)
  {
    delete serial_port_;
    serial_port_ = nullptr;
  }
}

bool BocicRotatorController::connect()
{
  if (serial_port_ == nullptr)
  {
    return false;
  }
  if (!serial_port_->isOpen())
  {
    if (serial_port_->open(QIODevice::ReadWrite))
    {
      serial_port_->setBaudRate(QSerialPort::Baud9600);
      serial_port_->setDataBits(QSerialPort::Data8);
      serial_port_->setStopBits(QSerialPort::OneStop);
      serial_port_->setFlowControl(QSerialPort::NoFlowControl);
      serial_port_->setParity(QSerialPort::NoParity);
      serial_port_->clearError();
      serial_port_->clear();
      RSFSCLog::getInstance()->info(u8"连接串口成功");
      return true;
    }
    RSFSCLog::getInstance()->error(u8"打开串口失败");
    return false;
  }
  RSFSCLog::getInstance()->info(u8"串口已连接");
  return true;
}

bool BocicRotatorController::disconnect()
{
  if (serial_port_ == nullptr)
  {
    return false;
  }
  if (serial_port_->isOpen())
  {
    serial_port_->clear();
    serial_port_->close();
    RSFSCLog::getInstance()->info(u8"关闭串口成功");
    return true;
  }
  RSFSCLog::getInstance()->info(u8"串口未连接");
  return true;
}

bool BocicRotatorController::addRotator(const RotatorAxis _axis, const std::string& _rotator_type, bool _use_zero_limit)
{
  if (!serial_port_->isOpen())
  {
    RSFSCLog::getInstance()->error(u8"串口未连接");
    return false;
  }
  if (rotators_.size() >= support_rotator_number_)
  {
    RSFSCLog::getInstance()->error(u8"电机控制器支持电机数量过多");
    return false;
  }
  std::shared_ptr<BocicRotator> rotator =
    std::make_shared<BocicRotator>(BocicRotator::stringModelToRotator(_rotator_type), _use_zero_limit);
  rotators_[_axis] = std::make_shared<BocicRotatorControllerCommand>(rotator, _axis);
  RSFSCLog::getInstance()->info(u8"添加电机成功");
  return initRotator(_axis);
}

bool BocicRotatorController::rotateAngle(const float _angle_in_degree, const RotatorAxis _axis)
{
  if (!isRotatorStatusNormal(_axis))
  {
    return false;
  }
  bool is_moving = true;
  if (!isMoving(is_moving, _axis))
  {
    return false;
  }
  if (is_moving)
  {
    return false;
  }
  writeCommand(rotators_[_axis]->rotateAbsAngleCommand(_angle_in_degree));
  if (readData().find(PASS_REPLY.data()) == std::string::npos)
  {
    RSFSCLog::getInstance()->error(u8"控制电机旋转失败");
    return false;
  }
  return true;
}

bool BocicRotatorController::rotateTwoAxis(const float _angle_in_degree1,
                                           const RotatorAxis _axis1,
                                           const float _angle_in_degree2,
                                           const RotatorAxis _axis2)
{
  if (!isRotatorStatusNormal(_axis1))
  {
    return false;
  }
  if (!isRotatorStatusNormal(_axis2))
  {
    return false;
  }
  bool is_moving = true;
  if (!isMoving(is_moving, _axis1))
  {
    return false;
  }
  if (is_moving)
  {
    return false;
  }
  is_moving = true;
  if (!isMoving(is_moving, _axis2))
  {
    return false;
  }
  if (is_moving)
  {
    return false;
  }
  int pluse1 = static_cast<int>(_angle_in_degree1 * 200.0 * rotators_[_axis1]->getRotator()->getSubdivisionNumber() *
                                rotators_[_axis1]->getRotator()->getTransmissionRatio() / 360.0);
  int pluse2 = static_cast<int>(_angle_in_degree2 * 200.0 * rotators_[_axis2]->getRotator()->getSubdivisionNumber() *
                                rotators_[_axis2]->getRotator()->getTransmissionRatio() / 360.0);
  writeCommand(BocicRotatorControllerCommand::getMovingTwoAxisCommand(pluse1, _axis1, pluse2, _axis2));
  if (readData().find(PASS_REPLY.data()) == std::string::npos)
  {
    RSFSCLog::getInstance()->error(u8"控制双轴插补运动失败");
    return false;
  }
  return true;
}

bool BocicRotatorController::getCurrentAngle(float& _angle_in_degree, const RotatorAxis _axis)
{
  if (!isRotatorStatusNormal(_axis))
  {
    return false;
  }
  writeCommand(rotators_[_axis]->getCurrentPositionCommand());
  _angle_in_degree = rotators_[_axis]->positionReplyToAngle(readData());
  return true;
}

bool BocicRotatorController::move(const float _position_in_millimeter, const RotatorAxis _axis)
{
  if (!isRotatorStatusNormal(_axis))
  {
    return false;
  }
  bool is_moving = true;
  if (!isMoving(is_moving, _axis))
  {
    return false;
  }
  if (is_moving)
  {
    return false;
  }
  writeCommand(rotators_[_axis]->goStraightDistanceCommand(_position_in_millimeter));
  if (readData().find(PASS_REPLY.data()) == std::string::npos)
  {
    RSFSCLog::getInstance()->error(u8"控制电机移动失败");
    return false;
  }
  return true;
}

bool BocicRotatorController::getCurrentPosition(float& _position_in_millimeter, const RotatorAxis _axis)
{
  if (!isRotatorStatusNormal(_axis))
  {
    return false;
  }
  writeCommand(rotators_[_axis]->getCurrentPositionCommand());
  _position_in_millimeter = rotators_[_axis]->positionReplyToPosition(readData());
  return true;
}

bool BocicRotatorController::resetZeroPosition(const RotatorAxis _axis)
{
  if (!isRotatorStatusNormal(_axis))
  {
    return false;
  }
  bool is_moving = true;
  if (!isMoving(is_moving, _axis))
  {
    return false;
  }
  if (is_moving)
  {
    return false;
  }
  writeCommand(rotators_[_axis]->goHomeCommand());
  if (readData().find(PASS_REPLY.data()) == std::string::npos)
  {
    RSFSCLog::getInstance()->error(u8"复位电机失败");
    return false;
  }
  return true;
}

bool BocicRotatorController::stopRotator(const RotatorAxis _axis)
{
  if (!isRotatorStatusNormal(_axis))
  {
    return false;
  }
  writeCommand(rotators_[_axis]->stopCommand());
  if (readData().find(PASS_REPLY.data()) == std::string::npos)
  {
    RSFSCLog::getInstance()->error(u8"停止电机失败");
    return false;
  }
  return true;
}

bool BocicRotatorController::writeCommand(const std::string& _command)
{
  RSFSCLog::getInstance()->debug(u8"上位机写入命令[" + _command + "]");
  serial_port_->write(_command.data());
  return serial_port_->waitForBytesWritten(1000);
}

std::string BocicRotatorController::readData()
{
  std::this_thread::sleep_for(std::chrono::milliseconds(200));
  std::string data;
  if (serial_port_->waitForReadyRead(1000))
  {
    data = serial_port_->readAll().toStdString();
    RSFSCLog::getInstance()->debug(u8"电机控制器回复[" + data + "]");
  }
  return data;
}

bool BocicRotatorController::initRotator(const RotatorAxis _axis)
{
  if (!isRotatorStatusNormal(_axis))
  {
    return false;
  }
  writeCommand(rotators_[_axis]->setRotatorTypeCommand());
  if (readData().find(PASS_REPLY.data()) == std::string::npos)
  {
    RSFSCLog::getInstance()->error(u8"设置电机类型失败");
    return false;
  }
  writeCommand(rotators_[_axis]->setSubdivisionNumberCommand());
  if (readData().find(PASS_REPLY.data()) == std::string::npos)
  {
    RSFSCLog::getInstance()->error(u8"设置电机细分数失败");
    return false;
  }
  writeCommand(rotators_[_axis]->setTransmissionRatioCommand());
  if (readData().find(PASS_REPLY.data()) == std::string::npos)
  {
    RSFSCLog::getInstance()->error(u8"设置电机传动比失败");
    return false;
  }

  writeCommand(rotators_[_axis]->setRangeCommand());
  if (readData().find(PASS_REPLY.data()) == std::string::npos)
  {
    RSFSCLog::getInstance()->error(u8"设置电机软限位失败");
    return false;
  }
  return true;
}

bool BocicRotatorController::isRotatorStatusNormal(const RotatorAxis _axis)
{
  if (!serial_port_->isOpen())
  {
    RSFSCLog::getInstance()->error(u8"串口未连接");
    return false;
  }
  if (rotators_.find(_axis) == rotators_.end())
  {
    RSFSCLog::getInstance()->error(u8"未添加当前电机");
    return false;
  }
  return true;
}

bool BocicRotatorController::setRotatorSpeed(const int _speed, const RotatorAxis _axis)
{
  if (!isRotatorStatusNormal(_axis))
  {
    return false;
  }
  writeCommand(rotators_[_axis]->setHomingSpeedCommand(_speed));
  if (readData().find(PASS_REPLY.data()) == std::string::npos)
  {
    RSFSCLog::getInstance()->error(u8"设置电机回零速度失败");
    return false;
  }
  writeCommand(rotators_[_axis]->setSpeedCommand(_speed));
  if (readData().find(PASS_REPLY.data()) == std::string::npos)
  {
    RSFSCLog::getInstance()->error(u8"设置电机速度失败");
    return false;
  }
  return true;
}

bool BocicRotatorController::isMoving(bool& _is_moving, const RotatorAxis _axis)
{
  if (!isRotatorStatusNormal(_axis))
  {
    return false;
  }
  writeCommand(BocicRotatorControllerCommand::getIsMovingCommand());
  _is_moving = rotators_[_axis]->movingStatusReplyToBool(readData());
  return true;
}
// NOLINTNEXTLINE(bugprone-easily-swappable-parameters)
bool BocicRotatorController::setSoftLimit(const float _min_range, const float _max_range, const RotatorAxis _axis)
{
  if (!isRotatorStatusNormal(_axis))
  {
    return false;
  }

  rotators_[_axis]->getRotator()->setMinRange(_min_range);
  rotators_[_axis]->getRotator()->setMaxRange(_max_range);
  writeCommand(rotators_[_axis]->setRangeCommand());
  if (readData().find(PASS_REPLY.data()) == std::string::npos)
  {
    RSFSCLog::getInstance()->error(u8"设置电机软限位失败");
    return false;
  }
  return true;
}
bool BocicRotatorController::setAcceleration(const int _acc, const RotatorAxis _axis)
{
  // TODO: implement this function
  RSFSCLog::getInstance()->error(u8"此类型转台尚未实现加速度设置功能");
  return false;
}
}  // namespace lidar
}  // namespace robosense