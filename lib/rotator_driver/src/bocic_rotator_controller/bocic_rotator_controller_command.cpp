﻿/******************************************************************************
 * Copyright 2022 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#include "bocic_rotator_controller/bocic_rotator_controller_command.h"
#include <cstdlib>
#include <memory>
#include <regex>
#include <string>
#if __has_include("rsfsc_lib/include/rsfsc_log.h")
#  include "rsfsc_lib/include/rsfsc_log.h"
#elif __has_include("rsfsc_lib/rsfsc_log.h")
#  include "rsfsc_lib/rsfsc_log.h"
#elif __has_include("rsfsc_log/rsfsc_log.h")
#  include "rsfsc_log/rsfsc_log.h"
#else
#  include "rsfsc_log.h"
#endif
namespace robosense
{
namespace lidar
{
BocicRotatorControllerCommand::BocicRotatorControllerCommand(std::shared_ptr<BocicRotator> _rotator,
                                                             const BocicRotatorController::RotatorAxis& _axis) :
  rotator_(std::move(_rotator)), axis_(_axis)
{}

std::string BocicRotatorControllerCommand::axis2String(BocicRotatorController::RotatorAxis _axis)
{
  switch (_axis)
  {
  case RotatorControllerInterface::X: return "X";
  case RotatorControllerInterface::Y: return "Y";
  case RotatorControllerInterface::Z: return "Z";
  }
  return "";
}

std::string BocicRotatorControllerCommand::setRotatorTypeCommand()
{
  return "T" + axis2String(axis_) + "=" +
         (rotator_->getType() == BocicRotator::BocicRotatorType::ROTATION_STAGE ? "C" : "D") + "/";
}

std::string BocicRotatorControllerCommand::setSubdivisionNumberCommand()
{
  return "F" + axis2String(axis_) + "=" + std::to_string(rotator_->getSubdivisionNumber()) + "/";
}

std::string BocicRotatorControllerCommand::setTransmissionRatioCommand()
{
  return "D" + axis2String(axis_) + "=" + std::to_string(rotator_->getTransmissionRatio()) + "/";
}

std::string BocicRotatorControllerCommand::setSpeedCommand(const int _speed)
{
  return "V" + axis2String(axis_) + "=" + std::to_string(_speed) + "/";
}

std::string BocicRotatorControllerCommand::setHomingSpeedCommand(const int _speed)
{
  return "HV" + axis2String(axis_) + "=" + std::to_string(_speed) + "/";
}

std::string BocicRotatorControllerCommand::setRangeCommand()
{
  int min = 0;
  int max = 0;
  switch (rotator_->getType())
  {
  case BocicRotator::BocicRotatorType::STRAIGHT_LINE_STAGE:
    min = static_cast<int>(rotator_->getMinRange() * rotator_->getSubdivisionNumber() *
                           rotator_->getTransmissionRatio() / 80.0);
    max = static_cast<int>(rotator_->getMaxRange() * rotator_->getSubdivisionNumber() *
                           rotator_->getTransmissionRatio() / 80.0);
    break;
  case BocicRotator::BocicRotatorType::ROTATION_STAGE:
    min = static_cast<int>(rotator_->getMinRange() * 200.0 * rotator_->getSubdivisionNumber() *
                           rotator_->getTransmissionRatio() / 360.0);
    max = static_cast<int>(rotator_->getMaxRange() * 200.0 * rotator_->getSubdivisionNumber() *
                           rotator_->getTransmissionRatio() / 360.0);
    break;
  }
  return "L" + axis2String(axis_) + "=" + std::to_string(min) + "," + std::to_string(max) + "/";
}

std::string BocicRotatorControllerCommand::rotateAbsAngleCommand(const float _angle)
{
  if (rotator_->getMinRange() != 0 || rotator_->getMaxRange() != 0)
  {
    if (_angle > rotator_->getMaxRange() || _angle < rotator_->getMinRange())
    {
      RSFSCLog::getInstance()->error("rotator set angle out of range (min:" + std::to_string(rotator_->getMinRange()) +
                                     ", max:" + std::to_string(rotator_->getMaxRange()) + ")");
      return "";
    }
  }

  int angle_pluse = 0;
  if (rotator_->getHasZeroLimit())
  {
    angle_pluse =
      static_cast<int>(_angle * 200.0 * rotator_->getSubdivisionNumber() * rotator_->getTransmissionRatio() / 360.0);
  }
  else
  {
    angle_pluse =
      static_cast<int>(-_angle * 200.0 * rotator_->getSubdivisionNumber() * rotator_->getTransmissionRatio() / 360.0);
  }
  return axis2String(axis_) + "=" + std::to_string(angle_pluse) + "/";
}

std::string BocicRotatorControllerCommand::goStraightDistanceCommand(const float _position_in_millimeter)
{
  if (_position_in_millimeter > rotator_->getMaxRange() || _position_in_millimeter < rotator_->getMinRange())
  {
    return "";
  }
  int distance_pluse = static_cast<int>(_position_in_millimeter * rotator_->getSubdivisionNumber() *
                                        rotator_->getTransmissionRatio() / 80.0);
  return axis2String(axis_) + "=" + std::to_string(distance_pluse) + "/";
}

std::string BocicRotatorControllerCommand::stopCommand() { return "S0" + axis2String(axis_) + "/"; }

std::string BocicRotatorControllerCommand::goHomeCommand()
{
  if (rotator_->getHasZeroLimit() && rotator_->getUseZeroLimit())
  {
    return "-H" + axis2String(axis_) + "/";
  }
  return rotateAbsAngleCommand(0);
}

std::string BocicRotatorControllerCommand::getCurrentPositionCommand() { return "?" + axis2String(axis_) + "/"; }

std::string BocicRotatorControllerCommand::getIsMovingCommand() { return "?ST/"; }

std::string BocicRotatorControllerCommand::getMovingTwoAxisCommand(const int _angle_in_pluse1,
                                                                   const BocicRotatorController::RotatorAxis _axis1,
                                                                   const int _angle_in_pluse2,
                                                                   const BocicRotatorController::RotatorAxis _axis2)
{
  return axis2String(_axis1) + ":" + std::to_string(_angle_in_pluse1) + "," + axis2String(_axis2) + ":" +
         std::to_string(_angle_in_pluse2) + "/";
}

float BocicRotatorControllerCommand::positionReplyToAngle(const std::string& _position_reply)
{
  float angle       = 0;
  std::string reply = _position_reply.substr(0, _position_reply.size() - 1);
  if (std::regex_match(reply, std::regex(axis2String(axis_) + "=[+-][0-9]*")))
  {
    int pluse = static_cast<int>(std::strtol(reply.substr(2, reply.size() - 2).data(), nullptr, 10));
    angle =
      static_cast<float>(pluse * 360.0 / rotator_->getSubdivisionNumber() / rotator_->getTransmissionRatio() / 200.0);
  }
  return angle;
}

float BocicRotatorControllerCommand::positionReplyToPosition(const std::string& _position_reply)
{
  float position    = 0;
  std::string reply = _position_reply.substr(0, _position_reply.size() - 1);
  if (std::regex_match(reply, std::regex(axis2String(axis_) + "=[+-][0-9]*")))
  {
    int pluse = static_cast<int>(std::strtol(reply.substr(2, reply.size() - 2).data(), nullptr, 10));
    position  = static_cast<float>(pluse * 80.0 / rotator_->getSubdivisionNumber() / rotator_->getTransmissionRatio());
  }
  return position;
}

std::shared_ptr<BocicRotator> BocicRotatorControllerCommand::getRotator() { return rotator_; }

bool BocicRotatorControllerCommand::movingStatusReplyToBool(const std::string& _status_reply)
{
  bool is_moving    = true;
  std::string reply = _status_reply.substr(0, 5);
  if (std::regex_match(reply, std::regex("ST=[0-9]*")))
  {
    int status = static_cast<int>(std::strtol(reply.substr(4, 1).data(), nullptr, 10));
    RSFSCLog::getInstance()->debug(u8"电机状态[" + std::to_string(status) + "]");
    switch (axis_)
    {
    case RotatorControllerInterface::X:
    {
      if (status % 2 == 0)
      {
        is_moving = false;
      }
      break;
    }
    case RotatorControllerInterface::Y:
    {
      if (status % 4 / 2 == 0)
      {
        is_moving = false;
      }
      break;
    }
    case RotatorControllerInterface::Z:
    {
      if (status % 8 / 4 == 0)
      {
        is_moving = false;
      }
      break;
    }
    }
  }
  else
  {
    RSFSCLog::getInstance()->error(u8"字符匹配失败");
  }
  return is_moving;
}  // namespace lidar
}  // namespace lidar
}  // namespace robosense