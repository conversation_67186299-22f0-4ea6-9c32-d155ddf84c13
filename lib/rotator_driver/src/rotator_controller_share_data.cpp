﻿/******************************************************************************
 * Copyright 2022 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#include "rotator_controller_share_data.h"
#include <memory>
#include <thread>
namespace robosense
{
namespace lidar
{
void RotatorControllerRequest::setID(const int& _id) { request_id_ = _id; }
int RotatorControllerRequest::getID() const { return request_id_; }
void RotatorControllerRequest::setRequestType(const RequestType& _type) { request_type_ = _type; }
RotatorControllerRequest::RequestType RotatorControllerRequest::getRequestType() const { return request_type_; }
void RotatorControllerRequest::setAxis(const RotatorControllerInterface::RotatorAxis& _axis) { axis_ = _axis; }
RotatorControllerInterface::RotatorAxis RotatorControllerRequest::getAxis() const { return axis_; }
void RotatorControllerRequest::setAxis1(const RotatorControllerInterface::RotatorAxis& _axis) { axis1_ = _axis; }
RotatorControllerInterface::RotatorAxis RotatorControllerRequest::getAxis1() const { return axis1_; }
void RotatorControllerRequest::setAxis2(const RotatorControllerInterface::RotatorAxis& _axis) { axis2_ = _axis; }
RotatorControllerInterface::RotatorAxis RotatorControllerRequest::getAxis2() const { return axis2_; }
void RotatorControllerRequest::setRotatorType(const std::string& _rotator_type) { rotator_type_ = _rotator_type; }
std::string RotatorControllerRequest::getRotatorType() const { return rotator_type_; }
void RotatorControllerRequest::setUseZeroLimit(const bool& _use_zero_limit) { use_zero_limit_ = _use_zero_limit; }
bool RotatorControllerRequest::getUseZeroLimit() const { return use_zero_limit_; }
void RotatorControllerRequest::setAngle(const float& _angle) { angle_ = _angle; }
float RotatorControllerRequest::getAngle() const { return angle_; }
void RotatorControllerRequest::setAngle1(const float& _angle) { angle1_ = _angle; }
float RotatorControllerRequest::getAngle1() const { return angle1_; }
void RotatorControllerRequest::setAngle2(const float& _angle) { angle2_ = _angle; }
float RotatorControllerRequest::getAngle2() const { return angle2_; }
void RotatorControllerRequest::setPosition(const float& _position) { position_ = _position; }
float RotatorControllerRequest::getPosition() const { return position_; }
void RotatorControllerRequest::setSpeed(const int& _speed) { speed_ = _speed; }
int RotatorControllerRequest::getSpeed() const { return speed_; }
void RotatorControllerRequest::setAcc(const int& _acc) { acc_ = _acc; }
int RotatorControllerRequest::getAcc() const { return acc_; }

void RotatorControllerResult::setID(const int& _id) { result_id_ = _id; }
int RotatorControllerResult::getID() const { return result_id_; }
void RotatorControllerResult::setIsSuccess(const bool& _is_success) { is_success_ = _is_success; }
bool RotatorControllerResult::getIsSuccess() const { return is_success_; }
void RotatorControllerResult::setAngle(const float& _angle) { angle_ = _angle; }
float RotatorControllerResult::getAngle() const { return angle_; }
void RotatorControllerResult::setPosition(const float& _position) { position_ = _position; }
float RotatorControllerResult::getPosition() const { return position_; }
void RotatorControllerResult::setIsMoving(const bool& _is_moving) { is_moving_ = _is_moving; }
bool RotatorControllerResult::getIsMoving() const { return is_moving_; }

bool RotatorControllerShareData::connect(const int& _id)
{
  int request_id = getRotatorShareDataByID(_id)->generateRequestID();
  std::shared_ptr<RotatorControllerRequest> new_request(
    new RotatorControllerRequest(request_id, RotatorControllerRequest::RequestType::CONNECT));
  getRotatorShareDataByID(_id)->addRequest(request_id, new_request);
  return getIsSuccessByID(_id, request_id);
}

bool RotatorControllerShareData::disconnect(const int& _id)
{
  int request_id = getRotatorShareDataByID(_id)->generateRequestID();
  std::shared_ptr<RotatorControllerRequest> new_request(
    new RotatorControllerRequest(request_id, RotatorControllerRequest::RequestType::DISCONNECT));
  getRotatorShareDataByID(_id)->addRequest(request_id, new_request);
  return getIsSuccessByID(_id, request_id);
}

bool RotatorControllerShareData::addRotator(const int& _id,
                                            const RotatorControllerInterface::RotatorAxis& _axis,
                                            const std::string& _rotator_type,
                                            const bool& _use_zero_limit)
{
  int request_id = getRotatorShareDataByID(_id)->generateRequestID();
  std::shared_ptr<RotatorControllerRequest> new_request =
    std::make_shared<RotatorControllerRequest>(request_id, RotatorControllerRequest::RequestType::ADD_ROTATOR);
  new_request->setAxis(_axis);
  new_request->setRotatorType(_rotator_type);
  new_request->setUseZeroLimit(_use_zero_limit);
  getRotatorShareDataByID(_id)->addRequest(request_id, new_request);
  return getIsSuccessByID(_id, request_id);
}

int RotatorControllerShareData::rotateAngle(const int& _id,
                                            const float& _angle,
                                            const RotatorControllerInterface::RotatorAxis& _axis)
{
  int request_id = getRotatorShareDataByID(_id)->generateRequestID();
  std::shared_ptr<RotatorControllerRequest> new_request =
    std::make_shared<RotatorControllerRequest>(request_id, RotatorControllerRequest::RequestType::ROTATE_ANGLE);
  new_request->setAxis(_axis);
  new_request->setAngle(_angle);
  getRotatorShareDataByID(_id)->addRequest(request_id, new_request);
  return request_id;
}

int RotatorControllerShareData::rotateAngleRelative(const int& _id,
                                                    const float& _angle,
                                                    const RotatorControllerInterface::RotatorAxis& _axis)
{
  int request_id                                        = getRotatorShareDataByID(_id)->generateRequestID();
  std::shared_ptr<RotatorControllerRequest> new_request = std::make_shared<RotatorControllerRequest>(
    request_id, RotatorControllerRequest::RequestType::ROTATE_ANGLE_RELATIVE);
  new_request->setAxis(_axis);
  new_request->setAngle(_angle);
  getRotatorShareDataByID(_id)->addRequest(request_id, new_request);
  return request_id;
}

int RotatorControllerShareData::rotateTwoAxis(const int& _id,
                                              const float& _angle1,
                                              const RotatorControllerInterface::RotatorAxis& _axis1,
                                              const float& _angle2,
                                              const RotatorControllerInterface::RotatorAxis& _axis2)
{
  int request_id = getRotatorShareDataByID(_id)->generateRequestID();
  std::shared_ptr<RotatorControllerRequest> new_request =
    std::make_shared<RotatorControllerRequest>(request_id, RotatorControllerRequest::RequestType::ROTATE_TWO_AXIS);
  new_request->setAxis1(_axis1);
  new_request->setAngle1(_angle1);
  new_request->setAxis2(_axis2);
  new_request->setAngle2(_angle2);
  getRotatorShareDataByID(_id)->addRequest(request_id, new_request);
  return request_id;
}

bool RotatorControllerShareData::getCurrentAngle(const int& _id,
                                                 float& _angle_in_degree,
                                                 const RotatorControllerInterface::RotatorAxis& _axis)
{
  int request_id = getRotatorShareDataByID(_id)->generateRequestID();
  std::shared_ptr<RotatorControllerRequest> new_request =
    std::make_shared<RotatorControllerRequest>(request_id, RotatorControllerRequest::RequestType::GET_CURRENT_ANGLE);
  new_request->setAxis(_axis);
  getRotatorShareDataByID(_id)->addRequest(request_id, new_request);
  return getCurrentAngleByID(_id, request_id, _angle_in_degree);
}

int RotatorControllerShareData::move(const int& _id,
                                     const float& _position,
                                     const RotatorControllerInterface::RotatorAxis& _axis)
{
  int request_id = getRotatorShareDataByID(_id)->generateRequestID();
  std::shared_ptr<RotatorControllerRequest> new_request =
    std::make_shared<RotatorControllerRequest>(request_id, RotatorControllerRequest::RequestType::MOVE);
  new_request->setAxis(_axis);
  new_request->setPosition(_position);
  getRotatorShareDataByID(_id)->addRequest(request_id, new_request);
  return request_id;
}

bool RotatorControllerShareData::getCurrentPosition(const int& _id,
                                                    float& _position,
                                                    const RotatorControllerInterface::RotatorAxis& _axis)
{
  int request_id = getRotatorShareDataByID(_id)->generateRequestID();
  std::shared_ptr<RotatorControllerRequest> new_request =
    std::make_shared<RotatorControllerRequest>(request_id, RotatorControllerRequest::RequestType::GET_CURRENT_POSITION);
  new_request->setAxis(_axis);
  getRotatorShareDataByID(_id)->addRequest(request_id, new_request);
  return getCurrentPositionByID(_id, request_id, _position);
}

int RotatorControllerShareData::resetZeroPosition(const int& _id, const RotatorControllerInterface::RotatorAxis& _axis)
{
  int request_id = getRotatorShareDataByID(_id)->generateRequestID();
  std::shared_ptr<RotatorControllerRequest> new_request =
    std::make_shared<RotatorControllerRequest>(request_id, RotatorControllerRequest::RequestType::RESET_ZERO_POSITION);
  new_request->setAxis(_axis);
  getRotatorShareDataByID(_id)->addRequest(request_id, new_request);
  return request_id;
}

bool RotatorControllerShareData::stopRotator(const int& _id, const RotatorControllerInterface::RotatorAxis& _axis)
{
  int request_id = getRotatorShareDataByID(_id)->generateRequestID();
  std::shared_ptr<RotatorControllerRequest> new_request(
    new RotatorControllerRequest(request_id, RotatorControllerRequest::RequestType::STOP_ROTATOR));
  new_request->setAxis(_axis);
  getRotatorShareDataByID(_id)->addRequest(request_id, new_request);
  return getIsSuccessByID(_id, request_id);
}

bool RotatorControllerShareData::setRotatorSpeed(const int& _id,
                                                 const int& _speed,
                                                 const RotatorControllerInterface::RotatorAxis& _axis)
{
  int request_id = getRotatorShareDataByID(_id)->generateRequestID();
  std::shared_ptr<RotatorControllerRequest> new_request(
    new RotatorControllerRequest(request_id, RotatorControllerRequest::RequestType::SET_ROTATOR_SPEED));
  new_request->setSpeed(_speed);
  new_request->setAxis(_axis);
  getRotatorShareDataByID(_id)->addRequest(request_id, new_request);
  return getIsSuccessByID(_id, request_id);
}
bool RotatorControllerShareData::setAcceleration(const int& _id,
                                                 const int _acc,
                                                 const RotatorControllerInterface::RotatorAxis& _axis)
{
  int request_id = getRotatorShareDataByID(_id)->generateRequestID();
  std::shared_ptr<RotatorControllerRequest> new_request(
    new RotatorControllerRequest(request_id, RotatorControllerRequest::RequestType::SET_ROTATOR_ACC));
  new_request->setAcc(_acc);
  new_request->setAxis(_axis);
  getRotatorShareDataByID(_id)->addRequest(request_id, new_request);
  return getIsSuccessByID(_id, request_id);
}
bool RotatorControllerShareData::isMoving(const int& _id,
                                          bool& _is_moving,
                                          const RotatorControllerInterface::RotatorAxis& _axis)
{
  int request_id = getRotatorShareDataByID(_id)->generateRequestID();
  std::shared_ptr<RotatorControllerRequest> new_request =
    std::make_shared<RotatorControllerRequest>(request_id, RotatorControllerRequest::RequestType::IS_MOVING);
  new_request->setAxis(_axis);
  getRotatorShareDataByID(_id)->addRequest(request_id, new_request);
  return getIsMovingByID(_id, request_id, _is_moving);
}

bool RotatorControllerShareData::setUserOriginal(const int& _id, const RotatorControllerInterface::RotatorAxis _axis)
{
  int request_id = getRotatorShareDataByID(_id)->generateRequestID();
  std::shared_ptr<RotatorControllerRequest> new_request =
    std::make_shared<RotatorControllerRequest>(request_id, RotatorControllerRequest::RequestType::SET_USER_ORIGINAL);
  new_request->setAxis(_axis);
  getRotatorShareDataByID(_id)->addRequest(request_id, new_request);
  return getIsSuccessByID(_id, request_id);
}

bool RotatorControllerShareData::goOrigion(const int& _id, const RotatorControllerInterface::RotatorAxis _axis)
{
  int request_id = getRotatorShareDataByID(_id)->generateRequestID();
  std::shared_ptr<RotatorControllerRequest> new_request =
    std::make_shared<RotatorControllerRequest>(request_id, RotatorControllerRequest::RequestType::GO_ORIGINAL);
  new_request->setAxis(_axis);
  getRotatorShareDataByID(_id)->addRequest(request_id, new_request);
  return getIsSuccessByID(_id, request_id);
}

bool RotatorControllerShareData::getIsSuccessByID(const int& _thread_id, const int& _result_id)
{
  while (!getRotatorShareDataByID(_thread_id)->isResultExist(_result_id))
  {
    std::this_thread::sleep_for(std::chrono::milliseconds(200));
  }
  return getRotatorShareDataByID(_thread_id)->getResultByID(_result_id)->getIsSuccess();
}

bool RotatorControllerShareData::getIsMovingByID(const int& _thread_id, const int& _result_id, bool& _is_moving)
{
  while (!getRotatorShareDataByID(_thread_id)->isResultExist(_result_id))
  {
    std::this_thread::sleep_for(std::chrono::milliseconds(200));
  }
  std::shared_ptr<RotatorControllerResult> result = getRotatorShareDataByID(_thread_id)->getResultByID(_result_id);
  if (!result->getIsSuccess())
  {
    return false;
  }
  _is_moving = result->getIsMoving();
  return true;
}

bool RotatorControllerShareData::getCurrentAngleByID(const int& _thread_id, const int& _result_id, float& _angle)
{
  while (!getRotatorShareDataByID(_thread_id)->isResultExist(_result_id))
  {
    std::this_thread::sleep_for(std::chrono::milliseconds(200));
  }
  std::shared_ptr<RotatorControllerResult> result = getRotatorShareDataByID(_thread_id)->getResultByID(_result_id);
  if (!result->getIsSuccess())
  {
    return false;
  }
  _angle = result->getAngle();
  return true;
}

bool RotatorControllerShareData::getCurrentPositionByID(const int& _thread_id, const int& _result_id, float& _position)
{
  while (!getRotatorShareDataByID(_thread_id)->isResultExist(_result_id))
  {
    std::this_thread::sleep_for(std::chrono::milliseconds(200));
  }
  std::shared_ptr<RotatorControllerResult> result = getRotatorShareDataByID(_thread_id)->getResultByID(_result_id);
  if (!result->getIsSuccess())
  {
    return false;
  }
  _position = result->getPosition();
  return true;
}

std::shared_ptr<ShareDataInterface<std::shared_ptr<RotatorControllerRequest>, std::shared_ptr<RotatorControllerResult>>>
RotatorControllerShareData::getRotatorShareDataByID(const int _id)
{
  if (rotator_share_data_.find(_id) == rotator_share_data_.end())
  {
    return nullptr;
  }
  return rotator_share_data_[_id];
}

void RotatorControllerShareData::addRotatorShareData(const int _id)
{
  if (rotator_share_data_.find(_id) == rotator_share_data_.end())
  {
    rotator_share_data_[_id] = std::make_shared<
      ShareDataInterface<std::shared_ptr<RotatorControllerRequest>, std::shared_ptr<RotatorControllerResult>>>();
  }
}
}  // namespace lidar
}  // namespace robosense