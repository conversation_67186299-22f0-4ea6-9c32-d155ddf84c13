﻿/******************************************************************************
 * Copyright 2022 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#include "mc600_rotator_controller/mc600_rotator_controller.h"
#include <chrono>
#include <functional>
#include <memory>
#include <mutex>
#include <thread>
#if __has_include("rsfsc_lib/include/rsfsc_log.h")
#  include "rsfsc_lib/include/rsfsc_log.h"
#elif __has_include("rsfsc_lib/rsfsc_log.h")
#  include "rsfsc_lib/rsfsc_log.h"
#elif __has_include("rsfsc_log/rsfsc_log.h")
#  include "rsfsc_log/rsfsc_log.h"
#else
#  include "rsfsc_log.h"
#endif

namespace robosense
{
namespace lidar
{

#define GetDirtion(value) ((value) > 0 ? "P," : "N,")

MC600RotatorController::MC600RotatorController(const std::string& _port_name, uint32_t _support_rotator_number) :
  support_rotator_number_(_support_rotator_number), serial_port_(new QSerialPort())
{
  serial_port_->setPortName(_port_name.data());
  thread_running_      = true;
  deal_command_thread_ = std::thread(std::bind(&MC600RotatorController::dealMoveCommand, this));
}

MC600RotatorController::~MC600RotatorController()
{
  thread_running_ = false;
  condition_var_.notify_one();
  if (deal_command_thread_.joinable())
  {
    deal_command_thread_.join();
  }

  disconnect();
  if (serial_port_ != nullptr)
  {
    delete serial_port_;
    serial_port_ = nullptr;
  }
}

bool MC600RotatorController::connect()
{
  if (serial_port_ == nullptr)
  {
    return false;
  }
  if (!serial_port_->isOpen())
  {
    if (serial_port_->open(QIODevice::ReadWrite))
    {
      serial_port_->setBaudRate(QSerialPort::Baud19200);
      serial_port_->setDataBits(QSerialPort::Data8);
      serial_port_->setStopBits(QSerialPort::OneStop);
      serial_port_->setFlowControl(QSerialPort::NoFlowControl);
      serial_port_->setParity(QSerialPort::NoParity);
      serial_port_->clearError();
      serial_port_->clear();
      RSFSCLog::getInstance()->info(u8"连接串口成功");
      return true;
    }
    RSFSCLog::getInstance()->error(u8"打开串口失败");
    return false;
  }
  RSFSCLog::getInstance()->info(u8"串口已连接");
  return true;
}

bool MC600RotatorController::disconnect()
{
  if (serial_port_ == nullptr)
  {
    return false;
  }
  if (serial_port_->isOpen())
  {
    serial_port_->clear();
    serial_port_->close();
    RSFSCLog::getInstance()->info(u8"关闭串口成功");
    return true;
  }
  RSFSCLog::getInstance()->info(u8"串口未连接");
  return false;
}

bool MC600RotatorController::addRotator(const RotatorAxis _axis, const std::string& _rotator_type, bool _use_zero_limit)
{
  if (!serial_port_->isOpen())
  {
    RSFSCLog::getInstance()->error(u8"串口未连接");
    return false;
  }

  is_moving_vec_[_axis] = false;

  return initRotator(_axis);
}

bool MC600RotatorController::rotateAngle(const float _angle_in_degree, const RotatorAxis _axis)
{
  if (!isRotatorStatusNormal(_axis))
  {
    return false;
  }

  if (is_moving_vec_[_axis])
  {
    return false;
  }

  is_moving_vec_[_axis] = true;

  std::string command = "GoPosition " + rotatorAxis2String_.at(_axis) + ",O,A," + GetDirtion(_angle_in_degree) +
                        std::to_string(std::abs(_angle_in_degree)) + "\r";

  writeCommand(command);
  std::string result = readAllData();
  if (result.find(PASS_REPLY_STR_READY) == std::string::npos)
  {
    RSFSCLog::getInstance()->error(u8"控制电机旋转失败");
    is_moving_vec_[_axis] = false;
    return false;
  }

  if (result.size() == PASS_REPLY_STR_READY.size() + 1)
  {
    if (readAllData(10000).find(PASS_REPLY_STR_OK) == std::string::npos)
    {
      is_moving_vec_[_axis] = false;
      RSFSCLog::getInstance()->error(u8"{}轴绝对运动失败", rotatorAxis2String_.at(_axis));
      return false;
    }
  }
  else if (!result.empty())
  {
    preprocessString(result);
    checkIfAixsMoveFinished(result);
  }

  is_moving_vec_[_axis] = false;

  return true;
}

bool MC600RotatorController::rotateAngleRelative(const float _angle_in_degree, const RotatorAxis _axis)
{
  if (!isRotatorStatusNormal(_axis))
  {
    return false;
  }

  if (is_moving_vec_[_axis])
  {
    return false;
  }

  is_moving_vec_[_axis] = true;

  std::string command = "GoPosition " + rotatorAxis2String_.at(_axis) + ",O,R," + GetDirtion(_angle_in_degree) +
                        std::to_string(std::abs(_angle_in_degree)) + "\r";

  writeCommand(command);
  std::string result = readAllData();
  if (result.find(PASS_REPLY_STR_READY) == std::string::npos)
  {
    RSFSCLog::getInstance()->error(u8"控制电机旋转失败");
    is_moving_vec_[_axis] = false;
    return false;
  }

  if (result.size() == PASS_REPLY_STR_READY.size() + 1)
  {
    if (readAllData(10000).find(PASS_REPLY_STR_OK) == std::string::npos)
    {
      is_moving_vec_[_axis] = false;
      RSFSCLog::getInstance()->error(u8"{}轴相对运动失败", rotatorAxis2String_.at(_axis));
      return false;
    }
  }
  else if (!result.empty())
  {
    preprocessString(result);
    checkIfAixsMoveFinished(result);
  }

  is_moving_vec_[_axis] = false;

  return true;
}

bool MC600RotatorController::rotateTwoAxis(const float _angle_in_degree1,
                                           const RotatorAxis _axis1,
                                           const float _angle_in_degree2,
                                           const RotatorAxis _axis2)
{
  if (!isRotatorStatusNormal(_axis1) || !isRotatorStatusNormal(_axis2))
  {
    return false;
  }

  if (is_moving_vec_[_axis1] || is_moving_vec_[_axis2])
  {
    return false;
  }

  // 此命令是双轴相对运动
  // std::string command = "GoInterpolationline2 " +
  //                       rotatorAxis2String_.at(_axis1) + "," + std::to_string(_angle_in_degree1) + "," +
  //                       rotatorAxis2String_.at(_axis2) + "," + std::to_string(_angle_in_degree2) + "\r";

  // writeCommand(command);
  // if (readData(PASS_REPLY_STR_READY.size()).find(PASS_REPLY_STR_READY) == std::string::npos)
  // {
  //   RSFSCLog::getInstance()->error(u8"控制双轴插补运动失败");
  //   return false;
  // }

  is_moving_vec_[_axis1] = is_moving_vec_[_axis2] = true;

  std::string command_1 = "GoPosition " + rotatorAxis2String_.at(_axis1) + ",O,A," + GetDirtion(_angle_in_degree1) +
                          std::to_string(std::abs(_angle_in_degree1)) + "\r";

  std::string command_2 = "GoPosition " + rotatorAxis2String_.at(_axis2) + ",O,A," + GetDirtion(_angle_in_degree2) +
                          std::to_string(std::abs(_angle_in_degree2)) + "\r";

  bool result_1 = writeCommand(command_1);
  bool result_2 = writeCommand(command_2);

  // 写两条命令会回复两个READY,但只读一个，防止读第二个的时候第一个已经回复OK
  // if (readData(PASS_REPLY_STR_READY.size()).find(PASS_REPLY_STR_READY) == std::string::npos)
  // {
  //   RSFSCLog::getInstance()->error(u8"控制电机旋转失败");
  //   return false;
  // }

  if (!result_1)
  {
    is_moving_vec_[_axis1] = false;
    RSFSCLog::getInstance()->error(u8"双轴运动X轴命令写入失败!");
  }

  if (!result_2)
  {
    is_moving_vec_[_axis2] = false;
    RSFSCLog::getInstance()->error(u8"双轴运动Y轴命令写入失败!");
  }

  condition_var_.notify_one();

  return result_1 && result_2;
}

bool MC600RotatorController::getCurrentAngle(float& _angle_in_degree, const RotatorAxis _axis)
{
  if (!isRotatorStatusNormal(_axis) || hasAxisMoving())
  {
    return false;
  }
  std::string command = "Position? " + rotatorAxis2String_.at(_axis) + "\r";
  writeCommand(command);
  std::string read_data = readAllData();

  return positionReplyToAngle(read_data, _angle_in_degree);
}

bool MC600RotatorController::move(const float _position_in_millimeter, const RotatorAxis _axis)
{
  RSFSCLog::getInstance()->error(u8"未实现该控制器的移动指令");
  return false;
}

bool MC600RotatorController::getCurrentPosition(float& _position_in_millimeter, const RotatorAxis _axis)
{
  RSFSCLog::getInstance()->error(u8"未实现该控制器的位置获取指令");
  return false;
}

bool MC600RotatorController::resetZeroPosition(const RotatorAxis _axis)
{
  if (!isRotatorStatusNormal(_axis))
  {
    return false;
  }

  if (is_moving_vec_[_axis])
  {
    return false;
  }

  if (!writeCommand("GoHome " + rotatorAxis2String_.at(_axis) + "\r"))
  {
    RSFSCLog::getInstance()->error(u8"光电回零命令写入失败!");
    return false;
  }

  std::string result = readAllData();
  if (result.find(PASS_REPLY_STR_READY) == std::string::npos)
  {
    RSFSCLog::getInstance()->error(u8"光电回零命令未收到正确回复，回复信息为:{}!", result);
    is_moving_vec_[_axis] = false;
    return false;
  }

  if (result.size() == PASS_REPLY_STR_READY.size() + 1)
  {
    if (readAllData(1000 * 15).find(PASS_REPLY_STR_OK) == std::string::npos)
    {
      is_moving_vec_[_axis] = false;
      RSFSCLog::getInstance()->error(u8"{}轴光电回零失败", rotatorAxis2String_.at(_axis));
      return false;
    }
  }
  else if (!result.empty())
  {
    preprocessString(result);
    checkIfAixsMoveFinished(result);
  }

  return true;
}

bool MC600RotatorController::stopRotator(const RotatorAxis _axis)
{
  if (!isRotatorStatusNormal(_axis))
  {
    return false;
  }

  writeCommand("STOP " + rotatorAxis2String_.at(_axis) + "\r");
  if (readData(PASS_REPLY_STR_READY.size()).find(PASS_REPLY_STR_READY) == std::string::npos)
  {
    RSFSCLog::getInstance()->error(u8"停止电机失败");
    return false;
  }

  // TODO:
  // 待确认是否立即停止以及是否需要读取此回复
  if (readAllData(5000).find(PASS_REPLY_STR_OK) == std::string::npos)
  {
    RSFSCLog::getInstance()->error(u8"停止电机失败");
    return false;
  }

  is_moving_vec_[_axis] = false;

  return true;
}

bool MC600RotatorController::setRotatorSpeed(const int _speed, const RotatorAxis _axis)
{
  if (!isRotatorStatusNormal(_axis))
  {
    return false;
  }

  std::string command;

  float init_speed = _speed * 0.45;
  command          = "SetInitSpeed " + rotatorAxis2String_.at(_axis) + "," + std::to_string(init_speed) + "\r";
  writeCommand(command);
  if (readAllData().find(PASS_REPLY_STR_OK) == std::string::npos)
  {
    RSFSCLog::getInstance()->error(u8"设置电机初速度失败");
    return false;
  }

  command = "SetSpeed " + rotatorAxis2String_.at(_axis) + "," + std::to_string(_speed) + "\r";
  writeCommand(command);
  if (readAllData().find(PASS_REPLY_STR_OK) == std::string::npos)
  {
    RSFSCLog::getInstance()->error(u8"设置电机速度失败");
    return false;
  }

  float home_speed = std::min(init_speed - 0.5, 3.0);
  command          = "SetHomeSpeed " + rotatorAxis2String_.at(_axis) + "," + std::to_string(home_speed) + "\r";
  writeCommand(command);
  if (readAllData().find(PASS_REPLY_STR_OK) == std::string::npos)
  {
    RSFSCLog::getInstance()->error(u8"设置电机回零速度失败");
    return false;
  }

  return true;
}

bool MC600RotatorController::isMoving(bool& _is_moving, const RotatorAxis _axis)
{
  if (is_moving_vec_.find(_axis) == is_moving_vec_.end())
  {
    RSFSCLog::getInstance()->error(u8"未添加的电机轴：" + rotatorAxis2String_[_axis]);
    return false;
  }

  _is_moving = is_moving_vec_[_axis];

  return true;
}

bool MC600RotatorController::hasAxisMoving()
{
  bool has_axis_moving = false;
  for (auto& is_move : is_moving_vec_)
  {
    has_axis_moving |= is_move.second;
  }
  return has_axis_moving;
}

bool MC600RotatorController::setUserOriginal(const RotatorAxis _axis)
{
  if (!isRotatorStatusNormal(_axis))
  {
    return false;
  }

  std::string command = "SetUserOrigin " + rotatorAxis2String_.at(_axis) + ",0\r";
  writeCommand(command);
  if (readAllData().find(PASS_REPLY_STR_OK) == std::string::npos)
  {
    RSFSCLog::getInstance()->error(rotatorAxis2String_.at(_axis) + "轴用户原点设置失败!");
    return false;
  }
  return true;
}

bool MC600RotatorController::goOrigion(const RotatorAxis _axis)
{
  if (!isRotatorStatusNormal(_axis))
  {
    return false;
  }

  if (is_moving_vec_[_axis])
  {
    return false;
  }

  if (!writeCommand("GoOrigion " + rotatorAxis2String_.at(_axis) + "\r"))
  {
    RSFSCLog::getInstance()->error(u8"{}轴用户回零命令写入失败!", rotatorAxis2String_.at(_axis));
    return false;
  }

  std::string result = readAllData();
  if (result.find(PASS_REPLY_STR_READY) == std::string::npos)
  {
    RSFSCLog::getInstance()->error(u8"用户回零命令未收到正确回复，回复信息为:{}!", result);
    is_moving_vec_[_axis] = false;
    return false;
  }

  if (result.size() == PASS_REPLY_STR_READY.size() + 1)
  {
    if (readAllData(1000 * 15).find(PASS_REPLY_STR_OK) == std::string::npos)
    {
      is_moving_vec_[_axis] = false;
      RSFSCLog::getInstance()->error(u8"{}轴用户回零失败", rotatorAxis2String_.at(_axis));
      return false;
    }
  }
  else if (!result.empty())
  {
    preprocessString(result);
    checkIfAixsMoveFinished(result);
  }

  return true;
}

bool MC600RotatorController::writeCommand(const std::string& _command)
{
  RSFSCLog::getInstance()->debug(u8"上位机写入命令[" + _command + "]");
  serial_port_->write(_command.data());
  return serial_port_->waitForBytesWritten(1000);
}

std::string MC600RotatorController::readAllData(int _msecs)
{
  std::string data;
  if (serial_port_->waitForReadyRead(_msecs))
  {
    data = serial_port_->readAll().toStdString();
    std::this_thread::sleep_for(std::chrono::milliseconds(30));
    data += serial_port_->readAll().toStdString();

    std::replace(data.begin(), data.end(), '\r', ' ');
    RSFSCLog::getInstance()->debug(u8"电机控制器回复[" + data + "]");
  }
  else
  {
    RSFSCLog::getInstance()->debug(u8"等待控制器回复超时!");
  }
  return data;
}

std::string MC600RotatorController::readData(qint64 _max_size, int _msecs)
{
  std::this_thread::sleep_for(std::chrono::milliseconds(30));
  std::string data;
  if (serial_port_->waitForReadyRead(_msecs))
  {
    data = serial_port_->read(_max_size).toStdString();
    std::replace(data.begin(), data.end(), '\r', ' ');
    RSFSCLog::getInstance()->debug(u8"电机控制器回复[" + data + "]");
  }
  else
  {
    RSFSCLog::getInstance()->debug(u8"等待控制器回复超时!");
  }
  return data;
}

bool MC600RotatorController::positionReplyToAngle(const std::string& _reply_msg, float& _position)
{
  if (_reply_msg.find(PASS_REPLY_STR_OK) == std::string::npos)
  {
    return false;
  }

  //回复形式为: Position? Axis,x,ok
  std::size_t first_index = _reply_msg.find_first_of(',');
  std::size_t last_index  = _reply_msg.find_last_of(',');
  if (last_index <= first_index)
  {
    return false;
  }

  _position = static_cast<float>(atof(_reply_msg.substr(first_index + 1, last_index - 1 - first_index).data()));
  return true;
}

bool MC600RotatorController::setSoftLimit(const float _min, const float _max, const RotatorAxis _axis)
{
  RSFSCLog::getInstance()->error(u8"MC 600 not support soft limit setting");
  return false;
}
bool MC600RotatorController::setAcceleration(const int _acc, const RotatorAxis _axis)
{
  if (!isRotatorStatusNormal(_axis))
  {
    return false;
  }

  std::string command;
  command = "SetAccSpeed " + rotatorAxis2String_.at(_axis) + "," + std::to_string(_acc) + "\r";
  writeCommand(command);
  if (readAllData().find(PASS_REPLY_STR_OK) == std::string::npos)
  {
    RSFSCLog::getInstance()->error(u8"设置电机加速度失败");
    return false;
  }
  RSFSCLog::getInstance()->info(u8"设置电机加速度为：" + std::to_string(_acc));
  return true;
}
bool MC600RotatorController::isRotatorStatusNormal(const RotatorAxis _axis)
{
  if (!serial_port_->isOpen())
  {
    RSFSCLog::getInstance()->error(u8"串口未连接");
    return false;
  }

  if (is_moving_vec_.find(_axis) == is_moving_vec_.end())
  {
    RSFSCLog::getInstance()->error(u8"未添加当前电机:" + rotatorAxis2String_[_axis]);
    return false;
  }

  return true;
}

bool MC600RotatorController::initRotator(const RotatorAxis _axis)
{
  if (!isRotatorStatusNormal(_axis))
  {
    return false;
  }

  std::string command;
  command = "SetStageStyle " + rotatorAxis2String_.at(_axis) + ",R" + "\r";
  writeCommand(command);
  if (readAllData().find(PASS_REPLY_STR_OK) == std::string::npos)
  {
    RSFSCLog::getInstance()->error(u8"设置电机类型失败");
    return false;
  }

  command = "SetStageDriveRat " + rotatorAxis2String_.at(_axis) + ",180" + "\r";
  writeCommand(command);
  if (readAllData().find(PASS_REPLY_STR_OK) == std::string::npos)
  {
    RSFSCLog::getInstance()->error(u8"设置电机传动比失败");
    return false;
  }

  command = "Setsechhomemode " + rotatorAxis2String_.at(_axis) + ",2" + "\r";
  writeCommand(command);
  if (readAllData().find(PASS_REPLY_STR_OK) == std::string::npos)
  {
    RSFSCLog::getInstance()->error(u8"设置归零模式失败");
    return false;
  }

  command = "SetUnit " + rotatorAxis2String_.at(_axis) + ",d" + "\r";
  writeCommand(command);
  if (readAllData().find(PASS_REPLY_STR_OK) == std::string::npos)
  {
    RSFSCLog::getInstance()->error(u8"设置运行单位失败");
    return false;
  }

  // writeCommand(rotators_[_axis]->setRangeCommand());
  // if (readAllData().find(PASS_REPLY_STR) == std::string::npos)
  // {
  //   RSFSCLog::getInstance()->error(u8"设置电机软限位失败");
  //   return false;
  // }
  return true;
}

void MC600RotatorController::checkIfAixsMoveFinished(std::string& _reply_msg)
{
  for (std::size_t i = 0; i < rotatorAxis2String_.size(); ++i)
  {
    if (_reply_msg.find(rotatorAxis2String_.at(i) + " ") != std::string::npos &&
        _reply_msg.find("," + PASS_REPLY_STR_OK) != std::string::npos)
    {
      is_moving_vec_[i] = false;
    }
  }
}

inline void MC600RotatorController::preprocessString(std::string& _string)
{
  while (true)
  {
    auto pos = _string.find(PASS_REPLY_STR_READY);
    if (pos == std::string::npos)
    {
      break;
    }
    _string.erase(pos, PASS_REPLY_STR_READY.size());
  }
}

void MC600RotatorController::dealMoveCommand()
{
  while (thread_running_)
  {
    std::unique_lock<std::mutex> lock(mutex_);
    condition_var_.wait(lock);

    if (!hasAxisMoving() || !thread_running_)
    {
      continue;
    }

    std::string reply_msg = "";
    int count             = 10;
    while (count > 0)  // 3次循环，最多等待30s之后退出
    {
      reply_msg += readAllData(1000 * 5);
      preprocessString(reply_msg);

      auto index = reply_msg.find(PASS_REPLY_STR_OK);
      if (index != std::string::npos)
      {
        std::string single_full_msg = reply_msg.substr(0, index + PASS_REPLY_STR_OK.size());
        reply_msg                   = reply_msg.substr(index + PASS_REPLY_STR_OK.size());
        checkIfAixsMoveFinished(single_full_msg);
      }

      if (!hasAxisMoving())
      {
        break;
      }

      count--;
    }
  }
}

}  // namespace lidar
}  // namespace robosense