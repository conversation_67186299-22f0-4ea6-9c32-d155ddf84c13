﻿/******************************************************************************
 * Copyright 2022 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#include "rotator_controller_monitor.h"
#include "rotator_controller_share_data.h"
#include <chrono>
#include <memory>
#include <thread>
#include <utility>
#if __has_include("rsfsc_lib/include/rsfsc_log.h")
#  include "rsfsc_lib/include/rsfsc_log.h"
#elif __has_include("rsfsc_lib/rsfsc_log.h")
#  include "rsfsc_lib/rsfsc_log.h"
#elif __has_include("rsfsc_log/rsfsc_log.h")
#  include "rsfsc_log/rsfsc_log.h"
#else
#  include "rsfsc_log.h"
#endif
namespace robosense
{
namespace lidar
{
RotatorControllerMonitor::RotatorControllerMonitor(const int& _id,
                                                   const RotatorControllerFactory::RotatorControllerType& _type,
                                                   const std::string& _port_name) :
  id_(_id), type_(_type), port_name_(_port_name), is_running_(true)
{
  if (port_name_.empty())
  {
    RSFSCLog::getInstance()->error(u8"初始化电机控制器失败，串口号为空");
    return;
  }
}

RotatorControllerMonitor::~RotatorControllerMonitor() { stop(); }

void RotatorControllerMonitor::stop()
{
  is_running_ = false;
  wait();
}

void RotatorControllerMonitor::run()
{
  std::shared_ptr<RotatorControllerInterface> rotator_controller(
    RotatorControllerFactory::createRotatorController(type_, { port_name_ }));
  if (rotator_controller == nullptr)
  {
    RSFSCLog::getInstance()->error(u8"初始化电机控制器失败，无法识别电机控制器型号");
    return;
  }
  std::shared_ptr<RotatorControllerRequest> current_request(nullptr);
  bool result        = false;
  bool request_again = false;
  while (is_running_)
  {
    if (RotatorControllerShareData::getInstance()->getRotatorShareDataByID(id_)->isHaveNewRequest())
    {
      request_again = false;
      current_request =
        std::move(RotatorControllerShareData::getInstance()->getRotatorShareDataByID(id_)->getRequest());
      std::shared_ptr<RotatorControllerResult> new_result =
        std::make_shared<RotatorControllerResult>(current_request->getID());
      switch (current_request->getRequestType())
      {
      case RotatorControllerRequest::CONNECT:
      {
        result = rotator_controller->connect();
        break;
      }
      case RotatorControllerRequest::DISCONNECT:
      {
        result = rotator_controller->disconnect();
        break;
      }
      case RotatorControllerRequest::ADD_ROTATOR:
      {
        result = rotator_controller->addRotator(current_request->getAxis(), current_request->getRotatorType(),
                                                current_request->getUseZeroLimit());
        break;
      }
      case RotatorControllerRequest::ROTATE_ANGLE:
      {
        bool is_moving = true;
        rotator_controller->isMoving(is_moving, current_request->getAxis());
        if (is_moving)
        {
          request_again = true;
        }
        else
        {
          result = rotator_controller->rotateAngle(current_request->getAngle(), current_request->getAxis());
        }
        break;
      }
      case RotatorControllerRequest::ROTATE_ANGLE_RELATIVE:
      {
        bool is_moving = true;
        rotator_controller->isMoving(is_moving, current_request->getAxis());
        if (is_moving)
        {
          request_again = true;
        }
        else
        {
          result = rotator_controller->rotateAngleRelative(current_request->getAngle(), current_request->getAxis());
        }
        break;
      }
      case RotatorControllerRequest::ROTATE_TWO_AXIS:
      {
        bool is_moving1 = true;
        bool is_moving2 = true;
        rotator_controller->isMoving(is_moving1, current_request->getAxis1());
        rotator_controller->isMoving(is_moving2, current_request->getAxis2());
        if (is_moving1 || is_moving2)
        {
          request_again = true;
        }
        else
        {
          result = rotator_controller->rotateTwoAxis(current_request->getAngle1(), current_request->getAxis1(),
                                                     current_request->getAngle2(), current_request->getAxis2());
        }
        break;
      }
      case RotatorControllerRequest::GET_CURRENT_ANGLE:
      {
        float angle = 0;
        result      = rotator_controller->getCurrentAngle(angle, current_request->getAxis());
        new_result->setAngle(angle);
        break;
      }
      case RotatorControllerRequest::MOVE:
      {
        bool is_moving = true;
        if (type_ == RotatorControllerFactory::RotatorControllerType::CL_01A)
        {
          is_moving = false;
        }
        else
        {
          rotator_controller->isMoving(is_moving, current_request->getAxis());
        }
        if (is_moving)
        {
          request_again = true;
        }
        else
        {
          result = rotator_controller->move(current_request->getPosition(), current_request->getAxis());
        }
        break;
      }
      case RotatorControllerRequest::GET_CURRENT_POSITION:
      {
        float position = 0;
        result         = rotator_controller->getCurrentPosition(position, current_request->getAxis());
        new_result->setPosition(position);
        break;
      }
      case RotatorControllerRequest::RESET_ZERO_POSITION:
      {
        bool is_moving = true;
        rotator_controller->isMoving(is_moving, current_request->getAxis());
        if (is_moving)
        {
          request_again = true;
        }
        else
        {
          result = rotator_controller->resetZeroPosition(current_request->getAxis());
        }
        break;
      }
      case RotatorControllerRequest::STOP_ROTATOR:
      {
        result = rotator_controller->stopRotator(current_request->getAxis());
        break;
      }
      case RotatorControllerRequest::SET_ROTATOR_SPEED:
      {
        result = rotator_controller->setRotatorSpeed(current_request->getSpeed(), current_request->getAxis());
        break;
      }
      case RotatorControllerRequest::SET_ROTATOR_ACC:
      {
        result = rotator_controller->setAcceleration(current_request->getAcc(), current_request->getAxis());
        break;
      }
      case RotatorControllerRequest::IS_MOVING:
      {
        bool is_moving = true;
        result         = rotator_controller->isMoving(is_moving, current_request->getAxis());
        new_result->setIsMoving(is_moving);
        break;
      }
      case RotatorControllerRequest::SET_USER_ORIGINAL:
      {
        bool is_moving = true;
        rotator_controller->isMoving(is_moving, current_request->getAxis());
        if (is_moving)
        {
          request_again = true;
        }
        else
        {
          result = rotator_controller->setUserOriginal(current_request->getAxis());
        }
        break;
      }
      case RotatorControllerRequest::GO_ORIGINAL:
      {
        bool is_moving = true;
        rotator_controller->isMoving(is_moving, current_request->getAxis());
        if (is_moving)
        {
          request_again = true;
        }
        else
        {
          result = rotator_controller->goOrigion(current_request->getAxis());
        }
        break;
      }
      case RotatorControllerRequest::UNKNOW:
      {
        result = false;
        break;
      }
      }
      if (request_again)
      {
        RotatorControllerShareData::getInstance()->getRotatorShareDataByID(id_)->addRequest(current_request->getID(),
                                                                                            current_request);
      }
      else
      {
        new_result->setIsSuccess(result);
        RotatorControllerShareData::getInstance()->getRotatorShareDataByID(id_)->addResult(current_request->getID(),
                                                                                           new_result);
      }
    }
    std::this_thread::sleep_for(std::chrono::milliseconds(200));
  }
}
}  // namespace lidar
}  // namespace robosense