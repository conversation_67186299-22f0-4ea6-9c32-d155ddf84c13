﻿/******************************************************************************
 * Copyright 2022 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#include "asd_rotator_controller/asd_rotator_controller.h"
#include <string>
#include <thread>
#include <sstream>
#include <iomanip>
#if __has_include("rsfsc_lib/include/rsfsc_log.h")
#  include "rsfsc_lib/include/rsfsc_log.h"
#elif __has_include("rsfsc_lib/rsfsc_log.h")
#  include "rsfsc_lib/rsfsc_log.h"
#elif __has_include("rsfsc_log/rsfsc_log.h")
#  include "rsfsc_log/rsfsc_log.h"
#else
#  include "rsfsc_log.h"
#endif
namespace robosense
{
namespace lidar
{
AsdRotatorController::AsdRotatorController(const std::string& _port_name) :
  serial_port_(new QSerialPort()), speed_(5), min_range_(-360), max_range_(1080)
{
  serial_port_->setPortName(_port_name.data());
}

AsdRotatorController::~AsdRotatorController()
{
  disconnect();
  if (serial_port_ != nullptr)
  {
    delete serial_port_;
    serial_port_ = nullptr;
  }
}

bool AsdRotatorController::connect()
{
  if (serial_port_ == nullptr)
  {
    return false;
  }
  if (!serial_port_->isOpen())
  {
    if (serial_port_->open(QIODevice::ReadWrite))
    {
      serial_port_->setBaudRate(QSerialPort::Baud115200);
      serial_port_->setDataBits(QSerialPort::Data8);
      serial_port_->setStopBits(QSerialPort::OneStop);
      serial_port_->setFlowControl(QSerialPort::NoFlowControl);
      serial_port_->setParity(QSerialPort::NoParity);
      serial_port_->clearError();
      serial_port_->clear();
      RSFSCLog::getInstance()->info(u8"连接串口成功");
      return init();
    }
    RSFSCLog::getInstance()->error(u8"打开串口失败");
    return false;
  }
  RSFSCLog::getInstance()->info(u8"串口已连接");
  return true;
}

bool AsdRotatorController::disconnect()
{
  if (serial_port_ == nullptr)
  {
    return false;
  }
  if (serial_port_->isOpen())
  {
    serial_port_->clear();
    serial_port_->close();
    RSFSCLog::getInstance()->info(u8"关闭串口成功");
    return true;
  }
  RSFSCLog::getInstance()->info(u8"串口未连接");
  return true;
}

bool AsdRotatorController::addRotator(const RotatorAxis /*_axis*/,
                                      const std::string& /*_rotator_type*/,
                                      bool /*_use_zero_limit*/)
{
  RSFSCLog::getInstance()->error(u8"[AsdRotatorController::addRotator] 当前控制器不支持");
  return false;
}

bool AsdRotatorController::rotateAngle(const float _angle_in_degree, const RotatorAxis /*_axis*/)
{
  if (!serial_port_->isOpen())
  {
    RSFSCLog::getInstance()->error(u8"串口未连接");
    return false;
  }
  if (_angle_in_degree > max_range_ || _angle_in_degree < min_range_)
  {
    RSFSCLog::getInstance()->error(u8"旋转角度超过限位");
    return false;
  }
  bool is_moving = true;
  if (!isMoving(is_moving, RotatorAxis::X))
  {
    return false;
  }
  if (is_moving)
  {
    return false;
  }
  std::ostringstream speed_str;
  speed_str << std::fixed << std::setprecision(3) << speed_;
  writeCommand("MOVEABS " + fmt::format("{:.3f}", _angle_in_degree) + " " + speed_str.str() + "\r");
  if (readData().find("ERR") != std::string::npos)
  {
    RSFSCLog::getInstance()->error(u8"控制电机移动失败");
    return false;
  }
  return true;
}

bool AsdRotatorController::rotateTwoAxis(const float /*_angle_in_degree1*/,
                                         const RotatorAxis /*_axis1*/,
                                         const float /*_angle_in_degree2*/,
                                         const RotatorAxis /*_axis2*/)
{
  RSFSCLog::getInstance()->error(u8"[AsdRotatorController::rotateTwoAxis] 当前控制器不支持");
  return false;
}

bool AsdRotatorController::getCurrentAngle(float& _angle_in_degree, const RotatorAxis /*_axis*/)
{
  if (!serial_port_->isOpen())
  {
    RSFSCLog::getInstance()->error(u8"串口未连接");
    return false;
  }
  writeCommand("PFB<D8>\r");
  std::string data = readData();

  std::string prefix = "PFB<D8>";
  std::string suffix = "[deg]";

  size_t start = data.find(prefix);
  size_t end   = data.find(suffix);

  if (start == std::string::npos || end == std::string::npos || end <= start + prefix.length())
  {
    RSFSCLog::getInstance()->error(u8"解析获取电机角度失败{}", data);
    return false;
  }

  std::string angle_str = data.substr(start + prefix.length(), end - (start + prefix.length()));

  try
  {
    _angle_in_degree = std::stof(angle_str);
    return true;
  }
  catch (const std::invalid_argument& e)
  {
    RSFSCLog::getInstance()->error(u8"角度转换失败: 无效的数字格式");
    return false;
  }

  return true;
}

bool AsdRotatorController::move(const float /*_position_in_millimeter*/, const RotatorAxis /*_axis*/)
{
  RSFSCLog::getInstance()->error(u8"[AsdRotatorController::move] 当前控制器不支持");
  return false;
}

bool AsdRotatorController::getCurrentPosition(float& /*_position_in_millimeter*/, const RotatorAxis /*_axis*/)
{
  RSFSCLog::getInstance()->error(u8"[AsdRotatorController::getCurrentPosition] 当前控制器不支持");
  return false;
}

bool AsdRotatorController::resetZeroPosition(const RotatorAxis /*_axis*/)
{
  if (!serial_port_->isOpen())
  {
    RSFSCLog::getInstance()->error(u8"串口未连接");
    return false;
  }
  writeCommand("HOMECMD \r");
  if (readData().find("HOMECMD \r") == std::string::npos)
  {
    RSFSCLog::getInstance()->error(u8"控制电机回零失败");
    return false;
  }
  return true;
}

bool AsdRotatorController::stopRotator(const RotatorAxis /*_axis*/)
{
  if (!serial_port_->isOpen())
  {
    RSFSCLog::getInstance()->error(u8"串口未连接");
    return false;
  }
  writeCommand("K \r");
  if (readData().find("K \r") == std::string::npos)
  {
    RSFSCLog::getInstance()->error(u8"停止电机失败");
    return false;
  }
  return true;
}

bool AsdRotatorController::setRotatorSpeed(const int _speed, const RotatorAxis /*_axis*/)
{
  if (!serial_port_->isOpen())
  {
    RSFSCLog::getInstance()->error(u8"串口未连接");
    return false;
  }
  float speed_temp = static_cast<float>(_speed) / 1000.0F;
  speed_temp       = speed_temp > 0.2F ? speed_temp : 0.2F;
  speed_temp       = speed_temp < 50.0F ? speed_temp : 50.0F;
  speed_           = speed_temp;
  return true;
}

bool AsdRotatorController::isMoving(bool& _is_moving, const RotatorAxis /*_axis*/)
{
  if (!serial_port_->isOpen())
  {
    RSFSCLog::getInstance()->error(u8"串口未连接");
    return false;
  }
  writeCommand("STOPPED \r");
  _is_moving = (readData().find('0') != std::string::npos);
  return true;
}

// NOLINTNEXTLINE(bugprone-easily-swappable-parameters)
bool AsdRotatorController::setSoftLimit(const float /*_min_range*/,
                                        const float /*_max_range*/,
                                        const RotatorAxis /*_axis*/)
{
  // TODO: implement this function
  RSFSCLog::getInstance()->error(u8"此类型转台尚未实现软限位功能");
  return false;
}

bool AsdRotatorController::setAcceleration(const int _acc, const RotatorAxis /*_axis*/)
{
  if (!serial_port_->isOpen())
  {
    RSFSCLog::getInstance()->error(u8"串口未连接");
    return false;
  }
  writeCommand("ACC " + std::to_string(_acc) + "\r");
  if (readData().find("ACC") == std::string::npos)
  {
    RSFSCLog::getInstance()->error(u8"设置电机加速度失败");
    return false;
  }
  return true;
}
bool AsdRotatorController::writeCommand(const std::string& _command)
{
  RSFSCLog::getInstance()->debug(u8"上位机写入命令[" + _command + "]");
  serial_port_->write(_command.data());
  return serial_port_->waitForBytesWritten(1000);
}

std::string AsdRotatorController::readData()
{
  std::this_thread::sleep_for(std::chrono::milliseconds(200));
  std::string data;
  if (serial_port_->waitForReadyRead(1000))
  {
    data = serial_port_->readAll().toStdString();
    RSFSCLog::getInstance()->debug(u8"电机控制器回复[" + data + "]");
  }
  return data;
}

bool AsdRotatorController::init()
{
  if (!serial_port_->isOpen())
  {
    RSFSCLog::getInstance()->error(u8"串口未连接");
    return false;
  }
  writeCommand("EN \r");
  if (readData().find("EN \r") == std::string::npos)
  {
    RSFSCLog::getInstance()->error(u8"初始化电机控制器失败");
    return false;
  }
  return true;
}
}  // namespace lidar
}  // namespace robosense