# 参考https://releases.llvm.org/13.0.0/tools/clang/tools/extra/docs/clang-tidy/index.html
# 参考https://releases.llvm.org/13.0.0/tools/clang/tools/extra/docs/clang-tidy/checks/list.html
# 参考https://releases.llvm.org/13.0.0/tools/clang/tools/extra/docs/clang-tidy/checks/readability-identifier-naming.html
Checks: "*,
  -android-*,
  -objc-*,
  -abseil*,
  -llvmlibc*,
  -altera*,
  -modernize-use-auto,
  -modernize-pass-by-value,
  -modernize-use-trailing-return-type,
  -readability-magic-numbers,
  -readability-redundant-access-specifiers,
  -readability-identifier-naming,
  -readability-avoid-const-params-in-decls,
  -cppcoreguidelines-avoid-magic-numbers,
  -cppcoreguidelines-owning-memory,
  -cppcoreguidelines-pro-type-union-access,
  -google-readability-todo,
  -google-runtime-references,
  -google-readability-braces-around-statements,
  -fuchsia-default-arguments-calls,
  -fuchsia-statically-constructed-objects,
  -fuchsia-default-arguments-declarations,
  -fuchsia-overloaded-operator,
  -llvm-header-guard,
  -cert-env33-c,
  -hicpp-use-auto,
  -hicpp-member-init,
  -hicpp-avoid-c-arrays,
  -hicpp-use-nullptr,
  -hicpp-braces-around-statements,
  -clang-diagnostic-error,
  -misc-non-private-member-variables-in-classes,
  -modernize-avoid-c-arrays"
# misc-non-private-member-variables-in-classes is same as cppcoreguidelines-non-private-member-variables-in-classes
# hicpp-member-init is same as cppcoreguidelines-pro-type-member-init
# readability-identifier-naming is check in another command
# hicpp-avoid-c-arrays and modernize-avoid-c-arrays is same as cppcoreguidelines-avoid-c-arrays
# hicpp-use-nullptr is same as modernize-use-nullptr
# readability-avoid-const-params-in-decls some new fish will mistake param as global?
# google-readability-braces-around-statements and hicpp-braces-around-statements is same as readability-braces-around-statements
# cppcoreguidelines-pro-type-union-access, PCL has many this warning
# altera*, this is for altera FPGA
CheckOptions:
  - { key: bugprone-argument-comment.StrictMode, value: 1 }
  - { key: readability-function-size.LineThreshold, value: 200 }

  # Type
  - { key: readability-identifier-naming.AbstractClassCase, value: CamelCase }
  - { key: readability-identifier-naming.ClassCase, value: CamelCase }
  - { key: readability-identifier-naming.EnumCase, value: CamelCase }
  - { key: readability-identifier-naming.UnionCase, value: CamelCase }
  - { key: readability-identifier-naming.StructCase, value: CamelCase }
  # Type aliasing - using MyType = int
  - { key: readability-identifier-naming.TypeAliasCase, value: CamelCase }
  - { key: readability-identifier-naming.TypedefCase, value: CamelCase }
  - {
      key: readability-identifier-naming.TemplateParameterCase,
      value: CamelCase,
    }
  - {
      key: readability-identifier-naming.TemplateTemplateParameterCase,
      value: CamelCase,
    }
  - {
      key: readability-identifier-naming.TypeTemplateParameterCase,
      value: CamelCase,
    }

  # member variables
  - { key: readability-identifier-naming.ClassMemberCase, value: lower_case }
  - { key: readability-identifier-naming.ClassMemberSuffix, value: _ }
  # - { key: readability-identifier-naming.MemberCase, value: lower_case }
  # - { key: readability-identifier-naming.MemberSuffix, value: _ }
  - { key: readability-identifier-naming.PrivateMemberCase, value: lower_case }
  - { key: readability-identifier-naming.PrivateMemberSuffix, value: _ }
  - {
      key: readability-identifier-naming.ProtectedMemberCase,
      value: lower_case,
    }
  - { key: readability-identifier-naming.ProtectedMemberSuffix, value: _ }
  - { key: readability-identifier-naming.PublicMemberCase, value: lower_case }
  # - { key: readability-identifier-naming.PublicMemberSuffix, value: _ }
  - { key: readability-identifier-naming.ClassConstantCase, value: UPPER_CASE }
  # - { key: readability-identifier-naming.ClassConstantSuffix, value: _ }
  - { key: readability-identifier-naming.ConstantMemberCase, value: UPPER_CASE }
  # - { key: readability-identifier-naming.ConstantMemberSuffix, value: _ }

  # function
  - { key: readability-identifier-naming.FunctionCase, value: camelBack }
  - {
      key: readability-identifier-naming.ConstexprFunctionCase,
      value: camelBack,
    }
  - { key: readability-identifier-naming.GlobalFunctionCase, value: camelBack }
  - { key: readability-identifier-naming.ClassMethodCase, value: camelBack }
  - { key: readability-identifier-naming.ConstexprMethodCase, value: camelBack }
  - { key: readability-identifier-naming.MethodCase, value: camelBack }
  - { key: readability-identifier-naming.PrivateMethodCase, value: camelBack }
  - { key: readability-identifier-naming.ProtectedMethodCase, value: camelBack }
  - { key: readability-identifier-naming.PublicMethodCase, value: camelBack }
  - { key: readability-identifier-naming.VirtualMethodCase, value: camelBack }

  # Parameters
  - { key: readability-identifier-naming.ParameterCase, value: lower_case }
  - { key: readability-identifier-naming.ParameterPrefix, value: _ }
  - { key: readability-identifier-naming.ParameterPackCase, value: lower_case }
  - { key: readability-identifier-naming.ParameterPackPrefix, value: _ }
  - {
      key: readability-identifier-naming.ConstantParameterCase,
      value: lower_case,
    }
  - { key: readability-identifier-naming.ConstantParameterPrefix, value: _ }
  # - { key: readability-identifier-naming.ConstantPointerParameterCase, value: lower_case }
  # - { key: readability-identifier-naming.ConstantPointerParameterPrefix, value: _ptr_ }
  # - { key: readability-identifier-naming.PointerParameterCase, value: lower_case }
  # - { key: readability-identifier-naming.PointerParameterPrefix, value: _ptr_ }
  # - { key: readability-identifier-naming.ValueTemplateParameterCase, value: lower_case }
  # - { key: readability-identifier-naming.ValueTemplateParameterPrefix, value: _ptr_ }

  # Variables
  - { key: readability-identifier-naming.ConstantCase, value: UPPER_CASE }
  - { key: readability-identifier-naming.GlobalVariableCase, value: lower_case }
  - { key: readability-identifier-naming.GlobalVariablePrefix, value: g_ }
  - { key: readability-identifier-naming.GlobalConstantCase, value: UPPER_CASE }
  - { key: readability-identifier-naming.GlobalConstantPrefix, value: G_ }
  # - { key: readability-identifier-naming.GlobalConstantPointerCase, value: UPPER_CASE }
  # - { key: readability-identifier-naming.GlobalConstantPointerPrefix, value: gptr_ }
  # - { key: readability-identifier-naming.GlobalPointerCase, value: lower_case }
  # - { key: readability-identifier-naming.GlobalPointerPrefix, value: gptr_ }
  - {
      key: readability-identifier-naming.ConstexprVariableCase,
      value: UPPER_CASE,
    }
  - { key: readability-identifier-naming.LocalConstantCase, value: UPPER_CASE }
  # - { key: readability-identifier-naming.LocalConstantPointerCase, value: UPPER_CASE }
  # - { key: readability-identifier-naming.LocalConstantPointerPrefix, value: ptr_ }
  # - { key: readability-identifier-naming.LocalPointerCase, value: lower_case }
  # - { key: readability-identifier-naming.LocalPointerPrefix, value: ptr_ }
  - { key: readability-identifier-naming.LocalVariableCase, value: lower_case }
  - { key: readability-identifier-naming.VariableCase, value: lower_case }
  - { key: readability-identifier-naming.StaticConstantCase, value: UPPER_CASE }
  # - { key: readability-identifier-naming.StaticConstantPrefix, value: g_ }
  - { key: readability-identifier-naming.StaticVariableCase, value: lower_case }
  # - { key: readability-identifier-naming.StaticVariablePrefix, value: g_ }

  # Macros
  - {
      key: readability-identifier-naming.MacroDefinitionCase,
      value: UPPER_CASE,
    }
  # Enum
  - { key: readability-identifier-naming.EnumConstantCase, value: UPPER_CASE }

  # Namespaces
  - { key: readability-identifier-naming.NamespaceCase, value: lower_case }
  - { key: readability-identifier-naming.NamespaceIgnoredRegexp, value: "Ui"}
  - {
      key: readability-identifier-naming.InlineNamespaceCase,
      value: lower_case,
    }

# format when apply fixes
FormatStyle: "file"
# HeaderFilterRegex: "./*"
WarningsAsErrors: "readability-identifier-naming, clang-diagnostic-return-type"
