﻿波特率:115200,n,8,1

1.定速运行,不指定终点
	正向:0x55,0xaa,0x06,0x09,speed,0x00,0x00,0x00,0xc3;
	负向:0x55,0xaa,0x06,0x0A,speed,0x00,0x00,0x00,0xc3;
	
	speed=速度(范围:400-40000)

	比如,需要控制器以2400HZ的速度朝正向运行,则指令是:
	十进制:2400
	16进制:0x0960
	0x55,0xaa,0x06,0x09,0x60,0x09,0x00,0x00,0x00,0xc3;


2.定速运行,指定终点
	绝对运动:0x55,0xaa,0x07,speed,steps,0xc3;
	增量运动:0x55,0xaa,0x08,speed,steps,0xc3;
	speed=速度(2个字节,400-40000)
	steps=距离(4个字节,支持负值)


3.暂停运行
	0x55,0xaa,0x02,0x00,0x00,0x00,0x00,0x00,0x00,0xc3;


4.回程序零
	0x55,0xaa,0x07,speed,0x00,0x00,0x00,0x00,0xc3


5.回机械零
	朝正向:0x55,0xaa,0x0b,0x09,speed,0x00,0x00,0x00,0xc3
	朝负向:0x55,0xaa,0x0b,0x0a,speed,0x00,0x00,0x00,0xc3
	
6.取状态
	0x55,0xaa,0x0c,0x00,0x00,0x00,0x00,0x00,0x00,0xc3;
	取当前的控制的状态,返回一共8个字节
	第1-4字节,当前坐标,long型,高位在前,低位在后
	第5-6字节,当前定时器值,根据下面公式,可得到实际速度值
		speed=14745600/(65535 + 46 - (buff[4]<<8+buff[5])),此值计算结果为设定速度.
	第7字节表示输入状态(0,表示输入有效,1表示输入无效)
		bit2:正限
		bit3:零点
		bit4:负限
		bit5:入四
	第8字节表示当前的工作状态
		0x00:上电复位
		0x01:定量
		0x02:未定义
		0x04:手动
		0x08:回零
		0x10:未定义
		0x20:停止
7.输出
	0x55,0xaa,0x0d,port,status,0x00,0x00,0x00,0x00,0xc3
	port取值为1或者2,代表输出口1和输出口2
	status取值为0或者非0,0代表关闭输出,1代表打开输出.
		
上面数据中,speed 为unsigned int 型,steps为 long 型


参数操作说明
按下OK键,AUTO灯亮,按下左键进入参数设定
参数含义可参考CL-01A说明书
SPD参数也就是HF