// cSpell Settings
{
  // Version of the setting file.  Always 0.2
  "version": "0.2",
  // language - current active spelling language
  "language": "en",
  // words - list of words to be always considered correct
  "words": [
    "<PERSON><PERSON><PERSON><PERSON>",
    "<PERSON><PERSON>",
    "ASD's",
    "AUTOSAR",
    "Axisd",
    "Boci<PERSON>",
    "bugprone",
    "CALIB",
    "chardet",
    "CHNL",
    "CICD",
    "combobox",
    "Contant",
    "DIFOP",
    "dspinbox",
    "dtags",
    "Eigen",
    "fusa",
    "gbit",
    "gedit",
    "gitsubmodule",
    "gmock",
    "googletest",
    "gtest",
    "HAIKANG",
    "hhmmss",
    "hicpp",
    "HOMECMD",
    "lable",
    "Liang",
    "libqt",
    "librotator",
    "librsfsc",
    "lidars",
    "lineedit",
    "loguru",
    "LPTOP",
    "mainwindow",
    "MEMS",
    "MEMSTCP",
    "MEMSUDP",
    "MINDVISI<PERSON>",
    "MOVEABS",
    "MSOP",
    "munubar",
    "NOLINT",
    "NOLINTNEXTLINE",
    "opencv",
    "OPENMP",
    "pcap",
    "PEINPOS",
    "PEINPOSTIME",
    "Pixmap",
    "qmake",
    "QMESSAGE",
    "qobject",
    "qsetting",
    "qsettings",
    "qstyleoption",
    "qtserialport",
    "Quaterniond",
    "REGRST",
    "rheight",
    "robosense",
    "rosnode",
    "rsdata",
    "rsfsc",
    "RSFSCLIB",
    "RSFSCLOG",
    "RSFSCQSettings",
    "rsfsg's",
    "rslidar",
    "RSLOG",
    "rwidth",
    "SATU",
    "Shen",
    "SHIYAN",
    "spdlog",
    "spdlogger",
    "stdstr",
    "suteng",
    "tablewidget",
    "tabwidget",
    "THRE",
    "TMPC",
    "TMPCOE",
    "TMPDIF",
    "utest",
    "Wenduo",
    "widgetaction",
    "WORKCOE",
    "YAMLCPP",
    "Ying",
    "Zhang",
    "Zheng",
    "ZHONG"
  ],
  // flagWords - list of words to be always considered incorrect
  // This is useful for offensive words and common spelling errors.
  // For example "hte" should be "the"
  "flagWords": [],
  "dictionaries": [
    "cpp",
    "python",
    "bash",
    "en_us",
    "latex",
    "public-licenses",
    "softwareTerms"
  ]
}
