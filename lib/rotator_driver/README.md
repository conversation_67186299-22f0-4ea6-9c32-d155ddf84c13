﻿# 转台控制器驱动

本工程用于驱动现有各个运动控制器，包括：  
1、北京北光世纪仪器有限公司(BOCIC)的SC100系列步进电机控制器  
2、雅科贝思精密机电上海有限公司(Akribis)的ASD电机控制器  
3、北京海杰嘉创科技有限公司的CL-01A电机控制器  

本工程在以下环境编译通过并能运行example的功能：

|    Ubuntu 1604     | Windows 10 |
| :----------------: | :--------: |
| Qt 5.5.1(默认版本) |  Qt 5.5.1  |

## 1 安装依赖

### 1.1 QtSerialPort

这个官方说Qt5.1以后会自带，但Ubuntu系统里没这个，

可以通过apt安装：

```bash
sudo apt-get install libqt5serialport5-dev
```

或者[下载源码编译安装]](https://wiki.qt.io/Qt_Serial_Port):

```bash
git clone git://code.qt.io/qt/qtserialport.git
cd qtserialport
git checkout v5.5.1
mkdir build
cd build
qmake ../qtserialport.pro
make
sudo make install
```

通过上述安装，QtSerialPort就被安装到`/usr/include/x86_64-linux-gnu/qt5/QtSerialPort/`目录下了。

## 2. 基本框架

转台控制器的说明书请详见  
ASD：[ASD驱动器说明](/doc/ASD/ASD驱动器用户最新版%20201604-05_CN.pdf)  
北光世纪：[SC系列步进电机控制器说明书](/doc/北光世纪/SC系列步进电机控制器说明书.pdf)  
CL-01A:[CL-01A说明书](/doc/CL-01A/CL-01A说明书.pdf)  

### 2.1 设计原理

本模块使用监听者模式加工厂模式实现，可参照![监听者模式](/doc/img/监听者模式.jpeg)，一个控制器一个线程，可实现多控制器多电机下的联动，具体实现可看详细代码

### 2.2 使用方法

北光世纪电机控制器使用前请在控制器将其设置成联机模式，最好将其联机设置成启动默认联机

具体使用方法请参考`example.cpp`的示例以及`rotator_controller_interface.h`中对各个函数的注释。

如果想要增加自己的功能，可以扩展接口类方法

对本工程示例的**编译**，可以在当前目录下新建一个`build`目录，然后在`build`目录下运行`cmake ..-DBUILD_ROTATOR_DRIVER_TEST=ON`和`make`

### 2.3 CMake配置方式

在需要使用到本模块的工程，先添加到git

```git
git submodule add url lib/rotator_driver
```

然后配置cmake，在`CMakeLists.txt`下添加

```cmake
add_subdirectory(lib/rotator_driver)
target_include_directories(rotator_driver_object PRIVATE lib)

add_executable(${your_project_name})
target_link_libraries(${your_project_name} rotator_driver_shared)

install(
  TARGETS ${your_project_name}
          rotator_driver_shared
  PERMISSIONS
    OWNER_EXECUTE
    OWNER_WRITE
    OWNER_READ
    GROUP_EXECUTE
    GROUP_READ
    WORLD_EXECUTE
    WORLD_READ
  ARCHIVE DESTINATION ${CMAKE_INSTALL_PREFIX}/lib/${PROJECT_NAME}
  LIBRARY DESTINATION ${CMAKE_INSTALL_PREFIX}/lib/${PROJECT_NAME}
  RUNTIME DESTINATION ${CMAKE_INSTALL_PREFIX}/bin/${PROJECT_NAME})
```

**静态链接**将上面cmake指令中的`rotator_driver_shared`更换为`rotator_driver_static`

**动态链接**参照上面cmake指令

## 其他疑问

### 我怎么手动单步控制

代码有开发手动控制界面（rotator_view/rotator_control_view.h）,使用时，可以使用按钮进行独立界面调用

```cpp
const int ROTATOR_THREAD_ID1 = 1;
RotatorControlView rotator_control_view_;

rotator_control_view_ = new RotatorControlView(ROTATOR_THREAD_ID1, this);
rotator_control_view_->setWindowIcon(QIcon(":/img/icon.png"));

void MainWindow::slotShowRotatorControlView()
{
  if (rotator_control_view_->isVisible())
  {
    rotator_control_view_->raise();
    rotator_control_view_->activateWindow();
  }
  else
  {
    rotator_control_view_->show();
  }
}

void MainWindow::closeEvent(QCloseEvent* _event)
{
  if (rotator_control_view_->isVisible())
  {
    rotator_control_view_->close();
  }
}
```

也可以嵌入自己的UI界面使用

```cpp
const int ROTATOR_THREAD_ID1 = 1;
RotatorControlView rotator_control_view_;

rotator_control_view_ = new RotatorControlView(ROTATOR_THREAD_ID1, this);
rotator_control_view_->setWindowFlags(Qt::CustomizeWindowHint | Qt::FramelessWindowHint);

QVBoxLayout* layout_main = new QVBoxLayout;
layout_main->addWidget(rotator_control_view_);
```

### 多个转台情况下怎么使用手动控制界面

可以构建多个转台手动控制界面对象并传入不同的转台ID，针对这些不同的转台ID会生成不同的配置文件，不需要担心配置记录异常的问题
