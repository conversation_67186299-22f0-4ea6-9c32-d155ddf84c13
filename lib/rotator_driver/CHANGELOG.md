﻿# Changelog
## v3.1.0 - 20241018
### Feat:
+ 合并develop_cl_01a分支的内容
## v3.0.6 - 20231214
### Fix:
+ MC600：修复两轴运动后未能完全读取数据的问题
## v3.0.5 - 20231116
### Feat:
+ MC600：接受到有数据之后延时再读取，防止数据未读全
## v3.0.5 - 20231116
### Feat:
+ MC600:更新回零速度设置，需要小于初速度
## v3.0.5 - 20231116
### Feat:
+ MC600:转台速度开放设置，各速度按工作速度的比例自适应设置
## v3.0.4 - 20231113
### Fixed
+ MC600:修复析构时线程未退出的问题
## v3.0.4 - 20231113
### Fixed
+ MC600:修复回零运动消息解析逻辑错误的问题
## v3.0.4 - 20231110
### Fixed
+ MC600:修复单轴运动时消息解析混乱的问题
## v3.0.4 - 20231031
### Fixed
+ MC600:修复运动完成后没有恢复运动标志的问题
## v3.0.4 - 20231031
### Added
+ MC600:完善运动完成的判断
## v3.0.3 - 20231026
### Added
+ MC600:增加是否在运动的判断方式，补充相关接口
+ 增加一些公共接口
## v3.0.2 - 20231024
### Added
+ 增加monitor相对运动的指令
## v3.0.1 - 20231018
### Added
+ 增加monitor相对运动的指令
## v3.0.0 - 20231018
### Added
+ 增加MC600的电机控制
## unreleased
### Fixed
+ 修复在使用ASD转台时选择北光世纪控制器导致闪退的问题

## v2.1.0 - 20230517
### Added
+ 增加QT示例

## v2.0.0 - 20230305
### Changed
+ 修改工程框架为监听者模式

### Added
+ 增加北光世纪控制说明书
+ 增加北光世纪双轴插补函数
+ 增加CL-01A控制说明书
+ 增加CL-01A新运动控制器
+ 增加ASD控制说明书

## v1.4.1 - 20220930
### Changed
+ 更新RSFSCLog至v1.1.1

## v1.4.0 - 20220706
### Added
+ Add RSFSCLog

### Fixed
+ Dont check available port before open, just open directly

### Changed
+ Change to namespace robosense and namespace lidar

## v1.3.0 - 20210908

### Added
+ Support ASD rotator

## v1.2.0 - 20210401
### Added
+ Visual Studio support

## v1.0.1 - 20210219
### Fixed
+ Bug of use_zero_limit_ which cause it is not be used
  
## v1.0.0 - 20210126
### Added
+ Support Windows 
+ Support MRS102, MRS402, MGC103
+ Try to find zero limit when use_zero_limit_
+ Add reset zero speed
