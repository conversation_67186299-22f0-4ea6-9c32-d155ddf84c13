﻿if(BUILD_ROTATOR_DRIVER_QT_EXAMPLE)
  find_package(
    Qt5
    COMPONENTS Widgets Ser<PERSON>Port
    REQUIRED)
  set(CMAKE_AUTOMOC ON)
  set(CMAKE_AUTORCC ON)
  set(CMAKE_AUTOUIC ON)
  add_executable(rotator_controller_example qt_example.cpp mainwindow.h mainwindow.cpp)
  target_include_directories(rotator_controller_example PRIVATE SYSTEM ${Qt5Widgets_INCLUDE_DIRS})
  target_link_libraries(rotator_controller_example Qt5::Core Qt5::Widgets)
else()
  add_executable(rotator_controller_example example.cpp)
endif(BUILD_ROTATOR_DRIVER_QT_EXAMPLE)
target_link_libraries(rotator_controller_example rotator_driver_shared)
set_target_properties(
  rotator_controller_example
  PROPERTIES CXX_STANDARD 14
             CXX_STANDARD_REQUIRED YES
             CXX_EXTENSIONS NO)
