﻿/******************************************************************************
 * Copyright 2022 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#include "rotator_controller_monitor.h"
#include "rotator_controller_share_data.h"
#include <chrono>
#include <iostream>
#include <memory>
#include <string>
#include <thread>

using namespace robosense::lidar;

enum ControllerExample
{
  ASD = 0,
  BOCIC,
  CL_01A
};

int main()
{
  ControllerExample example = ControllerExample::BOCIC;
  switch (example)
  {
  case ASD:
  {
    constexpr int THREAD1 = 1;
    std::unique_ptr<RotatorControllerMonitor> monitor(
      new RotatorControllerMonitor(THREAD1, RotatorControllerFactory::RotatorControllerType::ASD, "ttyUSB0"));
    RotatorControllerShareData::getInstance()->addRotatorShareData(THREAD1);
    monitor->start();
    if (!RotatorControllerShareData::getInstance()->connect(THREAD1))
    {
      std::cout << "connect rotator controller1 failed" << std::endl;
      return -1;
    }
    if (!RotatorControllerShareData::getInstance()->setRotatorSpeed(THREAD1, 10000))
    {
      std::cout << "set rotator controller1 speed failed" << std::endl;
      return -1;
    }
    int rotator_request_id = RotatorControllerShareData::getInstance()->resetZeroPosition(THREAD1);
    if (!RotatorControllerShareData::getInstance()->getIsSuccessByID(THREAD1, rotator_request_id))
    {
      std::cout << "reset rotator1 of rotator controller1 failed" << std::endl;
      return -1;
    }
    bool is_moving = true;
    while (is_moving)
    {
      RotatorControllerShareData::getInstance()->isMoving(THREAD1, is_moving);
      std::this_thread::sleep_for(std::chrono::milliseconds(200));
    }
    rotator_request_id = RotatorControllerShareData::getInstance()->rotateAngle(THREAD1, 30);
    if (!RotatorControllerShareData::getInstance()->getIsSuccessByID(THREAD1, rotator_request_id))
    {
      std::cout << "rotate rotator1 of rotator controller1 failed" << std::endl;
      return -1;
    }
    is_moving = true;
    while (is_moving)
    {
      RotatorControllerShareData::getInstance()->isMoving(THREAD1, is_moving);
      std::this_thread::sleep_for(std::chrono::milliseconds(200));
    }
    monitor->stop();
    break;
  }
  case BOCIC:
  {
    constexpr int THREAD1 = 1;
    constexpr int THREAD2 = 2;
    std::unique_ptr<RotatorControllerMonitor> monitor1(
      new RotatorControllerMonitor(THREAD1, RotatorControllerFactory::RotatorControllerType::SC101, "ttyUSB0"));
    RotatorControllerShareData::getInstance()->addRotatorShareData(THREAD1);
    std::unique_ptr<RotatorControllerMonitor> monitor2(
      new RotatorControllerMonitor(THREAD2, RotatorControllerFactory::RotatorControllerType::SC101, "ttyUSB1"));
    RotatorControllerShareData::getInstance()->addRotatorShareData(THREAD2);
    monitor1->start();
    monitor2->start();

    if (!RotatorControllerShareData::getInstance()->connect(THREAD1))
    {
      std::cout << "connect rotator controller1 failed" << std::endl;
      return -1;
    }

    if (!RotatorControllerShareData::getInstance()->connect(THREAD2))
    {
      std::cout << "connect rotator controller2 failed" << std::endl;
      return -1;
    }

    if (!RotatorControllerShareData::getInstance()->addRotator(THREAD1, RotatorControllerInterface::RotatorAxis::X,
                                                               "MRS102", true))
    {
      std::cout << "add rotator1 of rotator controller1 failed" << std::endl;
      return -1;
    }

    if (!RotatorControllerShareData::getInstance()->addRotator(THREAD2, RotatorControllerInterface::RotatorAxis::X,
                                                               "MRS102", true))
    {
      std::cout << "add rotator1 of rotator controller2 failed" << std::endl;
      return -1;
    }

    if (!RotatorControllerShareData::getInstance()->setRotatorSpeed(THREAD1, 3000,
                                                                    RotatorControllerInterface::RotatorAxis::X))
    {
      std::cout << "add rotator1 of rotator controller1 failed" << std::endl;
      return -1;
    }

    if (!RotatorControllerShareData::getInstance()->setRotatorSpeed(THREAD2, 3000,
                                                                    RotatorControllerInterface::RotatorAxis::X))
    {
      std::cout << "add rotator1 of rotator controller2 failed" << std::endl;
      return -1;
    }

    int rotator1_request_id =
      RotatorControllerShareData::getInstance()->resetZeroPosition(THREAD1, RotatorControllerInterface::RotatorAxis::X);
    int rotator2_request_id =
      RotatorControllerShareData::getInstance()->resetZeroPosition(THREAD2, RotatorControllerInterface::RotatorAxis::X);
    if (!RotatorControllerShareData::getInstance()->getIsSuccessByID(THREAD1, rotator1_request_id))
    {
      std::cout << "reset rotator1 of rotator controller1 failed" << std::endl;
      return -1;
    }
    if (!RotatorControllerShareData::getInstance()->getIsSuccessByID(THREAD2, rotator2_request_id))
    {
      std::cout << "reset rotator1 of rotator controller2 failed" << std::endl;
      return -1;
    }

    bool is_moving1 = true;
    bool is_moving2 = true;
    while (is_moving1 || is_moving2)
    {
      RotatorControllerShareData::getInstance()->isMoving(THREAD1, is_moving1,
                                                          RotatorControllerInterface::RotatorAxis::X);
      RotatorControllerShareData::getInstance()->isMoving(THREAD2, is_moving2,
                                                          RotatorControllerInterface::RotatorAxis::X);
      std::this_thread::sleep_for(std::chrono::milliseconds(200));
    }

    rotator1_request_id =
      RotatorControllerShareData::getInstance()->rotateAngle(THREAD1, 130, RotatorControllerInterface::RotatorAxis::X);
    rotator2_request_id =
      RotatorControllerShareData::getInstance()->rotateAngle(THREAD2, 30, RotatorControllerInterface::RotatorAxis::X);
    if (!RotatorControllerShareData::getInstance()->getIsSuccessByID(THREAD1, rotator1_request_id))
    {
      std::cout << "rotate rotator1 of rotator controller1 failed" << std::endl;
      return -1;
    }
    if (!RotatorControllerShareData::getInstance()->getIsSuccessByID(THREAD2, rotator2_request_id))
    {
      std::cout << "rotate rotator1 of rotator controller2 failed" << std::endl;
      return -1;
    }

    std::this_thread::sleep_for(std::chrono::milliseconds(2000));
    float angle = 0;
    if (!RotatorControllerShareData::getInstance()->getCurrentAngle(THREAD1, angle,
                                                                    RotatorControllerInterface::RotatorAxis::X))
    {
      std::cout << "add rotator1 of rotator controller1 failed" << std::endl;
    }
    std::cout << std::to_string(angle) << std::endl;

    if (!RotatorControllerShareData::getInstance()->stopRotator(THREAD1, RotatorControllerInterface::RotatorAxis::X))
    {
      std::cout << "add rotator1 of rotator controller1 failed" << std::endl;
    }

    is_moving1 = true;
    is_moving2 = true;
    while (is_moving1 || is_moving2)
    {
      RotatorControllerShareData::getInstance()->isMoving(THREAD1, is_moving1,
                                                          RotatorControllerInterface::RotatorAxis::X);
      RotatorControllerShareData::getInstance()->isMoving(THREAD2, is_moving2,
                                                          RotatorControllerInterface::RotatorAxis::X);
      std::this_thread::sleep_for(std::chrono::milliseconds(200));
    }
    monitor1->stop();
    monitor2->stop();
    break;
  }
  case CL_01A:
  {
    constexpr int THREAD1 = 1;
    std::unique_ptr<RotatorControllerMonitor> monitor(
      new RotatorControllerMonitor(THREAD1, RotatorControllerFactory::RotatorControllerType::CL_01A, "ttyUSB0"));
    RotatorControllerShareData::getInstance()->addRotatorShareData(THREAD1);
    monitor->start();
    if (!RotatorControllerShareData::getInstance()->connect(THREAD1))
    {
      std::cout << "connect rotator controller1 failed" << std::endl;
      return -1;
    }
    if (!RotatorControllerShareData::getInstance()->setRotatorSpeed(THREAD1, 1000))
    {
      std::cout << "set rotator controller1 speed failed" << std::endl;
      return -1;
    }
    int rotator1_request_id = RotatorControllerShareData::getInstance()->move(THREAD1, 10000);
    if (!RotatorControllerShareData::getInstance()->getIsSuccessByID(THREAD1, rotator1_request_id))
    {
      std::cout << "rotate rotator1 of rotator controller1 failed" << std::endl;
      return -1;
    }
    std::this_thread::sleep_for(std::chrono::milliseconds(2000));
    if (!RotatorControllerShareData::getInstance()->stopRotator(THREAD1))
    {
      std::cout << "stop rotator1 of rotator controller1 failed" << std::endl;
      return -1;
    }
    bool is_moving = true;
    while (is_moving)
    {
      RotatorControllerShareData::getInstance()->isMoving(THREAD1, is_moving);
      std::this_thread::sleep_for(std::chrono::milliseconds(200));
    }
    monitor->stop();
    break;
  }
  }
  return 0;
}