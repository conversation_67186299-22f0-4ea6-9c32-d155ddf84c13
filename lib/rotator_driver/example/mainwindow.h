﻿/******************************************************************************
 * Copyright 2023 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#ifndef ROTATOR_DRIVER_MAIN_WINDOW_H
#define ROTATOR_DRIVER_MAIN_WINDOW_H
#include "rotator_view/rotator_control_view.h"
#include <QtWidgets/QMainWindow>
namespace robosense
{
namespace lidar
{
class MainWindow : public QMainWindow
{
  Q_OBJECT
public:
  explicit MainWindow(int _argc, char** _argv, QWidget* _parent = nullptr);
  explicit MainWindow(MainWindow&&)      = delete;
  explicit MainWindow(const MainWindow&) = delete;
  MainWindow& operator=(MainWindow&&) = delete;
  MainWindow& operator=(const MainWindow&) = delete;
  ~MainWindow() override;

private:
  RotatorControlView* rotator_control_view_;
};
}  // namespace lidar
}  // namespace robosense
#endif  //ROTATOR_DRIVER_MAIN_WINDOW_H