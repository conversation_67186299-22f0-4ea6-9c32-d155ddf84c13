﻿/******************************************************************************
 * Copyright 2022 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#ifndef ROTATOR_CONTROLLER_INTERFACE_H
#define ROTATOR_CONTROLLER_INTERFACE_H
#include <iostream>
#include <string>
namespace robosense
{
namespace lidar
{
class RotatorControllerInterface
{
public:
  RotatorControllerInterface()                                           = default;
  explicit RotatorControllerInterface(RotatorControllerInterface&&)      = delete;
  explicit RotatorControllerInterface(const RotatorControllerInterface&) = delete;
  RotatorControllerInterface& operator=(RotatorControllerInterface&&) = delete;
  RotatorControllerInterface& operator=(const RotatorControllerInterface&) = delete;
  virtual ~RotatorControllerInterface()                                    = default;

public:
  enum RotatorAxis
  {
    X = 0,
    Y,
    Z
  };
  /**
   * @brief connect serial port
   * @param 
   * 
   * @return true if connect successfully, otherwise return false
   */
  virtual bool connect() = 0;
  /**
   * @brief disconnect serial port
   * @param 
   * 
   * @return true if disconnect successfully, otherwise return false
   */
  virtual bool disconnect() = 0;
  /**
   * @brief if you use bocic rotator controller, you must add rotator
   * @param _axis the axis you init, such as RotatorAxis::X,RotatorAxis::Y,RotatorAxis::Z
   * @param _rotator_type the rotator of axis you init, you can use [MRS101, MRS102, MRS103, MRS104, MRS105, MRS402, MGC103, MGC104]
   * @param _use_zero_limit if you set false, reset rotator will call rotate absolute zero angle
   * 
   * @return true if add rotator successfully, otherwise return false
   */
  virtual bool addRotator(const RotatorAxis _axis, const std::string& _rotator_type, bool _use_zero_limit) = 0;
  /**
   * @brief rotate axis by absolute angle
   * @param _angle_in_degree the absolute angle of axis will rotate
   * @param _axis the axis will rotate
   * 
   * @return true if execute rotate angle command successfully, otherwise return false
   */
  virtual bool rotateAngle(const float _angle_in_degree, const RotatorAxis _axis) = 0;
  /**
   * @brief rotate axis by relative angle
   * @param _angle_in_degree the absolute angle of axis will rotate
   * @param _axis the axis will rotate
   * 
   * @return true if execute rotate angle command successfully, otherwise return false
   */
  virtual bool rotateAngleRelative(const float _angle_in_degree, const RotatorAxis _axis) = 0;
  /**
   * @brief rotate two axis by absolute angle
   * @param _angle_in_degree1 the absolute angle of axis1 will rotate
   * @param _axis the axis1 will rotate
   * @param _angle_in_degree2 the absolute angle of axis2 will rotate
   * @param _axis the axis2 will rotate
   * 
   * @return true if execute rotate angle command successfully, otherwise return false
   */
  virtual bool rotateTwoAxis(const float _angle_in_degree1,
                             const RotatorAxis _axis1,
                             const float _angle_in_degree2,
                             const RotatorAxis _axis2) = 0;
  /**
   * @brief get absolute angle of current axis
   * @param _angle_in_degree the absolute angle of axis
   * @param _axis the axis1 that you get the absolute angle
   * 
   * @return true if get angle command successfully, otherwise return false
   */
  virtual bool getCurrentAngle(float& _angle_in_degree, const RotatorAxis _axis) = 0;
  /**
   * @brief move axis by absolute position
   * @param _position_in_millimeter the absolute position of axis will rotate
   * @param _axis the axis will move
   * 
   * @return true if execute move command successfully, otherwise return false
   */
  virtual bool move(const float _position_in_millimeter, const RotatorAxis _axis) = 0;
  /**
   * @brief get the position of current axis
   * @param _position_in_millimeter the position of axis
   * @param _axis the axis1 that you get the position
   * 
   * @return true if get position successfully, otherwise return false
   */
  virtual bool getCurrentPosition(float& _position_in_millimeter, const RotatorAxis _axis) = 0;
  /**
   * @brief reset axis
   * @param _axis the axis will reset
   * 
   * @return true if execute reset command successfully, otherwise return false
   */
  virtual bool resetZeroPosition(const RotatorAxis _axis) = 0;
  /**
   * @brief stop rotator
   * @param _axis the axis will stop
   * 
   * @return true if stop successfully, otherwise return false
   */
  virtual bool stopRotator(const RotatorAxis _axis) = 0;
  /**
   * @brief set the speed of axis
   * @param _speed the speed will set
   * @param _axis the axis will set
   * 
   * @return true if set speed successfully, otherwise return false
   */
  virtual bool setRotatorSpeed(const int _speed, const RotatorAxis _axis) = 0;
  /**
   * @brief get the motion status of axis
   * @param _is_moving the status
   * @param _axis the axis will get
   * 
   * @return true if get status successfully, otherwise return false
   */
  virtual bool isMoving(bool& _is_moving, const RotatorAxis _axis) = 0;

  /**
   * @brief set current position as user original
   * @param _axis the current axis
   * 
   * @return true if execute command successfully, otherwise return false
   */
  virtual bool setUserOriginal(const RotatorAxis _axis)
  {
    std::cout << "该控制器未实现设置用户零点接口!" << std::endl;
    return true;
  }

  /**
   * @brief go original position
   * @param _axis the current axis
   * 
   * @return true if execute command successfully, otherwise return false
   */
  virtual bool goOrigion(const RotatorAxis _axis)
  {
    std::cout << "该控制器未实现用户回零接口!" << std::endl;
    return true;
  }

  virtual bool setSoftLimit(const float _min, const float _max, const RotatorAxis _axis) = 0;

  virtual bool setAcceleration(const int _acc, const RotatorAxis _axis) = 0;
};
}  // namespace lidar
}  // namespace robosense
#endif  //ROTATOR_CONTROLLER_INTERFACE_H