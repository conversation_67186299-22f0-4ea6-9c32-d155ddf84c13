﻿/******************************************************************************
 * Copyright 2022 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#ifndef ROTATOR_CONTROLLER_SHARE_DATA_H
#define ROTATOR_CONTROLLER_SHARE_DATA_H
#include "rotator_controller_interface.h"
#include "share_data_interface.h"
#include <map>
#include <memory>
#include <string>
namespace robosense
{
namespace lidar
{
class RotatorControllerRequest
{
public:
  enum RequestType
  {
    CONNECT = 0,
    DISCONNECT,
    ADD_ROTATOR,
    ROTATE_ANGLE,
    ROTATE_ANGLE_RELATIVE,
    ROTATE_TWO_AXIS,
    GET_CURRENT_ANGLE,
    MOVE,
    GET_CURRENT_POSITION,
    RESET_ZERO_POSITION,
    STOP_ROTATOR,
    SET_ROTATOR_SPEED,
    SET_ROTATOR_ACC,
    IS_MOVING,
    SET_USER_ORIGINAL,
    GO_ORIGINAL,
    UNKNOW
  };
  explicit RotatorControllerRequest() = default;
  explicit RotatorControllerRequest(int _id, RequestType _request_type) : request_id_(_id), request_type_(_request_type)
  {}

public:
  void setID(const int& _id);
  int getID() const;
  void setRequestType(const RequestType& _type);
  RequestType getRequestType() const;
  void setAxis(const RotatorControllerInterface::RotatorAxis& _axis);
  RotatorControllerInterface::RotatorAxis getAxis() const;
  void setAxis1(const RotatorControllerInterface::RotatorAxis& _axis);
  RotatorControllerInterface::RotatorAxis getAxis1() const;
  void setAxis2(const RotatorControllerInterface::RotatorAxis& _axis);
  RotatorControllerInterface::RotatorAxis getAxis2() const;
  void setRotatorType(const std::string& _rotator_type);
  std::string getRotatorType() const;
  void setUseZeroLimit(const bool& _use_zero_limit);
  bool getUseZeroLimit() const;
  void setAngle(const float& _angle);
  float getAngle() const;
  void setAngle1(const float& _angle);
  float getAngle1() const;
  void setAngle2(const float& _angle);
  float getAngle2() const;
  void setPosition(const float& _position);
  float getPosition() const;
  void setSpeed(const int& _speed);
  int getSpeed() const;
  void setAcc(const int& _acc);
  int getAcc() const;

private:
  int request_id_                                = 0;
  RequestType request_type_                      = RequestType::UNKNOW;
  RotatorControllerInterface::RotatorAxis axis_  = RotatorControllerInterface::RotatorAxis::X;
  RotatorControllerInterface::RotatorAxis axis1_ = RotatorControllerInterface::RotatorAxis::X;
  RotatorControllerInterface::RotatorAxis axis2_ = RotatorControllerInterface::RotatorAxis::X;
  std::string rotator_type_                      = "MRS101";
  bool use_zero_limit_                           = true;
  float angle_                                   = 0;
  float angle1_                                  = 0;
  float angle2_                                  = 0;
  float position_                                = 0;
  int speed_                                     = 0.0;
  int acc_                                       = 0;
};

class RotatorControllerResult
{
public:
  explicit RotatorControllerResult() = default;
  explicit RotatorControllerResult(int _result_id) : result_id_(_result_id) {}
  explicit RotatorControllerResult(int _result_id, bool _is_success) : result_id_(_result_id), is_success_(_is_success)
  {}

public:
  void setID(const int& _id);
  int getID() const;
  void setIsSuccess(const bool& _is_success);
  bool getIsSuccess() const;
  void setAngle(const float& _angle);
  float getAngle() const;
  void setPosition(const float& _position);
  float getPosition() const;
  void setIsMoving(const bool& _is_moving);
  bool getIsMoving() const;

private:
  int result_id_   = 0;
  bool is_success_ = false;
  float angle_     = 0;
  float position_  = 0;
  bool is_moving_  = true;
};

class RotatorControllerShareData
{
public:
  RotatorControllerShareData() = default;
  static std::shared_ptr<RotatorControllerShareData> getInstance()
  {
    static std::shared_ptr<RotatorControllerShareData> instance = std::make_shared<RotatorControllerShareData>();
    return instance;
  }

public:
  bool connect(const int& _id);
  bool disconnect(const int& _id);
  bool addRotator(const int& _id,
                  const RotatorControllerInterface::RotatorAxis& _axis,
                  const std::string& _rotator_type,
                  const bool& _use_zero_limit);
  int rotateAngle(const int& _id,
                  const float& _angle,
                  const RotatorControllerInterface::RotatorAxis& _axis = RotatorControllerInterface::RotatorAxis::X);
  int rotateAngleRelative(
    const int& _id,
    const float& _angle,
    const RotatorControllerInterface::RotatorAxis& _axis = RotatorControllerInterface::RotatorAxis::X);
  int rotateTwoAxis(const int& _id,
                    const float& _angle1,
                    const RotatorControllerInterface::RotatorAxis& _axis1,
                    const float& _angle2,
                    const RotatorControllerInterface::RotatorAxis& _axis2);
  bool getCurrentAngle(
    const int& _id,
    float& _angle_in_degree,
    const RotatorControllerInterface::RotatorAxis& _axis = RotatorControllerInterface::RotatorAxis::X);
  int move(const int& _id,
           const float& _position,
           const RotatorControllerInterface::RotatorAxis& _axis = RotatorControllerInterface::RotatorAxis::X);
  bool getCurrentPosition(
    const int& _id,
    float& _position,
    const RotatorControllerInterface::RotatorAxis& _axis = RotatorControllerInterface::RotatorAxis::X);
  int resetZeroPosition(
    const int& _id,
    const RotatorControllerInterface::RotatorAxis& _axis = RotatorControllerInterface::RotatorAxis::X);
  bool stopRotator(const int& _id,
                   const RotatorControllerInterface::RotatorAxis& _axis = RotatorControllerInterface::RotatorAxis::X);
  bool setRotatorSpeed(
    const int& _id,
    const int& _speed,
    const RotatorControllerInterface::RotatorAxis& _axis = RotatorControllerInterface::RotatorAxis::X);
  bool isMoving(const int& _id,
                bool& _is_moving,
                const RotatorControllerInterface::RotatorAxis& _axis = RotatorControllerInterface::RotatorAxis::X);

  bool setUserOriginal(const int& _id, const RotatorControllerInterface::RotatorAxis _axis);

  bool goOrigion(const int& _id, const RotatorControllerInterface::RotatorAxis _axis);

  bool getIsSuccessByID(const int& _thread_id, const int& _result_id);
  bool getIsMovingByID(const int& _thread_id, const int& _result_id, bool& _is_moving);
  bool getCurrentAngleByID(const int& _thread_id, const int& _result_id, float& _angle);
  bool getCurrentPositionByID(const int& _thread_id, const int& _result_id, float& _position);
  bool setAcceleration(
    const int& _id,
    const int _acc,
    const RotatorControllerInterface::RotatorAxis& _axis = RotatorControllerInterface::RotatorAxis::X);

public:
  std::shared_ptr<
    ShareDataInterface<std::shared_ptr<RotatorControllerRequest>, std::shared_ptr<RotatorControllerResult>>>
  getRotatorShareDataByID(const int _id);

  void addRotatorShareData(const int _id);

private:
  std::map<int,
           std::shared_ptr<
             ShareDataInterface<std::shared_ptr<RotatorControllerRequest>, std::shared_ptr<RotatorControllerResult>>>>
    rotator_share_data_;
};
}  // namespace lidar
}  // namespace robosense
#endif  //ROTATOR_CONTROLLER_SHARE_DATA_H