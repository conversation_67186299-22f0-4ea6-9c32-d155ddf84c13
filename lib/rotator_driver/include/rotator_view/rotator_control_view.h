﻿/******************************************************************************
 * Copyright 2023 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#ifndef ROTATOR_CONTROL_VIEW_H
#define ROTATOR_CONTROL_VIEW_H
#include "qobjectdefs.h"
#include "rotator_controller_monitor.h"
#include <QtCore/QObject>
#include <QtWidgets/QDialog>
#include <memory>

class QPushButton;
class QLineEdit;
class QComboBox;

namespace robosense
{
namespace lidar
{
class RotatorControlView : public QDialog
{
  Q_OBJECT
public:
  explicit RotatorControlView(int _thread_id, QWidget* _parent = Q_NULLPTR);
  explicit RotatorControlView(RotatorControlView&&)      = delete;
  explicit RotatorControlView(const RotatorControlView&) = delete;
  RotatorControlView& operator=(RotatorControlView&&) = delete;
  RotatorControlView& operator=(const RotatorControlView&) = delete;
  ~RotatorControlView() override;

Q_SIGNALS:
  void signalStopMove();

private Q_SLOTS:
  void slotUpdateRotatorController(int _index);
  void slotConnectController();
  void slotResetRotator();
  void slotSetRotatorSpeed();
  void slotRotateRotator();
  void slotStopMoveEnableWidget();

private:
  void initLayout();
  void connectAllWidget();
  void updateAllWidget();
  void startMoveDisableWidget();
  void readSetting();
  void writeSetting();
  void moveWidgetEnable(bool _status);

private:
  QComboBox* combobox_rotator_controller_type_;
  QComboBox* combobox_rotator_type_;
  QLineEdit* lineedit_com_name_;
  QPushButton* pushbutton_connect_;
  QPushButton* pushbutton_reset_;
  QPushButton* pushbutton_set_speed_;
  QLineEdit* lineedit_speed_;
  QPushButton* pushbutton_move_;
  QLineEdit* lineedit_aim_point_;

private:
  int thread_id_;
  std::unique_ptr<RotatorControllerMonitor> monitor_;
  RotatorControllerFactory::RotatorControllerType controller_type_;
  QString setting_file_name_;
};
}  // namespace lidar
}  // namespace robosense
#endif