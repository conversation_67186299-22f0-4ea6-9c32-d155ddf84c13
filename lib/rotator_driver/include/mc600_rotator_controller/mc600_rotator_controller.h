﻿/******************************************************************************
 * Copyright 2022 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#ifndef MC600_ROTATOR_CONTROLLER_H
#define MC600_ROTATOR_CONTROLLER_H
#include "qserialport.h"
#include "rotator_controller_interface.h"
#include <QObject>
#include <condition_variable>
#include <cstdint>
#include <mutex>
#include <string>
#include <thread>
#include <unordered_map>
#include <vector>
namespace robosense
{
namespace lidar
{
class MC600RotatorController : public QObject, public RotatorControllerInterface
{
  Q_OBJECT
public:
  explicit MC600RotatorController(const std::string& _port_name, uint32_t _support_rotator_number);
  MC600RotatorController& operator=(MC600RotatorController&&) = delete;
  MC600RotatorController& operator=(MC600RotatorController&) = delete;
  MC600RotatorController(MC600RotatorController&&)           = delete;
  MC600RotatorController(MC600RotatorController&)            = delete;
  ~MC600RotatorController() override;

public:
  bool connect() override;

  bool disconnect() override;

  bool addRotator(const RotatorAxis _axis, const std::string& _rotator_type, bool _use_zero_limit) override;

  // 阻塞式
  bool rotateAngle(const float _angle_in_degree, const RotatorAxis _axis) override;

  // 阻塞式
  bool rotateAngleRelative(const float _angle_in_degree, const RotatorAxis _axis) override;

  //! @brief 双轴绝对旋转，非阻塞
  //!
  //! @return bool
  bool rotateTwoAxis(const float _angle_in_degree1,
                     const RotatorAxis _axis1,
                     const float _angle_in_degree2,
                     const RotatorAxis _axis2) override;

  bool getCurrentAngle(float& _angle_in_degree, const RotatorAxis _axis) override;

  bool move(const float _position_in_millimeter, const RotatorAxis _axis) override;

  bool getCurrentPosition(float& _position_in_millimeter, const RotatorAxis _axis) override;

  //! @brief 光电回零，阻塞式
  //!
  //! @return true or false
  bool resetZeroPosition(const RotatorAxis _axis) override;

  bool stopRotator(const RotatorAxis _axis) override;

  bool setRotatorSpeed(const int _speed, const RotatorAxis _axis) override;

  bool isMoving(bool& _is_moving, const RotatorAxis _axis) override;

  bool hasAxisMoving();

  virtual bool setUserOriginal(const RotatorAxis _axis) override;

  //! @brief 用户回零，阻塞式
  //!
  //! @return true or false
  virtual bool goOrigion(const RotatorAxis _axis) override;

private:
  bool writeCommand(const std::string& _command);
  std::string readAllData(int _msecs = 1000);

  //! @brief 用于读取预备指令，即明确长度的数据
  //!
  //! @param _max_size        最大长度
  //! @param _msecs           超时时间，ms
  //!
  //! @return std::string
  std::string readData(qint64 _max_size, int _msecs = 1000);

  //! @brief 位置查询命令的回复解析
  //!
  //! @param _reply_msg        回复的数据
  //! @param _position         解析的位置
  //!
  //! @return bool
  bool positionReplyToAngle(const std::string& _reply_msg, float& _position);

  bool setSoftLimit(const float _min, const float _max, const RotatorAxis _axis) override;

  bool setAcceleration(const int _acc, const RotatorAxis _axis) override;

  bool isRotatorStatusNormal(const RotatorAxis _axis);

  bool initRotator(const RotatorAxis _axis);

  void checkIfAixsMoveFinished(std::string& _reply_msg);

  inline void preprocessString(std::string& _string);

private Q_SLOTS:
  //! @brief 该控制器没有查询运动是否完成的指令，但运动完成后会回复信息
  //!        设置该信号槽，当有运动指令时，便接收消息做解析，满足条件将运动变量置为false
  //!
  //! @return void
  void dealMoveCommand();

private:
  static constexpr int MIN_SPEED         = 200;
  static constexpr int MAX_SPEED         = 70000;
  const std::string PASS_REPLY_STR_OK    = "OK";
  const std::string PASS_REPLY_STR_READY = "READY";

private:
  uint32_t support_rotator_number_;
  QSerialPort* serial_port_;
  int speed_;
  float min_range_;
  float max_range_;
  std::vector<std::string> rotatorAxis2String_ { "X", "Y", "Z" };
  std::unordered_map<int, std::atomic_bool> is_moving_vec_;
  std::thread deal_command_thread_;
  std::mutex mutex_;
  bool thread_running_;
  std::condition_variable condition_var_;
};
}  // namespace lidar
}  // namespace robosense
#endif  //MC600_ROTATOR_CONTROLLER_H