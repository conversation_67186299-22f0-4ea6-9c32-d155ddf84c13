﻿/******************************************************************************
 * Copyright 2022 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#ifndef SHARE_DATA_INTERFACE_H
#define SHARE_DATA_INTERFACE_H
#include <map>
#include <mutex>
namespace robosense
{
namespace lidar
{
template <class Request, class Result>
class ShareDataInterface
{
public:
  ShareDataInterface();

public:
  int generateRequestID();
  void addRequest(const int& _request_id, Request _new_request);
  void addResult(const int& _result_id, Result _new_result);
  bool isRequestExist(int _request_id);
  bool isResultExist(int _result_id);
  Result getResultByID(int _result_id);
  bool isHaveNewRequest();
  Request getRequest();

private:
  int id_ = 0;
  std::mutex mutex_id_;
  std::map<int, Request> requests_;
  std::mutex mutex_requests_;
  std::map<int, Result> results_;
  std::mutex mutex_results_;
};

template <class Request, class Result>
ShareDataInterface<Request, Result>::ShareDataInterface() = default;

template <class Request, class Result>
int ShareDataInterface<Request, Result>::generateRequestID()
{
  std::lock_guard<std::mutex> lock(mutex_id_);
  return ++id_;
}

template <class Request, class Result>
void ShareDataInterface<Request, Result>::addRequest(const int& _request_id, Request _new_request)
{
  if (nullptr == _new_request)
  {
    return;
  }
  if (isRequestExist(_new_request->getID()))
  {
    return;
  }
  std::lock_guard<std::mutex> lock(mutex_requests_);
  requests_[_request_id] = _new_request;
}

template <class Request, class Result>
void ShareDataInterface<Request, Result>::addResult(const int& _result_id, Result _new_result)
{
  if (nullptr == _new_result)
  {
    return;
  }
  if (isResultExist(_new_result->getID()))
  {
    return;
  }
  std::lock_guard<std::mutex> lock(mutex_results_);
  results_[_result_id] = _new_result;
}

template <class Request, class Result>
bool ShareDataInterface<Request, Result>::isRequestExist(int _request_id)
{
  std::lock_guard<std::mutex> lock(mutex_requests_);
  return requests_.find(_request_id) != requests_.end();
}

template <class Request, class Result>
bool ShareDataInterface<Request, Result>::isResultExist(int _result_id)
{
  std::lock_guard<std::mutex> lock(mutex_results_);
  return results_.find(_result_id) != results_.end();
}

template <class Request, class Result>
Result ShareDataInterface<Request, Result>::getResultByID(int _result_id)
{
  Result result = nullptr;
  std::lock_guard<std::mutex> lock(mutex_results_);
  auto iter = results_.find(_result_id);
  if (iter != results_.end())
  {
    result = iter->second;
    results_.erase(iter);
  }
  return result;
}

template <class Request, class Result>
bool ShareDataInterface<Request, Result>::isHaveNewRequest()
{
  std::lock_guard<std::mutex> lock(mutex_requests_);
  return requests_.size() > 0;
}

template <class Request, class Result>
Request ShareDataInterface<Request, Result>::getRequest()
{
  Request request = nullptr;
  std::lock_guard<std::mutex> lock(mutex_requests_);
  if (!requests_.empty())
  {
    auto iter = requests_.begin();
    request   = iter->second;
    requests_.erase(iter);
  }
  return request;
}
}  // namespace lidar
}  // namespace robosense
#endif  //SHARE_DATA_INTERFACE_H