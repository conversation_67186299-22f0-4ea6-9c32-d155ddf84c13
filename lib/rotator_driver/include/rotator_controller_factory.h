﻿/******************************************************************************
 * Copyright 2022 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#ifndef ROTATOR_CONTROLLER_FACTORY_H
#define ROTATOR_CONTROLLER_FACTORY_H
#include "rotator_controller_interface.h"
#include <array>
#include <string>
namespace robosense
{
namespace lidar
{
class RotatorControllerFactory
{
public:
  enum RotatorControllerType
  {
    ASD = 0,
    CL_01A,
    SC101,
    SC102,
    SC103,
    // SC104, //暂时不支持3轴以上
    // SC105,
    // SC106,
    SC111,
    SC112,
    SC113,
    SC121,
    SC122,
    SC123,
    MC600,
    UNKNOW
  };
  static constexpr std::array<const char*, RotatorControllerType::UNKNOW> ROTATOR_CONTROLLER_TYPE_NAME = {
    "ASD", "CL_01A", "SC101", "SC102", "SC103", "SC111", "SC112", "SC113", "SC121", "SC122", "SC123", "MC600"
  };
  static RotatorControllerInterface* createRotatorController(RotatorControllerType _type,
                                                             std::initializer_list<std::string> _hardware_param);
  static RotatorControllerType stringTypeToEnum(const std::string& _string_type);
};
}  // namespace lidar
}  // namespace robosense
#endif  //ROTATOR_CONTROLLER_FACTORY_H