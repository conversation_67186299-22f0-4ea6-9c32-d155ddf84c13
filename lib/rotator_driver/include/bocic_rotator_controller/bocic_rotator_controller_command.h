﻿/******************************************************************************
 * Copyright 2022 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#ifndef BOCIC_ROTATOR_CONTROLLER_COMMAND_H
#define BOCIC_ROTATOR_CONTROLLER_COMMAND_H
#include "bocic_rotator_controller/bocic_rotator.h"
#include "bocic_rotator_controller/bocic_rotator_controller.h"
#include <array>
#include <memory>
#include <string>
namespace robosense
{
namespace lidar
{
constexpr std::array<char, 3> PASS_REPLY  = { "OK" };
constexpr std::array<char, 3> FAIL_REPLY1 = { "NC" };
constexpr std::array<char, 4> FAIL_REPLY2 = { "ERR" };
class BocicRotatorControllerCommand
{
public:
  explicit BocicRotatorControllerCommand(std::shared_ptr<BocicRotator> _rotator,
                                         const BocicRotatorController::RotatorAxis& _axis);

  static std::string getIsMovingCommand();
  static std::string getMovingTwoAxisCommand(const int _angle_in_pluse1,
                                             const BocicRotatorController::RotatorAxis _axis1,
                                             const int _angle_in_pluse2,
                                             const BocicRotatorController::RotatorAxis _axis2);

public:
  std::string setRotatorTypeCommand();
  std::string setSubdivisionNumberCommand();
  std::string setTransmissionRatioCommand();
  std::string setSpeedCommand(const int _speed);
  std::string setHomingSpeedCommand(const int _speed);
  std::string setRangeCommand();
  std::string rotateAbsAngleCommand(const float _angle);
  std::string goStraightDistanceCommand(const float _position_in_millimeter);
  std::string stopCommand();
  std::string goHomeCommand();
  std::string getCurrentPositionCommand();
  float positionReplyToAngle(const std::string& _position_reply);
  float positionReplyToPosition(const std::string& _position_reply);
  bool movingStatusReplyToBool(const std::string& _status_reply);

  std::shared_ptr<BocicRotator> getRotator();

private:
  static std::string axis2String(BocicRotatorController::RotatorAxis _axis);
  std::shared_ptr<BocicRotator> rotator_;
  BocicRotatorController::RotatorAxis axis_;
};
}  // namespace lidar
}  // namespace robosense
#endif  //BOCIC_ROTATOR_CONTROLLER_COMMAND_H