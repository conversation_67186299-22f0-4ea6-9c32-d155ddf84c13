﻿/******************************************************************************
 * Copyright 2022 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#ifndef BOCIC_ROTATOR_H
#define BOCIC_ROTATOR_H
#include <array>
#include <string>
namespace robosense
{
namespace lidar
{
class BocicRotator
{
public:
  enum BocicRotatorModel
  {
    MRS101 = 0,
    MRS102,
    MRS103,
    MRS104,
    MRS105,
    MRS402,
    MGC102,
    MGC103,
    MGC104,
    MTS101,
    MTS102,
    MTS103,
    MTS104,
    MTS105,
    MTS106,
    UNKNOW
  };
  static constexpr std::array<const char*, BocicRotatorModel::UNKNOW> BOCIC_ROTATOR_MODEL_NAME = {
    "MRS101", "MRS102", "MRS103", "MRS104", "MRS105", "MRS402", "MGC102", "MGC103",
    "MGC104", "MTS101", "MTS102", "MTS103", "MTS104", "MTS105", "MTS106"
  };
  explicit BocicRotator(const BocicRotatorModel _rotator_type, const bool _use_zero_limit);
  static BocicRotatorModel stringModelToRotator(const std::string& _string_model);

public:
  enum BocicRotatorType
  {
    STRAIGHT_LINE_STAGE = 0,
    ROTATION_STAGE
  };

public:
  BocicRotatorType getType() const;
  int getTransmissionRatio() const;
  int getSubdivisionNumber() const;
  bool getHasZeroLimit() const;
  bool getUseZeroLimit() const;
  float getMinRange() const;
  float getMaxRange() const;
  float getMaxSpeed() const;
  void setMinRange(const float _min_range);
  void setMaxRange(const float _max_range);

private:
  BocicRotatorType type_;
  int transmission_ratio_;
  int subdivision_number_;
  bool has_zero_limit_;  // if has zero limit, set negative limit as zero
  bool use_zero_limit_;  // if has_zero_limit_ and use_zero_limit_, go to zero limit when getResetZeroCommand,
                         // otherwise go to abs 0 degree
  float min_range_;      // in degree if RotationStage, in mm if StraightLineStage
  float max_range_;      // in degree if RotationStage, in mm if StraightLineStage
  float max_speed_;
};
}  // namespace lidar
}  // namespace robosense
#endif  //BOCIC_ROTATOR_H
