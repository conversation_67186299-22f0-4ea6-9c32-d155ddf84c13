﻿/******************************************************************************
 * Copyright 2022 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#ifndef BOCIC_ROTATOR_CONTROLLER_H
#define BOCIC_ROTATOR_CONTROLLER_H
#include "qserialport.h"
#include "rotator_controller_interface.h"
#include <atomic>
#include <cstdint>
#include <map>
#include <memory>
#include <string>
namespace robosense
{
namespace lidar
{
class BocicRotatorControllerCommand;
class BocicRotatorController : public RotatorControllerInterface
{
public:
  BocicRotatorController(std::string& _port_name, uint32_t _support_rotator_number);
  BocicRotatorController& operator=(BocicRotatorController&&) = delete;
  BocicRotatorController& operator=(BocicRotatorController&) = delete;
  BocicRotatorController(BocicRotatorController&&)           = delete;
  BocicRotatorController(BocicRotatorController&)            = delete;
  ~BocicRotatorController() override;

public:
  bool connect() override;
  bool disconnect() override;
  bool addRotator(const RotatorAxis _axis, const std::string& _rotator_type, bool _use_zero_limit) override;
  bool rotateAngle(const float _angle_in_degree, const RotatorAxis _axis) override;
  bool rotateAngleRelative(const float _angle_in_degree, const RotatorAxis _axis) override { return false; }
  bool rotateTwoAxis(const float _angle_in_degree1,
                     const RotatorAxis _axis1,
                     const float _angle_in_degree2,
                     const RotatorAxis _axis2) override;
  bool getCurrentAngle(float& _angle_in_degree, const RotatorAxis _axis) override;
  bool move(const float _position_in_millimeter, const RotatorAxis _axis) override;
  bool getCurrentPosition(float& _position_in_millimeter, const RotatorAxis _axis) override;
  bool resetZeroPosition(const RotatorAxis _axis) override;
  bool stopRotator(const RotatorAxis _axis) override;
  bool setRotatorSpeed(const int _speed, const RotatorAxis _axis) override;
  bool isMoving(bool& _is_moving, const RotatorAxis _axis) override;
  bool setSoftLimit(const float _min_range, const float _max_range, const RotatorAxis _axis) override;
  bool setAcceleration(const int _acc, const RotatorAxis _axis) override;

private:
  bool writeCommand(const std::string& _command);
  std::string readData();
  bool initRotator(const RotatorAxis _axis);
  bool isRotatorStatusNormal(const RotatorAxis _axis);

private:
  uint32_t support_rotator_number_;
  std::map<RotatorAxis, std::shared_ptr<BocicRotatorControllerCommand>> rotators_;
  QSerialPort* serial_port_;
};
}  // namespace lidar
}  // namespace robosense
#endif  //BOCIC_ROTATOR_CONTROLLER_H