﻿/******************************************************************************
 * Copyright 2022 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#ifndef ROTATOR_CONTROLLER_MONITOR_H
#define ROTATOR_CONTROLLER_MONITOR_H
#include "qthread.h"
#include "rotator_controller_factory.h"
#include <atomic>
namespace robosense
{
namespace lidar
{
class RotatorControllerMonitor : public QThread
{
  Q_OBJECT
public:
  RotatorControllerMonitor(const int& _id,
                           const RotatorControllerFactory::RotatorControllerType& _type,
                           const std::string& _port_name);
  ~RotatorControllerMonitor() override;
  explicit RotatorControllerMonitor(RotatorControllerMonitor&&)      = delete;
  explicit RotatorControllerMonitor(const RotatorControllerMonitor&) = delete;
  RotatorControllerMonitor& operator=(RotatorControllerMonitor&&) = delete;
  RotatorControllerMonitor& operator=(const RotatorControllerMonitor&) = delete;

public:
  void stop();

private:
  void run() override;

private:
  int id_;
  RotatorControllerFactory::RotatorControllerType type_;
  std::string port_name_;
  std::atomic_bool is_running_;
};
}  // namespace lidar
}  // namespace robosense
#endif  //ROTATOR_CONTROLLER_MONITOR_H