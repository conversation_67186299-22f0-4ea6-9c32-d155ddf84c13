﻿/******************************************************************************
 * Copyright 2020 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
/**
 * @file   csv_parser.h
 * <AUTHOR> Zhang (<EMAIL>)
 * @brief
 * @version 1.2.1
 * @date 2024-09-25
 *
 * Copyright (c) 2021, RoboSense Co.,Ltd. - www.robosense.ai
 * All Rights Reserved.
 *
 * You can not use, copy or spread without official authorization.
 *
**/
#ifndef RSFSCLIB_CSV_PARSER_H
#define RSFSCLIB_CSV_PARSER_H

#include <cstddef>
#include <cstdint>
#include <cstdio>
#include <fstream>
#include <map>
#include <sstream>
#include <string>
#include <vector>

namespace robosense
{
namespace lidar
{
constexpr int REGISTER_ADDRESS_STEP = 4;  //连续寄存器地址的间隔为4
constexpr int CSV_PARSER_PRINT_INFO = 0;  //是否打印寄存器信息
constexpr int EXTRA_INFO_NUM        = 5;  //额外可输入的字符串信息,供用户自定义使用

/**
 * @brief 寄存器基础信息
 */
struct RegisterInfo
{
  bool is_ok = false;  //当前寄存器是否存在，每次搜索寄存器后，可先读取这个状态值，来判断当前寄存器是否可用
  std::string name;    //寄存器名字
  uint32_t address        = 0;  //寄存器地址
  int total_continual_num = 1;  //寄存器连续地址的数量
  int cur_continual_index = 0;  //当前寄存器在连续寄存器中的序号
  std::string index_property_str;  //自定义的寄存器索引属性, 根据这个属性可获取对应的一系列寄存器
  int32_t value_in_register       = 0;        //标定开始前,读取寄存器的原始值
  int32_t value_at_calibration    = 0;        //标定时设定的数值
  int32_t value_after_calibration = 0;        //标定完成后恢复的数值
  int32_t max_value_th            = 1000000;  //标定调参可写入的最大数值
  int32_t min_value_th            = 0;        //标定调参可写入的最小数值

  std::vector<std::string> extra_str_info;  //额外的输入信息(字符串格式)
};

/**
 * @brief 阈值基础信息
 */
struct LimitInfo
{
  friend class CsvParser;
  friend class LidarAbnormalMonitor;

  bool is_ok    = false;  //当前参数是否存在，每次搜索参数后，可先读取这个状态值，来判断当前寄存器是否可用
  double min_th = -9999.0;                  //阈值下限
  double max_th = 9999.0;                   //阈值上限
  std::string limit_text;                   // 阈值文本
  std::string min_th_text;                  //阈值下限文本
  std::string max_th_text;                  //阈值上限文本
  std::vector<std::string> extra_str_info;  //额外的输入信息(字符串格式)

  std::string getName() const { return name; }  //获取参数名称
  std::string getUnit() const { return unit; }  //获取参数单位
  void setNameSuffix(const std::string& _suffix);  //设置名称后缀，一般不可使用，通过CsvParser::setNameSuffix进行设置
  std::string getNameSuffix() const { return name_suffix; }  //获取名称后缀

  LimitInfo() = default;

  std::string name;         //参数名称
  std::string unit;         //参数单位
  std::string name_suffix;  //名称后缀
};

/**
 * @brief 寄存器信息读取，搜索功能类
 */
class CsvParser
{
public:
  enum RegValueType
  {
    REG_VALUE_TYPE_AT_CALIB = 0,
    REG_VALUE_TYPE_AFTER_CALIB,
    REG_VALUE_TYPE_MIN_VALUE,
    REG_VALUE_TYPE_MAX_VALUE
  };
  CsvParser();
  explicit CsvParser(CsvParser&&)      = delete;
  explicit CsvParser(const CsvParser&) = delete;
  CsvParser& operator=(CsvParser&&) = delete;
  CsvParser& operator=(const CsvParser&) = delete;
  ~CsvParser()                           = default;

  /**
   * @brief 载入寄存器CSV信息
   *
   * @param csv_path 寄存器参数文件路径
   * @return 是否载入并解析成功
   */
  bool loadRegisterCsvInfo(const std::string& _csv_path);

  /**
   * @brief 载入参数阈值CSV信息
   *
   * @param csv_path 参数阈值文件路径
   * @return 是否载入并解析成功
   */
  bool loadLimitCsvInfo(const std::string& _csv_path);

  /**
   * @brief 根据输入的寄存器名称搜索对应寄存器的信息
   *
   * @param register_name 寄存器名称
   * @return 寄存器对应的信息
   */
  RegisterInfo getRegisterInfo(const std::string& _register_name);

  /**
   * @brief 根据输入的寄存器索引属性，将指定属性的寄存器信息全部返回
   *
   * @param index_property_str 寄存器索引属性　用户自定义的属性(字符串)
   * @return 指定属性的全部寄存器信息
   */
  std::map<std::string, RegisterInfo> getSelectIndexPropertyRegisterInfo(const std::string& _index_property_str);

  /**
   * @brief 获取全部寄存器信息
   */
  std::multimap<std::string, RegisterInfo> inline getAllRegisterInfo() { return register_info_; }

  /*
   * @brief 根据输入的寄存器索引属性，将指定属性的指定寄存器地址和数值返回，可结合MEMSTCP使用
   * 
   * @param _index_property_str 寄存器索引属性，用户可自定义字符串类型的属性
   * @param _value_type         返回标定时的值还是返回标定后恢复的值
   * @return                    first是寄存器地址vector，second是寄存器值的vector
   */
  std::pair<std::vector<uint32_t>, std::vector<int32_t>> getSelectIndexPropertyRegisterInfo(
    const std::string& _index_property_str,
    const RegValueType _value_type);

  /**
   * @brief 对于模式为1的寄存器,检验value_in_register是否等于value_after_calibration
   *
   * @param read_register_info 标定开始前,读取所有寄存器原始数值,赋值给value_in_register,传入此函数
   * @return 模式为1的,若寄存器原始数值与标定后恢复数值不一致,则添加到队列,作为异常比对结果输出
   */
  static std::map<std::string, RegisterInfo> checkOriValueValid(
    std::map<std::string, RegisterInfo>& _read_register_info,
    const std::string& _index_property_str);

  /**
   * @brief 根据输入的参数名称搜索对应参数阈值的信息
   *
   * @param limit_name 参数名称
   * @return 参数对应的信息
   */
  LimitInfo getLimitInfo(const std::string& _limit_name);

  /**
   * @brief 设置LimitInfo的参数名称后缀
   * 
   * @param _limit_name 输入参数名称
   * @param _suffix 输入参数名称后缀
   * @return LimitInfo 
   */
  LimitInfo setNameSuffix(const std::string& _limit_name, std::string& _suffix);

  /**
   * @brief 获取全部阈值信息
   */
  std::map<std::string, LimitInfo> inline getAllLimitInfo() { return limit_info_; }

  bool checkWithinLimit(const std::string& _limit_name, const double _value);
  static bool checkLimitCsvHeader(std::ifstream& _fin, int& _line_num);
  static bool checkRegisterCsvHeader(std::ifstream& _fin, int& _line_num);
  static bool generateLimitCsvHeader(std::fstream& _file);
  static bool generateRegisterCsvHeader(std::fstream& _file);
  static bool backUpOperateRegInfo(const std::string& _csv_path,
                                   const std::map<std::string, RegisterInfo>& _reg_info_map);
  static bool getBackUpOperateRegInfo(const std::string& _csv_path,
                                      std::vector<uint32_t>& _reg_addr,
                                      std::vector<int32_t>& _reg_val);

private:
  static void exitAfterSomeTime(const size_t _second);

private:
  /**
   * @brief 删除字符串中空格，制表符tab等无效字符
   */
  static std::string trim(std::string& _str);

  std::multimap<std::string, RegisterInfo> register_info_;
  std::map<std::string, LimitInfo> limit_info_;
};

}  // namespace lidar
}  // namespace robosense

#endif
