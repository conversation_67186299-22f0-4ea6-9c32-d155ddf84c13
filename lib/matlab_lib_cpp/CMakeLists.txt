﻿cmake_minimum_required(VERSION 3.16)
project(matlab_lib_cpp LANGUAGES C CXX)

set(MATLAB_RUNTIME_PATH /usr/local/MATLAB/MATLAB_Runtime/v99)
set(CMAKE_INSTALL_RPATH ${CMAKE_INSTALL_RPATH} ${MATLAB_RUNTIME_PATH}/runtime/glnxa64)

find_library(
  LIBMCRRT
  NAMES mwmclmcrrt
  PATHS ${MATLAB_RUNTIME_PATH}/runtime/glnxa64
  NO_DEFAULT_PATH)
if(NOT LIBMCRRT)
  message(FATAL_ERROR "Library 'libmwmclmcrrt.so' not found in ${MATLAB_RUNTIME_PATH}/runtime/glnxa64")
endif()

# 查找动态库
find_library(
  SMOOTH_FIT_LIB
  NAMES smooth_fit
  PATHS ${CMAKE_CURRENT_SOURCE_DIR}
  NO_DEFAULT_PATH)
if(NOT SMOOTH_FIT_LIB)
  message(FATAL_ERROR "Library 'libsmooth_fit.so' not found in ${SMOOTH_FIT_PATH}")
endif()

# 添加一个iterface 到matlab_lib_cpp
add_library(${PROJECT_NAME} INTERFACE)
target_include_directories(${PROJECT_NAME} SYSTEM INTERFACE ${CMAKE_CURRENT_SOURCE_DIR}/include
                                                            ${MATLAB_RUNTIME_PATH}/extern/include)

# 添加一个iterface 到matlab_lib_cpp
target_link_libraries(${PROJECT_NAME} INTERFACE ${LIBMCRRT} ${SMOOTH_FIT_LIB})

install(FILES ${SMOOTH_FIT_LIB} DESTINATION ${CMAKE_INSTALL_PREFIX}/lib/${CMAKE_PROJECT_NAME})
