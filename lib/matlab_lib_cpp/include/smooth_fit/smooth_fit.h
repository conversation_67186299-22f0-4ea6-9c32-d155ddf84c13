﻿/******************************************************************************
 * Copyright 2024 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai
 *
 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONS<PERSON>QUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef SMOOTH_FIT_H
#define SMOOTH_FIT_H

#include "libsmooth_fit.h"
#include <vector>

// NOLINTNEXTLINE(modernize-concat-nested-namespaces)
namespace robosense
{
namespace lidar
{
// NOLINTNEXTLINE(bugprone-easily-swappable-parameters)
//extern LIB_libsmooth_fit_C_API bool MW_CALL_CONV mlfSmooth_fit(int nargout, mxArray** FitResult_dist_out, mxArray* area, mxArray* dist, mxArray* area_index);
inline std::vector<double> smoothFit(const std::vector<double>& _x_vec,
                                     const std::vector<double>& _y_vec,
                                     const std::vector<double>& _x_target_vec)

{
  std::vector<double> y_target_vec;

  try
  {
    // 1) 为 x_vec, y_vec, x_target_vec 分别创建 mxArray (列向量)
    mxArray* x_mx        = mxCreateDoubleMatrix(_x_vec.size(), 1, mxREAL);
    mxArray* y_mx        = mxCreateDoubleMatrix(_y_vec.size(), 1, mxREAL);
    mxArray* x_target_mx = mxCreateDoubleMatrix(_x_target_vec.size(), 1, mxREAL);

    // 2) 将 C++ 的 std::vector 拷贝到 mxArray 的实际存储区中
    std::copy(_x_vec.begin(), _x_vec.end(), mxGetPr(x_mx));
    std::copy(_y_vec.begin(), _y_vec.end(), mxGetPr(y_mx));
    std::copy(_x_target_vec.begin(), _x_target_vec.end(), mxGetPr(x_target_mx));

    // 3) **打印**拷贝结果，检查是否一致
    double* x_data_ptr        = mxGetPr(x_mx);
    double* y_data_ptr        = mxGetPr(y_mx);
    double* x_target_data_ptr = mxGetPr(x_target_mx);

    // 4) 调用 MATLAB 函数 smooth_fit(area, dist, area_index)
    //    注意参数顺序：area -> dist -> area_index
    mxArray* y_target_mxArray = nullptr;
    if (!mlfSmooth_fit(1, &y_target_mxArray,
                       x_mx,        // area
                       y_mx,        // dist
                       x_target_mx  // area_index
                       ))
    {
      std::cerr << "[ERROR] Error calling mlfSmooth_fit.\n";
      mxDestroyArray(x_mx);
      mxDestroyArray(y_mx);
      mxDestroyArray(x_target_mx);
      return y_target_vec;
    }

    // 5) 将输出 mxArray 转换为 C++ 的 std::vector<double>
    double* y_target_data = mxGetPr(y_target_mxArray);
    size_t y_target_size  = mxGetNumberOfElements(y_target_mxArray);
    y_target_vec.assign(y_target_data, y_target_data + y_target_size);

    // 6) 清理 mxArray 避免内存泄漏
    mxDestroyArray(x_mx);
    mxDestroyArray(y_mx);
    mxDestroyArray(x_target_mx);
    mxDestroyArray(y_target_mxArray);
  }
  catch (const std::exception& e)
  {
    std::cerr << "[EXCEPTION] " << e.what() << "\n";
  }

  return y_target_vec;
}
}  // namespace lidar
}  // namespace robosense
#endif  // SMOOTH_FIT_H