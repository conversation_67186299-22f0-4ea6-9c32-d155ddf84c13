/*
 * MATLAB Compiler: 8.1 (R2020b)
 * Date: Wed Jan  8 15:08:49 2025
 * Arguments:
 * "-B""macro_default""-W""lib:libsmooth_fit""-T""link:lib""-d""/media/robosense
 * /1TBHDD/program/test/libsmooth_fit/for_testing""-v""/media/robosense/1TBHDD/p
 * rogram/test/smooth_fit.m"
 */

#ifndef libsmooth_fit_h
#define libsmooth_fit_h 1

#if defined(__cplusplus) && !defined(mclmcrrt_h) && defined(__linux__)
#  pragma implementation "mclmcrrt.h"
#endif
#include "mclmcrrt.h"
#ifdef __cplusplus
extern "C" { // sbcheck:ok:extern_c
#endif

/* This symbol is defined in shared libraries. Define it here
 * (to nothing) in case this isn't a shared library. 
 */
#ifndef LIB_libsmooth_fit_C_API 
#define LIB_libsmooth_fit_C_API /* No special import/export declaration */
#endif

/* GENERAL LIBRARY FUNCTIONS -- START */

extern LIB_libsmooth_fit_C_API 
bool MW_CALL_CONV libsmooth_fitInitializeWithHandlers(
       mclOutputHandlerFcn error_handler, 
       mclOutputHandlerFcn print_handler);

extern LIB_libsmooth_fit_C_API 
bool MW_CALL_CONV libsmooth_fitInitialize(void);

extern LIB_libsmooth_fit_C_API 
void MW_CALL_CONV libsmooth_fitTerminate(void);

extern LIB_libsmooth_fit_C_API 
void MW_CALL_CONV libsmooth_fitPrintStackTrace(void);

/* GENERAL LIBRARY FUNCTIONS -- END */

/* C INTERFACE -- MLX WRAPPERS FOR USER-DEFINED MATLAB FUNCTIONS -- START */

extern LIB_libsmooth_fit_C_API 
bool MW_CALL_CONV mlxSmooth_fit(int nlhs, mxArray *plhs[], int nrhs, mxArray *prhs[]);

/* C INTERFACE -- MLX WRAPPERS FOR USER-DEFINED MATLAB FUNCTIONS -- END */

/* C INTERFACE -- MLF WRAPPERS FOR USER-DEFINED MATLAB FUNCTIONS -- START */

extern LIB_libsmooth_fit_C_API bool MW_CALL_CONV mlfSmooth_fit(int nargout, mxArray** FitResult_dist_out, mxArray* area, mxArray* dist, mxArray* area_index);

#ifdef __cplusplus
}
#endif
/* C INTERFACE -- MLF WRAPPERS FOR USER-DEFINED MATLAB FUNCTIONS -- END */

#endif
