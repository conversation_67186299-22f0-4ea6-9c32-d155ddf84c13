[submodule "lib/rsfsc_lib"]
	path = lib/rsfsc_lib
	url = ../../../../factory_tool/common_lib/rsfsc_lib.git
[submodule "lib/mech_communication"]
	path = lib/mech_communication
	url = ../../../../factory_tool/mech/mech_communication.git
[submodule "lib/rotator_driver"]
	path = lib/rotator_driver
	url = ../../../../common_driver/rotator_driver.git
[submodule "lib/relay_controller_driver"]
	path = lib/relay_controller_driver
	url = ***********************:system_codebase/common_driver/relay_controller_driver.git
[submodule "lib/mech_comm_lib"]
	path = lib/mech_comm_lib
	url = ../../../../factory_tool/mech/mech_comm_lib.git
