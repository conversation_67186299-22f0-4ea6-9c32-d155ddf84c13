﻿/******************************************************************************
 * Copyright 2024 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai
 *
 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONS<PERSON>QUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef CAMERA_WINDOW_H
#define CAMERA_WINDOW_H

#include <QCamera>
#include <QCameraImageCapture>
#include <QLabel>
#include <QPushButton>
#include <QTimer>
#include <QWidget>
#include <memory>

// NOLINTNEXTLINE(modernize-concat-nested-namespaces)
namespace robosense
{
namespace lidar
{

class CameraWindow : public QWidget
{
  Q_OBJECT
public:
  explicit CameraWindow(QWidget* _parent = nullptr);
  explicit CameraWindow(CameraWindow&&)      = delete;
  explicit CameraWindow(const CameraWindow&) = delete;
  CameraWindow& operator=(CameraWindow&&) = delete;
  CameraWindow& operator=(const CameraWindow&) = delete;
  ~CameraWindow() override;

private slots:
  void toggleCamera();
  void captureImage();
  void processImage(int _request_id, const QImage& _image);
  void handleCameraStateChange(QCamera::State _state);
  void handleCameraError(QCamera::Error _error);
  void adjustImageSize();

private:
  std::unique_ptr<QCamera> camera_device_;
  std::unique_ptr<QCameraImageCapture> image_capture_;
  QLabel* image_label_;
  QPushButton* camera_button_;
  QTimer* capture_timer_;
  bool is_camera_active_;
  QImage original_image_;
  void resizeEvent(QResizeEvent* _event) override;
};

}  // namespace lidar
}  // namespace robosense
#endif  // CAMERA_WINDOW_H