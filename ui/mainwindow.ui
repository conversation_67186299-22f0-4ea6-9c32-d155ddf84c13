<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1061</width>
    <height>866</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>MainWindow</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <property name="minimumSize">
    <size>
     <width>0</width>
     <height>840</height>
    </size>
   </property>
   <property name="styleSheet">
    <string notr="true">.QWidget{
background-color: rgb(239, 244, 248);
}
</string>
   </property>
   <layout class="QGridLayout" name="gridLayout_2">
    <property name="leftMargin">
     <number>0</number>
    </property>
    <property name="topMargin">
     <number>0</number>
    </property>
    <property name="rightMargin">
     <number>0</number>
    </property>
    <property name="bottomMargin">
     <number>0</number>
    </property>
    <item row="0" column="1">
     <widget class="QWidget" name="widget_main" native="true">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="minimumSize">
       <size>
        <width>950</width>
        <height>650</height>
       </size>
      </property>
      <property name="styleSheet">
       <string notr="true">.QWidget{
background-color: rgb(255, 255, 255);
border-top-left-radius: 20px
}
</string>
      </property>
      <layout class="QVBoxLayout" name="verticalLayout_3">
       <item>
        <widget class="QStackedWidget" name="widget_page_all">
         <property name="enabled">
          <bool>true</bool>
         </property>
         <property name="sizePolicy">
          <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="currentIndex">
          <number>5</number>
         </property>
         <widget class="QWidget" name="main_page">
          <layout class="QGridLayout" name="gridLayout_3">
           <item row="0" column="0">
            <layout class="QHBoxLayout" name="horizontalLayout_14">
             <item>
              <widget class="QGroupBox" name="groupBox_6">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="maximumSize">
                <size>
                 <width>230</width>
                 <height>500</height>
                </size>
               </property>
               <property name="title">
                <string>信息</string>
               </property>
               <layout class="QVBoxLayout" name="verticalLayout_9">
                <item>
                 <widget class="robosense::lidar::LabelTestState" name="widget" native="true">
                  <property name="minimumSize">
                   <size>
                    <width>0</width>
                    <height>70</height>
                   </size>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLabel" name="label_product_display">
                  <property name="minimumSize">
                   <size>
                    <width>0</width>
                    <height>30</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <pointsize>15</pointsize>
                   </font>
                  </property>
                  <property name="text">
                   <string>unknown</string>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignCenter</set>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLabel" name="label_mow_area_type">
                  <property name="text">
                   <string/>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignCenter</set>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="WidgetLidarInfoList" name="widget_lidar_info_list" native="true">
                  <property name="sizePolicy">
                   <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                    <horstretch>0</horstretch>
                    <verstretch>0</verstretch>
                   </sizepolicy>
                  </property>
                  <property name="minimumSize">
                   <size>
                    <width>0</width>
                    <height>0</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>16777215</width>
                    <height>16777215</height>
                   </size>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QProgressBar" name="progressbar_collect">
                  <property name="value">
                   <number>0</number>
                  </property>
                  <property name="format">
                   <string>采集%p%</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QProgressBar" name="progressbar_main_process_data">
                  <property name="value">
                   <number>0</number>
                  </property>
                  <property name="format">
                   <string>处理%p%</string>
                  </property>
                 </widget>
                </item>
               </layout>
              </widget>
             </item>
             <item>
              <layout class="QVBoxLayout" name="verticalLayout_8">
               <property name="bottomMargin">
                <number>6</number>
               </property>
               <item>
                <widget class="QTabWidget" name="main_page_tab">
                 <property name="currentIndex">
                  <number>1</number>
                 </property>
                 <widget class="QWidget" name="camera">
                  <attribute name="title">
                   <string>相机</string>
                  </attribute>
                  <layout class="QVBoxLayout" name="verticalLayout_13">
                   <property name="spacing">
                    <number>3</number>
                   </property>
                   <property name="leftMargin">
                    <number>1</number>
                   </property>
                   <property name="topMargin">
                    <number>1</number>
                   </property>
                   <property name="rightMargin">
                    <number>1</number>
                   </property>
                   <property name="bottomMargin">
                    <number>1</number>
                   </property>
                  </layout>
                 </widget>
                 <widget class="QWidget" name="tab_2">
                  <attribute name="title">
                   <string>Tab 2</string>
                  </attribute>
                  <layout class="QVBoxLayout" name="verticalLayout_10" stretch="3,2,2,1">
                   <item>
                    <layout class="QHBoxLayout" name="horizontalLayout_13">
                     <item>
                      <widget class="QGroupBox" name="groupbox_lidar_op">
                       <property name="enabled">
                        <bool>true</bool>
                       </property>
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>0</height>
                        </size>
                       </property>
                       <property name="title">
                        <string>雷达操作</string>
                       </property>
                       <property name="checkable">
                        <bool>false</bool>
                       </property>
                       <layout class="QVBoxLayout" name="verticalLayout_4">
                        <item>
                         <layout class="QHBoxLayout" name="horizontalLayout_2">
                          <item>
                           <widget class="QLabel" name="label_7">
                            <property name="enabled">
                             <bool>true</bool>
                            </property>
                            <property name="text">
                             <string>停止位置:</string>
                            </property>
                           </widget>
                          </item>
                          <item>
                           <widget class="QDoubleSpinBox" name="spinbox_stop_motor_to_angle">
                            <property name="enabled">
                             <bool>true</bool>
                            </property>
                            <property name="suffix">
                             <string>°</string>
                            </property>
                            <property name="decimals">
                             <number>1</number>
                            </property>
                            <property name="minimum">
                             <double>-360.000000000000000</double>
                            </property>
                            <property name="maximum">
                             <double>360.000000000000000</double>
                            </property>
                           </widget>
                          </item>
                          <item>
                           <widget class="QPushButton" name="button_stop_motor_to_angle">
                            <property name="enabled">
                             <bool>true</bool>
                            </property>
                            <property name="text">
                             <string>停止电机</string>
                            </property>
                            <property name="checkable">
                             <bool>false</bool>
                            </property>
                           </widget>
                          </item>
                          <item>
                           <widget class="QPushButton" name="button_start_motor">
                            <property name="enabled">
                             <bool>true</bool>
                            </property>
                            <property name="text">
                             <string>启动电机</string>
                            </property>
                           </widget>
                          </item>
                         </layout>
                        </item>
                        <item>
                         <layout class="QHBoxLayout" name="horizontalLayout_3">
                          <item>
                           <widget class="QLineEdit" name="lineedit_top_flash_start_addr">
                            <property name="enabled">
                             <bool>true</bool>
                            </property>
                            <property name="sizePolicy">
                             <sizepolicy hsizetype="Minimum" vsizetype="Minimum">
                              <horstretch>0</horstretch>
                              <verstretch>0</verstretch>
                             </sizepolicy>
                            </property>
                            <property name="maximumSize">
                             <size>
                              <width>80</width>
                              <height>16777215</height>
                             </size>
                            </property>
                            <property name="text">
                             <string>0xF00000</string>
                            </property>
                            <property name="alignment">
                             <set>Qt::AlignCenter</set>
                            </property>
                           </widget>
                          </item>
                          <item>
                           <widget class="QLabel" name="label_14">
                            <property name="text">
                             <string>-</string>
                            </property>
                           </widget>
                          </item>
                          <item>
                           <widget class="QLineEdit" name="lineedit_top_flash_end_addr">
                            <property name="enabled">
                             <bool>true</bool>
                            </property>
                            <property name="sizePolicy">
                             <sizepolicy hsizetype="Minimum" vsizetype="Minimum">
                              <horstretch>0</horstretch>
                              <verstretch>0</verstretch>
                             </sizepolicy>
                            </property>
                            <property name="maximumSize">
                             <size>
                              <width>80</width>
                              <height>16777215</height>
                             </size>
                            </property>
                            <property name="text">
                             <string>0xFFFFFF</string>
                            </property>
                            <property name="alignment">
                             <set>Qt::AlignCenter</set>
                            </property>
                           </widget>
                          </item>
                          <item>
                           <widget class="QPushButton" name="button_write_top_flash">
                            <property name="enabled">
                             <bool>true</bool>
                            </property>
                            <property name="sizePolicy">
                             <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                              <horstretch>0</horstretch>
                              <verstretch>0</verstretch>
                             </sizepolicy>
                            </property>
                            <property name="minimumSize">
                             <size>
                              <width>141</width>
                              <height>0</height>
                             </size>
                            </property>
                            <property name="maximumSize">
                             <size>
                              <width>141</width>
                              <height>16777215</height>
                             </size>
                            </property>
                            <property name="text">
                             <string>选择写入标定文件</string>
                            </property>
                           </widget>
                          </item>
                          <item>
                           <spacer name="horizontalSpacer_9">
                            <property name="orientation">
                             <enum>Qt::Horizontal</enum>
                            </property>
                            <property name="sizeHint" stdset="0">
                             <size>
                              <width>40</width>
                              <height>20</height>
                             </size>
                            </property>
                           </spacer>
                          </item>
                         </layout>
                        </item>
                        <item>
                         <layout class="QHBoxLayout" name="horizontalLayout_4">
                          <item>
                           <widget class="QLineEdit" name="lineedit_csv_init_name">
                            <property name="enabled">
                             <bool>true</bool>
                            </property>
                            <property name="sizePolicy">
                             <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                              <horstretch>0</horstretch>
                              <verstretch>0</verstretch>
                             </sizepolicy>
                            </property>
                            <property name="maximumSize">
                             <size>
                              <width>60</width>
                              <height>16777215</height>
                             </size>
                            </property>
                            <property name="text">
                             <string>init</string>
                            </property>
                            <property name="alignment">
                             <set>Qt::AlignCenter</set>
                            </property>
                           </widget>
                          </item>
                          <item>
                           <widget class="QPushButton" name="button_write_csv_init">
                            <property name="enabled">
                             <bool>true</bool>
                            </property>
                            <property name="sizePolicy">
                             <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                              <horstretch>0</horstretch>
                              <verstretch>0</verstretch>
                             </sizepolicy>
                            </property>
                            <property name="maximumSize">
                             <size>
                              <width>110</width>
                              <height>16777215</height>
                             </size>
                            </property>
                            <property name="text">
                             <string>从csv写入雷达</string>
                            </property>
                           </widget>
                          </item>
                          <item>
                           <widget class="QPushButton" name="button_rw_reg_from_file">
                            <property name="enabled">
                             <bool>true</bool>
                            </property>
                            <property name="sizePolicy">
                             <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                              <horstretch>0</horstretch>
                              <verstretch>0</verstretch>
                             </sizepolicy>
                            </property>
                            <property name="maximumSize">
                             <size>
                              <width>110</width>
                              <height>16777215</height>
                             </size>
                            </property>
                            <property name="text">
                             <string>从文件读写Reg</string>
                            </property>
                           </widget>
                          </item>
                          <item>
                           <spacer name="horizontalSpacer_6">
                            <property name="orientation">
                             <enum>Qt::Horizontal</enum>
                            </property>
                            <property name="sizeHint" stdset="0">
                             <size>
                              <width>40</width>
                              <height>20</height>
                             </size>
                            </property>
                           </spacer>
                          </item>
                         </layout>
                        </item>
                        <item>
                         <layout class="QHBoxLayout" name="horizontalLayout_11">
                          <item>
                           <widget class="QLabel" name="label_relay_state">
                            <property name="minimumSize">
                             <size>
                              <width>20</width>
                              <height>20</height>
                             </size>
                            </property>
                            <property name="styleSheet">
                             <string notr="true">background-color: rgb(211, 215, 207);</string>
                            </property>
                            <property name="text">
                             <string/>
                            </property>
                           </widget>
                          </item>
                          <item>
                           <widget class="QPushButton" name="button_main_open_relay">
                            <property name="text">
                             <string>上电</string>
                            </property>
                           </widget>
                          </item>
                          <item>
                           <widget class="QPushButton" name="button_main_close_relay">
                            <property name="text">
                             <string>下电</string>
                            </property>
                           </widget>
                          </item>
                          <item>
                           <widget class="QPushButton" name="button_close_eye_safe">
                            <property name="text">
                             <string>关闭人眼安全</string>
                            </property>
                           </widget>
                          </item>
                          <item>
                           <spacer name="horizontalSpacer_10">
                            <property name="orientation">
                             <enum>Qt::Horizontal</enum>
                            </property>
                            <property name="sizeHint" stdset="0">
                             <size>
                              <width>40</width>
                              <height>20</height>
                             </size>
                            </property>
                           </spacer>
                          </item>
                         </layout>
                        </item>
                       </layout>
                      </widget>
                     </item>
                     <item>
                      <widget class="QGroupBox" name="groupbox_test">
                       <property name="title">
                        <string>功能区</string>
                       </property>
                       <layout class="QVBoxLayout" name="verticalLayout_7">
                        <item>
                         <layout class="QGridLayout" name="gridLayout_8">
                          <item row="0" column="1">
                           <widget class="QPushButton" name="button_test">
                            <property name="enabled">
                             <bool>false</bool>
                            </property>
                            <property name="text">
                             <string>测试用</string>
                            </property>
                           </widget>
                          </item>
                          <item row="1" column="2">
                           <widget class="QPushButton" name="button_auto_set_sn">
                            <property name="text">
                             <string>自动获取SN</string>
                            </property>
                           </widget>
                          </item>
                          <item row="0" column="2">
                           <widget class="QPushButton" name="button_update_para_info">
                            <property name="text">
                             <string>更新参数</string>
                            </property>
                           </widget>
                          </item>
                         </layout>
                        </item>
                       </layout>
                      </widget>
                     </item>
                    </layout>
                   </item>
                   <item>
                    <widget class="QGroupBox" name="groupbox_rotator_op">
                     <property name="title">
                      <string>转台操作</string>
                     </property>
                     <layout class="QHBoxLayout" name="horizontalLayout_8">
                      <item>
                       <widget class="QDoubleSpinBox" name="spinbox_rotate_setup_lidar">
                        <property name="prefix">
                         <string/>
                        </property>
                        <property name="suffix">
                         <string>°</string>
                        </property>
                        <property name="minimum">
                         <double>-360.000000000000000</double>
                        </property>
                        <property name="maximum">
                         <double>360.000000000000000</double>
                        </property>
                       </widget>
                      </item>
                      <item>
                       <widget class="QPushButton" name="button_rotate_setup_lidar">
                        <property name="maximumSize">
                         <size>
                          <width>16777215</width>
                          <height>25</height>
                         </size>
                        </property>
                        <property name="text">
                         <string>-&gt;上料</string>
                        </property>
                       </widget>
                      </item>
                      <item>
                       <widget class="QDoubleSpinBox" name="spinbox_rotate_light_spot">
                        <property name="prefix">
                         <string/>
                        </property>
                        <property name="suffix">
                         <string>°</string>
                        </property>
                        <property name="minimum">
                         <double>-360.000000000000000</double>
                        </property>
                        <property name="maximum">
                         <double>360.000000000000000</double>
                        </property>
                       </widget>
                      </item>
                      <item>
                       <widget class="QPushButton" name="button_rotate_light_spot">
                        <property name="maximumSize">
                         <size>
                          <width>16777215</width>
                          <height>25</height>
                         </size>
                        </property>
                        <property name="text">
                         <string>-&gt;查光斑</string>
                        </property>
                       </widget>
                      </item>
                      <item>
                       <widget class="QSpinBox" name="spinbox_rotate_default_speed">
                        <property name="suffix">
                         <string/>
                        </property>
                        <property name="prefix">
                         <string>速度: </string>
                        </property>
                        <property name="minimum">
                         <number>10</number>
                        </property>
                        <property name="maximum">
                         <number>20000</number>
                        </property>
                        <property name="value">
                         <number>6000</number>
                        </property>
                       </widget>
                      </item>
                      <item>
                       <spacer name="horizontalSpacer_15">
                        <property name="orientation">
                         <enum>Qt::Horizontal</enum>
                        </property>
                        <property name="sizeHint" stdset="0">
                         <size>
                          <width>40</width>
                          <height>20</height>
                         </size>
                        </property>
                       </spacer>
                      </item>
                     </layout>
                    </widget>
                   </item>
                   <item>
                    <widget class="QGroupBox" name="groupbox_collect_op">
                     <property name="title">
                      <string>采集</string>
                     </property>
                     <property name="checkable">
                      <bool>false</bool>
                     </property>
                     <layout class="QVBoxLayout" name="verticalLayout_11">
                      <item>
                       <layout class="QHBoxLayout" name="horizontalLayout_5">
                        <item>
                         <widget class="QCheckBox" name="checkbox_zero_confirm">
                          <property name="text">
                           <string>零度角确认</string>
                          </property>
                         </widget>
                        </item>
                        <item>
                         <widget class="QCheckBox" name="checkbox_wave_angle_confirm">
                          <property name="text">
                           <string>回波确认</string>
                          </property>
                         </widget>
                        </item>
                        <item>
                         <widget class="QCheckBox" name="checkbox_eval_result">
                          <property name="text">
                           <string>评估结果</string>
                          </property>
                         </widget>
                        </item>
                        <item>
                         <widget class="QLabel" name="label_9">
                          <property name="text">
                           <string>开始角度:</string>
                          </property>
                         </widget>
                        </item>
                        <item>
                         <widget class="QDoubleSpinBox" name="spinbox_rotator_x_angle_start">
                          <property name="suffix">
                           <string>°</string>
                          </property>
                          <property name="maximum">
                           <double>350.000000000000000</double>
                          </property>
                         </widget>
                        </item>
                        <item>
                         <spacer name="horizontalSpacer_7">
                          <property name="orientation">
                           <enum>Qt::Horizontal</enum>
                          </property>
                          <property name="sizeHint" stdset="0">
                           <size>
                            <width>38</width>
                            <height>22</height>
                           </size>
                          </property>
                         </spacer>
                        </item>
                        <item>
                         <widget class="QLabel" name="label_10">
                          <property name="text">
                           <string>结束:</string>
                          </property>
                         </widget>
                        </item>
                        <item>
                         <widget class="QDoubleSpinBox" name="spinbox_rotator_x_angle_end">
                          <property name="suffix">
                           <string>°</string>
                          </property>
                          <property name="maximum">
                           <double>1080.000000000000000</double>
                          </property>
                          <property name="value">
                           <double>360.000000000000000</double>
                          </property>
                         </widget>
                        </item>
                        <item>
                         <spacer name="horizontalSpacer_8">
                          <property name="orientation">
                           <enum>Qt::Horizontal</enum>
                          </property>
                          <property name="sizeHint" stdset="0">
                           <size>
                            <width>58</width>
                            <height>22</height>
                           </size>
                          </property>
                         </spacer>
                        </item>
                        <item>
                         <widget class="QLabel" name="label_11">
                          <property name="text">
                           <string>速度:</string>
                          </property>
                         </widget>
                        </item>
                        <item>
                         <widget class="QSpinBox" name="spinbox_rotator_x_speed_start">
                          <property name="maximum">
                           <number>50000</number>
                          </property>
                          <property name="value">
                           <number>3000</number>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </item>
                      <item>
                       <layout class="QHBoxLayout" name="horizontalLayout_6">
                        <item>
                         <widget class="QPushButton" name="button_init_lidar">
                          <property name="maximumSize">
                           <size>
                            <width>100</width>
                            <height>16777215</height>
                           </size>
                          </property>
                          <property name="text">
                           <string>初始化雷达</string>
                          </property>
                         </widget>
                        </item>
                        <item>
                         <widget class="QPushButton" name="button_write_chn_angle">
                          <property name="text">
                           <string>写入通道角</string>
                          </property>
                         </widget>
                        </item>
                        <item>
                         <spacer name="horizontalSpacer_14">
                          <property name="orientation">
                           <enum>Qt::Horizontal</enum>
                          </property>
                          <property name="sizeHint" stdset="0">
                           <size>
                            <width>40</width>
                            <height>20</height>
                           </size>
                          </property>
                         </spacer>
                        </item>
                        <item>
                         <widget class="QCheckBox" name="checkbox_check_firmware_version">
                          <property name="text">
                           <string>MES版本卡控</string>
                          </property>
                         </widget>
                        </item>
                        <item>
                         <widget class="QCheckBox" name="checkbox_init_lidar">
                          <property name="text">
                           <string>采集前初始化雷达</string>
                          </property>
                         </widget>
                        </item>
                        <item>
                         <widget class="QCheckBox" name="checkbox_deinit_lidar">
                          <property name="text">
                           <string>采集后恢复雷达</string>
                          </property>
                          <property name="checked">
                           <bool>true</bool>
                          </property>
                         </widget>
                        </item>
                        <item>
                         <widget class="QPushButton" name="button_one_key_collect">
                          <property name="maximumSize">
                           <size>
                            <width>80</width>
                            <height>16777215</height>
                           </size>
                          </property>
                          <property name="text">
                           <string>一键采集</string>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </item>
                     </layout>
                    </widget>
                   </item>
                   <item>
                    <widget class="QGroupBox" name="groupbox_data_op">
                     <property name="title">
                      <string>数据</string>
                     </property>
                     <layout class="QVBoxLayout" name="verticalLayout_6">
                      <item>
                       <layout class="QHBoxLayout" name="horizontalLayout_7">
                        <item>
                         <widget class="QPushButton" name="button_data_fit">
                          <property name="maximumSize">
                           <size>
                            <width>80</width>
                            <height>16777215</height>
                           </size>
                          </property>
                          <property name="text">
                           <string>处理数据</string>
                          </property>
                         </widget>
                        </item>
                        <item>
                         <widget class="QPushButton" name="button_write_bit_to_lidar">
                          <property name="maximumSize">
                           <size>
                            <width>135</width>
                            <height>16777215</height>
                           </size>
                          </property>
                          <property name="text">
                           <string>标定数据写入雷达</string>
                          </property>
                         </widget>
                        </item>
                        <item>
                         <spacer name="horizontalSpacer_4">
                          <property name="orientation">
                           <enum>Qt::Horizontal</enum>
                          </property>
                          <property name="sizeHint" stdset="0">
                           <size>
                            <width>40</width>
                            <height>20</height>
                           </size>
                          </property>
                         </spacer>
                        </item>
                        <item>
                         <widget class="QPushButton" name="button_one_key_process">
                          <property name="text">
                           <string>一键处理</string>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </item>
                     </layout>
                    </widget>
                   </item>
                  </layout>
                 </widget>
                </widget>
               </item>
               <item>
                <layout class="QGridLayout" name="gridLayout_10">
                 <property name="leftMargin">
                  <number>6</number>
                 </property>
                 <property name="topMargin">
                  <number>6</number>
                 </property>
                 <property name="rightMargin">
                  <number>12</number>
                 </property>
                 <property name="bottomMargin">
                  <number>6</number>
                 </property>
                 <item row="0" column="1">
                  <widget class="QCheckBox" name="checkbox_onekey_wave">
                   <property name="text">
                    <string>回波</string>
                   </property>
                   <property name="checked">
                    <bool>true</bool>
                   </property>
                  </widget>
                 </item>
                 <item row="0" column="4">
                  <widget class="QPushButton" name="button_one_key_run">
                   <property name="text">
                    <string>一键运行</string>
                   </property>
                  </widget>
                 </item>
                 <item row="0" column="0">
                  <widget class="QCheckBox" name="checkbox_onekey_first_zero">
                   <property name="text">
                    <string>一次零度角</string>
                   </property>
                  </widget>
                 </item>
                 <item row="0" column="3">
                  <spacer name="horizontalSpacer_13">
                   <property name="orientation">
                    <enum>Qt::Horizontal</enum>
                   </property>
                   <property name="sizeHint" stdset="0">
                    <size>
                     <width>40</width>
                     <height>20</height>
                    </size>
                   </property>
                  </spacer>
                 </item>
                 <item row="0" column="2">
                  <widget class="QCheckBox" name="checkbox_onekey_sec_zero">
                   <property name="enabled">
                    <bool>true</bool>
                   </property>
                   <property name="text">
                    <string>二次零度角</string>
                   </property>
                  </widget>
                 </item>
                </layout>
               </item>
              </layout>
             </item>
            </layout>
           </item>
           <item row="1" column="0">
            <layout class="QHBoxLayout" name="horizontalLayout_10">
             <item>
              <widget class="QLabel" name="label_rotator_x_angle">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="minimumSize">
                <size>
                 <width>230</width>
                 <height>0</height>
                </size>
               </property>
               <property name="text">
                <string>转台:  未初始化</string>
               </property>
               <property name="alignment">
                <set>Qt::AlignCenter</set>
               </property>
              </widget>
             </item>
             <item>
              <spacer name="horizontalSpacer_3">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>40</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
            </layout>
           </item>
          </layout>
         </widget>
         <widget class="QWidget" name="dev_tools_page">
          <layout class="QGridLayout" name="gridLayout_4">
           <item row="0" column="0">
            <widget class="QTabWidget" name="tabWidget_2">
             <property name="currentIndex">
              <number>0</number>
             </property>
             <widget class="QWidget" name="tab_3">
              <attribute name="title">
               <string>Tab 1</string>
              </attribute>
              <widget class="WidgetExtDevice" name="widget_ext_device" native="true">
               <property name="geometry">
                <rect>
                 <x>0</x>
                 <y>10</y>
                 <width>914</width>
                 <height>451</height>
                </rect>
               </property>
              </widget>
             </widget>
             <widget class="QWidget" name="tab_4">
              <attribute name="title">
               <string>Tab 2</string>
              </attribute>
             </widget>
            </widget>
           </item>
          </layout>
         </widget>
         <widget class="QWidget" name="data_process_page">
          <layout class="QVBoxLayout" name="verticalLayout_5">
           <item>
            <widget class="QGroupBox" name="groupbox_zero_calib_op">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="title">
              <string>零度角标定</string>
             </property>
             <layout class="QGridLayout" name="gridLayout_7">
              <item row="1" column="8">
               <widget class="QPushButton" name="btn_zero_path">
                <property name="text">
                 <string>打开路径</string>
                </property>
               </widget>
              </item>
              <item row="0" column="7">
               <widget class="QLineEdit" name="lineedit_cur_zero">
                <property name="enabled">
                 <bool>false</bool>
                </property>
               </widget>
              </item>
              <item row="2" column="3">
               <widget class="QLabel" name="label_20">
                <property name="text">
                 <string>自检斜率：</string>
                </property>
               </widget>
              </item>
              <item row="1" column="5">
               <widget class="QLineEdit" name="lineedit_azi_end"/>
              </item>
              <item row="1" column="0">
               <widget class="QCheckBox" name="checkbox_rot">
                <property name="text">
                 <string>转台使能</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item row="1" column="6">
               <widget class="QLabel" name="label_16">
                <property name="text">
                 <string>文件名:</string>
                </property>
               </widget>
              </item>
              <item row="1" column="3">
               <widget class="QLabel" name="label_18">
                <property name="text">
                 <string>角度范围:</string>
                </property>
               </widget>
              </item>
              <item row="0" column="3">
               <widget class="QPushButton" name="btn_zero_set">
                <property name="text">
                 <string>write</string>
                </property>
               </widget>
              </item>
              <item row="1" column="7">
               <widget class="QLineEdit" name="lineedit_path_zero"/>
              </item>
              <item row="2" column="0">
               <widget class="QLabel" name="label_19">
                <property name="text">
                 <string>自检角度:</string>
                </property>
               </widget>
              </item>
              <item row="2" column="2">
               <widget class="QLineEdit" name="lineedit_test_azi_end"/>
              </item>
              <item row="2" column="1">
               <widget class="QLineEdit" name="lineedit_test_azi_start"/>
              </item>
              <item row="0" column="1">
               <widget class="QLabel" name="label_zero">
                <property name="palette">
                 <palette>
                  <active>
                   <colorrole role="Button">
                    <brush brushstyle="SolidPattern">
                     <color alpha="255">
                      <red>136</red>
                      <green>138</green>
                      <blue>133</blue>
                     </color>
                    </brush>
                   </colorrole>
                   <colorrole role="Light">
                    <brush brushstyle="SolidPattern">
                     <color alpha="255">
                      <red>204</red>
                      <green>207</green>
                      <blue>200</blue>
                     </color>
                    </brush>
                   </colorrole>
                   <colorrole role="Midlight">
                    <brush brushstyle="SolidPattern">
                     <color alpha="255">
                      <red>170</red>
                      <green>172</green>
                      <blue>166</blue>
                     </color>
                    </brush>
                   </colorrole>
                   <colorrole role="Dark">
                    <brush brushstyle="SolidPattern">
                     <color alpha="255">
                      <red>68</red>
                      <green>69</green>
                      <blue>66</blue>
                     </color>
                    </brush>
                   </colorrole>
                   <colorrole role="Mid">
                    <brush brushstyle="SolidPattern">
                     <color alpha="255">
                      <red>91</red>
                      <green>92</green>
                      <blue>89</blue>
                     </color>
                    </brush>
                   </colorrole>
                   <colorrole role="Window">
                    <brush brushstyle="SolidPattern">
                     <color alpha="255">
                      <red>136</red>
                      <green>138</green>
                      <blue>133</blue>
                     </color>
                    </brush>
                   </colorrole>
                   <colorrole role="Shadow">
                    <brush brushstyle="SolidPattern">
                     <color alpha="255">
                      <red>0</red>
                      <green>0</green>
                      <blue>0</blue>
                     </color>
                    </brush>
                   </colorrole>
                   <colorrole role="AlternateBase">
                    <brush brushstyle="SolidPattern">
                     <color alpha="255">
                      <red>195</red>
                      <green>196</green>
                      <blue>194</blue>
                     </color>
                    </brush>
                   </colorrole>
                  </active>
                  <inactive>
                   <colorrole role="Button">
                    <brush brushstyle="SolidPattern">
                     <color alpha="255">
                      <red>136</red>
                      <green>138</green>
                      <blue>133</blue>
                     </color>
                    </brush>
                   </colorrole>
                   <colorrole role="Light">
                    <brush brushstyle="SolidPattern">
                     <color alpha="255">
                      <red>204</red>
                      <green>207</green>
                      <blue>200</blue>
                     </color>
                    </brush>
                   </colorrole>
                   <colorrole role="Midlight">
                    <brush brushstyle="SolidPattern">
                     <color alpha="255">
                      <red>170</red>
                      <green>172</green>
                      <blue>166</blue>
                     </color>
                    </brush>
                   </colorrole>
                   <colorrole role="Dark">
                    <brush brushstyle="SolidPattern">
                     <color alpha="255">
                      <red>68</red>
                      <green>69</green>
                      <blue>66</blue>
                     </color>
                    </brush>
                   </colorrole>
                   <colorrole role="Mid">
                    <brush brushstyle="SolidPattern">
                     <color alpha="255">
                      <red>91</red>
                      <green>92</green>
                      <blue>89</blue>
                     </color>
                    </brush>
                   </colorrole>
                   <colorrole role="Window">
                    <brush brushstyle="SolidPattern">
                     <color alpha="255">
                      <red>136</red>
                      <green>138</green>
                      <blue>133</blue>
                     </color>
                    </brush>
                   </colorrole>
                   <colorrole role="Shadow">
                    <brush brushstyle="SolidPattern">
                     <color alpha="255">
                      <red>0</red>
                      <green>0</green>
                      <blue>0</blue>
                     </color>
                    </brush>
                   </colorrole>
                   <colorrole role="AlternateBase">
                    <brush brushstyle="SolidPattern">
                     <color alpha="255">
                      <red>195</red>
                      <green>196</green>
                      <blue>194</blue>
                     </color>
                    </brush>
                   </colorrole>
                  </inactive>
                  <disabled>
                   <colorrole role="Button">
                    <brush brushstyle="SolidPattern">
                     <color alpha="255">
                      <red>136</red>
                      <green>138</green>
                      <blue>133</blue>
                     </color>
                    </brush>
                   </colorrole>
                   <colorrole role="Light">
                    <brush brushstyle="SolidPattern">
                     <color alpha="255">
                      <red>204</red>
                      <green>207</green>
                      <blue>200</blue>
                     </color>
                    </brush>
                   </colorrole>
                   <colorrole role="Midlight">
                    <brush brushstyle="SolidPattern">
                     <color alpha="255">
                      <red>170</red>
                      <green>172</green>
                      <blue>166</blue>
                     </color>
                    </brush>
                   </colorrole>
                   <colorrole role="Dark">
                    <brush brushstyle="SolidPattern">
                     <color alpha="255">
                      <red>68</red>
                      <green>69</green>
                      <blue>66</blue>
                     </color>
                    </brush>
                   </colorrole>
                   <colorrole role="Mid">
                    <brush brushstyle="SolidPattern">
                     <color alpha="255">
                      <red>91</red>
                      <green>92</green>
                      <blue>89</blue>
                     </color>
                    </brush>
                   </colorrole>
                   <colorrole role="Window">
                    <brush brushstyle="SolidPattern">
                     <color alpha="255">
                      <red>136</red>
                      <green>138</green>
                      <blue>133</blue>
                     </color>
                    </brush>
                   </colorrole>
                   <colorrole role="Shadow">
                    <brush brushstyle="SolidPattern">
                     <color alpha="255">
                      <red>0</red>
                      <green>0</green>
                      <blue>0</blue>
                     </color>
                    </brush>
                   </colorrole>
                   <colorrole role="AlternateBase">
                    <brush brushstyle="SolidPattern">
                     <color alpha="255">
                      <red>136</red>
                      <green>138</green>
                      <blue>133</blue>
                     </color>
                    </brush>
                   </colorrole>
                  </disabled>
                 </palette>
                </property>
                <property name="text">
                 <string/>
                </property>
               </widget>
              </item>
              <item row="2" column="4">
               <widget class="QLineEdit" name="lineedit_test_slope">
                <property name="enabled">
                 <bool>false</bool>
                </property>
               </widget>
              </item>
              <item row="1" column="4">
               <widget class="QLineEdit" name="lineedit_azi_start"/>
              </item>
              <item row="0" column="2">
               <widget class="QLineEdit" name="lineedit_zero_set"/>
              </item>
              <item row="1" column="2">
               <widget class="QLineEdit" name="lineedit_zero_pos"/>
              </item>
              <item row="2" column="5">
               <widget class="QPushButton" name="btn_self_test">
                <property name="text">
                 <string>开始自检</string>
                </property>
               </widget>
              </item>
              <item row="1" column="1">
               <widget class="QLabel" name="label_17">
                <property name="text">
                 <string>转台位置：</string>
                </property>
               </widget>
              </item>
              <item row="2" column="8">
               <widget class="QPushButton" name="button_zero_self_test">
                <property name="text">
                 <string>自检</string>
                </property>
               </widget>
              </item>
              <item row="2" column="7">
               <widget class="QPushButton" name="button_zero_calib">
                <property name="text">
                 <string>标定</string>
                </property>
               </widget>
              </item>
              <item row="0" column="4">
               <widget class="QLabel" name="label_24">
                <property name="text">
                 <string>稳态误差:</string>
                </property>
               </widget>
              </item>
              <item row="0" column="5">
               <widget class="QDoubleSpinBox" name="spinbox_zero_stable_error">
                <property name="decimals">
                 <number>4</number>
                </property>
                <property name="minimum">
                 <double>-360.000000000000000</double>
                </property>
                <property name="maximum">
                 <double>360.000000000000000</double>
                </property>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <widget class="QGroupBox" name="groupbox_bit_compare_op">
             <property name="title">
              <string>测试对比</string>
             </property>
             <layout class="QGridLayout" name="gridLayout_14">
              <item row="1" column="4">
               <widget class="QPushButton" name="button_compare">
                <property name="text">
                 <string>对比</string>
                </property>
               </widget>
              </item>
              <item row="1" column="0">
               <widget class="QLabel" name="label_22">
                <property name="maximumSize">
                 <size>
                  <width>30</width>
                  <height>16777215</height>
                 </size>
                </property>
                <property name="text">
                 <string>bit2:</string>
                </property>
               </widget>
              </item>
              <item row="3" column="3">
               <widget class="QPushButton" name="button_write_static">
                <property name="text">
                 <string>静标</string>
                </property>
               </widget>
              </item>
              <item row="3" column="4">
               <widget class="QPushButton" name="button_write_two_abs">
                <property name="text">
                 <string>近距离</string>
                </property>
               </widget>
              </item>
              <item row="0" column="0">
               <widget class="QLabel" name="label_21">
                <property name="text">
                 <string>bit1:</string>
                </property>
               </widget>
              </item>
              <item row="3" column="0">
               <widget class="QLabel" name="label_3">
                <property name="text">
                 <string>写入</string>
                </property>
               </widget>
              </item>
              <item row="3" column="1">
               <widget class="QPushButton" name="button_write_refl">
                <property name="text">
                 <string>反标</string>
                </property>
               </widget>
              </item>
              <item row="3" column="5">
               <widget class="QPushButton" name="button_write_abs">
                <property name="text">
                 <string>绝标</string>
                </property>
               </widget>
              </item>
              <item row="3" column="2">
               <widget class="QPushButton" name="button_write_dynamic">
                <property name="text">
                 <string>动标</string>
                </property>
               </widget>
              </item>
              <item row="2" column="0">
               <widget class="QLabel" name="label_4">
                <property name="text">
                 <string>文件:</string>
                </property>
               </widget>
              </item>
              <item row="1" column="1" colspan="3">
               <widget class="QLineEdit" name="lineedit_bit2"/>
              </item>
              <item row="0" column="1" colspan="3">
               <widget class="QLineEdit" name="lineedit_bit1"/>
              </item>
              <item row="2" column="1" colspan="4">
               <widget class="QLineEdit" name="lineedit_bit_file_path"/>
              </item>
              <item row="2" column="5">
               <widget class="QPushButton" name="button_select_bit_file">
                <property name="text">
                 <string>选择</string>
                </property>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <widget class="QGroupBox" name="groupbox_process_op">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>70</height>
              </size>
             </property>
             <property name="title">
              <string>回波手动</string>
             </property>
             <layout class="QGridLayout" name="gridLayout_12" columnstretch="0,0,0,0,0,0,0,0,0,0,0,0">
              <item row="3" column="2">
               <widget class="QLabel" name="label_2">
                <property name="text">
                 <string>处理数据路径:</string>
                </property>
               </widget>
              </item>
              <item row="2" column="0">
               <widget class="QPushButton" name="button_select_process_pcap">
                <property name="text">
                 <string>选取pcap</string>
                </property>
               </widget>
              </item>
              <item row="4" column="0">
               <widget class="QCheckBox" name="checkbox_process_show_window">
                <property name="text">
                 <string>显示窗口</string>
                </property>
               </widget>
              </item>
              <item row="3" column="3" colspan="9">
               <widget class="QLineEdit" name="lineedit_data_path"/>
              </item>
              <item row="3" column="1">
               <widget class="QCheckBox" name="checkbox_dyn_angle_comp">
                <property name="text">
                 <string>动标角补偿</string>
                </property>
               </widget>
              </item>
              <item row="3" column="0">
               <widget class="QSpinBox" name="spinbox_process_num">
                <property name="suffix">
                 <string/>
                </property>
                <property name="prefix">
                 <string>进程数</string>
                </property>
                <property name="minimum">
                 <number>1</number>
                </property>
                <property name="maximum">
                 <number>12</number>
                </property>
                <property name="value">
                 <number>4</number>
                </property>
               </widget>
              </item>
              <item row="2" column="1">
               <widget class="QLabel" name="label_12">
                <property name="text">
                 <string>pcap路径:</string>
                </property>
               </widget>
              </item>
              <item row="2" column="2" colspan="6">
               <widget class="QLineEdit" name="lineedit_process_file_path">
                <property name="minimumSize">
                 <size>
                  <width>200</width>
                  <height>0</height>
                 </size>
                </property>
               </widget>
              </item>
              <item row="2" column="8">
               <widget class="QProgressBar" name="progressbar_process">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>16777215</width>
                  <height>16777215</height>
                 </size>
                </property>
                <property name="value">
                 <number>0</number>
                </property>
                <property name="format">
                 <string>处理%p%</string>
                </property>
               </widget>
              </item>
              <item row="2" column="9">
               <widget class="QProgressBar" name="progressbar_save_process_data">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="minimumSize">
                 <size>
                  <width>0</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="value">
                 <number>0</number>
                </property>
                <property name="format">
                 <string>保存%p%</string>
                </property>
               </widget>
              </item>
              <item row="2" column="11">
               <widget class="QPushButton" name="button_custom_process">
                <property name="text">
                 <string>处理</string>
                </property>
               </widget>
              </item>
              <item row="4" column="1">
               <widget class="QCheckBox" name="checkbox_process_save_image">
                <property name="text">
                 <string>保存图片</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item row="4" column="2">
               <widget class="QLabel" name="label_5">
                <property name="text">
                 <string>通道号:</string>
                </property>
               </widget>
              </item>
              <item row="4" column="3">
               <widget class="QComboBox" name="combobox_display_chn"/>
              </item>
              <item row="4" column="4">
               <widget class="QLabel" name="label_15">
                <property name="text">
                 <string>显示类型:</string>
                </property>
               </widget>
              </item>
              <item row="4" column="8">
               <widget class="QComboBox" name="combobox_display_type">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>16777215</width>
                  <height>16777215</height>
                 </size>
                </property>
                <item>
                 <property name="text">
                  <string>board</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>dynamic</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>static</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>refl</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>amp</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>abs</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>zero</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>board,dynamic,static,refl,amp,abs</string>
                 </property>
                </item>
               </widget>
              </item>
              <item row="4" column="9">
               <widget class="QProgressBar" name="progressbar_generate_figure">
                <property name="value">
                 <number>0</number>
                </property>
                <property name="format">
                 <string>生成%p%</string>
                </property>
               </widget>
              </item>
              <item row="4" column="11">
               <widget class="QPushButton" name="button_display_process_data">
                <property name="text">
                 <string>生成</string>
                </property>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
          </layout>
         </widget>
         <widget class="QWidget" name="function_page">
          <widget class="QGroupBox" name="groupBox_7">
           <property name="geometry">
            <rect>
             <x>10</x>
             <y>20</y>
             <width>687</width>
             <height>481</height>
            </rect>
           </property>
           <property name="title">
            <string>VBD标定</string>
           </property>
           <layout class="QGridLayout" name="gridLayout_17">
            <item row="4" column="3" rowspan="2" colspan="3">
             <widget class="QGroupBox" name="groupBox_11">
              <property name="title">
               <string>vbd_intercept</string>
              </property>
              <layout class="QGridLayout" name="gridLayout_15">
               <item row="0" column="0">
                <widget class="QDoubleSpinBox" name="spinbox_vbd_intercept">
                 <property name="prefix">
                  <string>float: </string>
                 </property>
                 <property name="decimals">
                  <number>6</number>
                 </property>
                 <property name="minimum">
                  <double>-9999999999.000000000000000</double>
                 </property>
                 <property name="maximum">
                  <double>9999999999.000000000000000</double>
                 </property>
                </widget>
               </item>
               <item row="1" column="0">
                <widget class="QSpinBox" name="spinbox_vbd_intercept_hex">
                 <property name="prefix">
                  <string>0x</string>
                 </property>
                 <property name="maximum">
                  <number>16777215</number>
                 </property>
                 <property name="stepType">
                  <enum>QAbstractSpinBox::DefaultStepType</enum>
                 </property>
                 <property name="value">
                  <number>0</number>
                 </property>
                 <property name="displayIntegerBase">
                  <number>16</number>
                 </property>
                </widget>
               </item>
              </layout>
             </widget>
            </item>
            <item row="6" column="0" colspan="7">
             <widget class="QGroupBox" name="groupBox_13">
              <property name="title">
               <string>vbd曲线</string>
              </property>
              <layout class="QGridLayout" name="gridLayout_16">
               <item row="0" column="4">
                <widget class="QPushButton" name="button_vbd_calculate_intercept">
                 <property name="text">
                  <string>计算vbd_intercept</string>
                 </property>
                </widget>
               </item>
               <item row="0" column="0">
                <widget class="QSpinBox" name="spinbox_vbd_v0">
                 <property name="prefix">
                  <string>V0: 0x</string>
                 </property>
                 <property name="maximum">
                  <number>65535</number>
                 </property>
                 <property name="displayIntegerBase">
                  <number>16</number>
                 </property>
                </widget>
               </item>
               <item row="0" column="1">
                <widget class="QSpinBox" name="spinbox_vbd_v1">
                 <property name="prefix">
                  <string>V1: 0x</string>
                 </property>
                 <property name="maximum">
                  <number>65535</number>
                 </property>
                 <property name="displayIntegerBase">
                  <number>16</number>
                 </property>
                </widget>
               </item>
               <item row="0" column="2">
                <widget class="QSpinBox" name="spinbox_vbd_v2">
                 <property name="prefix">
                  <string>V2: 0x</string>
                 </property>
                 <property name="maximum">
                  <number>65535</number>
                 </property>
                 <property name="displayIntegerBase">
                  <number>16</number>
                 </property>
                </widget>
               </item>
               <item row="0" column="3">
                <widget class="QPushButton" name="button_vbd_read_curve">
                 <property name="text">
                  <string>读取曲线</string>
                 </property>
                </widget>
               </item>
              </layout>
             </widget>
            </item>
            <item row="2" column="4" colspan="2">
             <widget class="QPushButton" name="button_vbd_load">
              <property name="text">
               <string>加载vbd</string>
              </property>
             </widget>
            </item>
            <item row="1" column="2" colspan="4">
             <widget class="QLineEdit" name="lineedit_vbd_save_dir"/>
            </item>
            <item row="0" column="0">
             <widget class="QLabel" name="label_6">
              <property name="text">
               <string>vbd文件:</string>
              </property>
             </widget>
            </item>
            <item row="2" column="2" colspan="2">
             <widget class="QDoubleSpinBox" name="spinbox_vbd">
              <property name="prefix">
               <string>vbd: </string>
              </property>
              <property name="decimals">
               <number>8</number>
              </property>
              <property name="minimum">
               <double>-99999999.000000000000000</double>
              </property>
              <property name="maximum">
               <double>99999999.000000000000000</double>
              </property>
             </widget>
            </item>
            <item row="2" column="1">
             <widget class="QLineEdit" name="lineedit_vbd_sn"/>
            </item>
            <item row="7" column="0" colspan="7">
             <widget class="QGroupBox" name="groupBox_12">
              <property name="title">
               <string>读取vbd</string>
              </property>
              <widget class="QPushButton" name="button_vbd_read">
               <property name="geometry">
                <rect>
                 <x>500</x>
                 <y>30</y>
                 <width>111</width>
                 <height>25</height>
                </rect>
               </property>
               <property name="text">
                <string>读取vbd截距</string>
               </property>
              </widget>
              <widget class="QSpinBox" name="spinbox_vbd_err_read">
               <property name="geometry">
                <rect>
                 <x>20</x>
                 <y>30</y>
                 <width>188</width>
                 <height>25</height>
                </rect>
               </property>
               <property name="prefix">
                <string>vbd_err 0x</string>
               </property>
               <property name="maximum">
                <number>65535</number>
               </property>
               <property name="stepType">
                <enum>QAbstractSpinBox::DefaultStepType</enum>
               </property>
               <property name="value">
                <number>0</number>
               </property>
               <property name="displayIntegerBase">
                <number>16</number>
               </property>
              </widget>
              <widget class="QSpinBox" name="spinbox_vbd_intercept_read">
               <property name="geometry">
                <rect>
                 <x>270</x>
                 <y>30</y>
                 <width>211</width>
                 <height>25</height>
                </rect>
               </property>
               <property name="prefix">
                <string>vbd_intercept 0x</string>
               </property>
               <property name="maximum">
                <number>16777215</number>
               </property>
               <property name="stepType">
                <enum>QAbstractSpinBox::DefaultStepType</enum>
               </property>
               <property name="value">
                <number>0</number>
               </property>
               <property name="displayIntegerBase">
                <number>16</number>
               </property>
              </widget>
             </widget>
            </item>
            <item row="2" column="0">
             <widget class="QLabel" name="label_13">
              <property name="text">
               <string>SN:</string>
              </property>
             </widget>
            </item>
            <item row="5" column="6">
             <widget class="QPushButton" name="button_vbd_load_and_write">
              <property name="text">
               <string>一键写入</string>
              </property>
             </widget>
            </item>
            <item row="0" column="1" colspan="4">
             <widget class="QLineEdit" name="lineedit_vbd_file_path"/>
            </item>
            <item row="1" column="0" colspan="2">
             <widget class="QLabel" name="label_23">
              <property name="text">
               <string>生成文件保存路径: </string>
              </property>
             </widget>
            </item>
            <item row="4" column="0" rowspan="2" colspan="3">
             <widget class="QGroupBox" name="groupBox_9">
              <property name="title">
               <string>vbd_err</string>
              </property>
              <layout class="QGridLayout" name="gridLayout_13">
               <item row="0" column="0">
                <widget class="QDoubleSpinBox" name="spinbox_vbd_err">
                 <property name="prefix">
                  <string>float: </string>
                 </property>
                 <property name="decimals">
                  <number>6</number>
                 </property>
                 <property name="minimum">
                  <double>-65536.000000000000000</double>
                 </property>
                 <property name="maximum">
                  <double>65535.000000000000000</double>
                 </property>
                </widget>
               </item>
               <item row="1" column="0">
                <widget class="QSpinBox" name="spinbox_vbd_err_hex">
                 <property name="prefix">
                  <string>0x</string>
                 </property>
                 <property name="maximum">
                  <number>65535</number>
                 </property>
                 <property name="stepType">
                  <enum>QAbstractSpinBox::DefaultStepType</enum>
                 </property>
                 <property name="value">
                  <number>0</number>
                 </property>
                 <property name="displayIntegerBase">
                  <number>16</number>
                 </property>
                </widget>
               </item>
              </layout>
             </widget>
            </item>
            <item row="0" column="5" colspan="2">
             <widget class="QPushButton" name="button_load_vbd_file">
              <property name="text">
               <string>打开文件</string>
              </property>
             </widget>
            </item>
            <item row="4" column="6">
             <widget class="QPushButton" name="button_vbd_write_flash">
              <property name="text">
               <string>写入flash</string>
              </property>
             </widget>
            </item>
            <item row="3" column="0" colspan="2">
             <widget class="QGroupBox" name="groupBox_14">
              <property name="title">
               <string>计算参数</string>
              </property>
              <layout class="QGridLayout" name="gridLayout_18">
               <item row="0" column="0">
                <widget class="QDoubleSpinBox" name="spin_box_vbd_cal_k">
                 <property name="prefix">
                  <string>斜率k: </string>
                 </property>
                 <property name="decimals">
                  <number>6</number>
                 </property>
                 <property name="minimum">
                  <double>-9999.000000000000000</double>
                 </property>
                 <property name="maximum">
                  <double>9999.000000000000000</double>
                 </property>
                 <property name="singleStep">
                  <double>1.000000000000000</double>
                 </property>
                 <property name="value">
                  <double>1.599000000000000</double>
                 </property>
                </widget>
               </item>
              </layout>
             </widget>
            </item>
           </layout>
          </widget>
         </widget>
         <widget class="QWidget" name="para_page">
          <layout class="QGridLayout" name="gridLayout_5"/>
         </widget>
         <widget class="QWidget" name="mes_setting_page">
          <layout class="QGridLayout" name="gridLayout">
           <property name="leftMargin">
            <number>0</number>
           </property>
           <property name="topMargin">
            <number>0</number>
           </property>
           <property name="rightMargin">
            <number>0</number>
           </property>
           <property name="bottomMargin">
            <number>0</number>
           </property>
           <property name="spacing">
            <number>0</number>
           </property>
           <item row="0" column="0">
            <widget class="QScrollArea" name="scrollArea">
             <property name="enabled">
              <bool>true</bool>
             </property>
             <property name="sizePolicy">
              <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>0</height>
              </size>
             </property>
             <property name="layoutDirection">
              <enum>Qt::LeftToRight</enum>
             </property>
             <property name="verticalScrollBarPolicy">
              <enum>Qt::ScrollBarAsNeeded</enum>
             </property>
             <property name="sizeAdjustPolicy">
              <enum>QAbstractScrollArea::AdjustToContents</enum>
             </property>
             <property name="widgetResizable">
              <bool>true</bool>
             </property>
             <property name="alignment">
              <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignTop</set>
             </property>
             <widget class="QWidget" name="scrollAreaWidgetContents">
              <property name="geometry">
               <rect>
                <x>0</x>
                <y>0</y>
                <width>916</width>
                <height>1080</height>
               </rect>
              </property>
              <property name="sizePolicy">
               <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>0</width>
                <height>1080</height>
               </size>
              </property>
              <layout class="QGridLayout" name="gridLayout_6">
               <item row="0" column="0" rowspan="2">
                <widget class="robosense::lidar::rsfsc_lib::WidgetLogSetting" name="widget_mes" native="true">
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <property name="minimumSize">
                  <size>
                   <width>0</width>
                   <height>0</height>
                  </size>
                 </property>
                 <property name="maximumSize">
                  <size>
                   <width>500</width>
                   <height>1080</height>
                  </size>
                 </property>
                </widget>
               </item>
               <item row="0" column="1">
                <layout class="QHBoxLayout" name="horizontalLayout_9">
                 <item>
                  <widget class="QLabel" name="label_8">
                   <property name="text">
                    <string>PC密码:</string>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <widget class="QLineEdit" name="lineedit_pc_password">
                   <property name="echoMode">
                    <enum>QLineEdit::PasswordEchoOnEdit</enum>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <spacer name="horizontalSpacer_11">
                   <property name="orientation">
                    <enum>Qt::Horizontal</enum>
                   </property>
                   <property name="sizeHint" stdset="0">
                    <size>
                     <width>40</width>
                     <height>20</height>
                    </size>
                   </property>
                  </spacer>
                 </item>
                </layout>
               </item>
               <item row="1" column="1">
                <spacer name="verticalSpacer_4">
                 <property name="orientation">
                  <enum>Qt::Vertical</enum>
                 </property>
                 <property name="sizeHint" stdset="0">
                  <size>
                   <width>17</width>
                   <height>1026</height>
                  </size>
                 </property>
                </spacer>
               </item>
              </layout>
             </widget>
            </widget>
           </item>
          </layout>
         </widget>
        </widget>
       </item>
       <item>
        <widget class="robosense::lidar::rsfsc_lib::MessageBrowser" name="widget_browser" native="true">
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>300</height>
          </size>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
    </item>
    <item row="0" column="0">
     <widget class="QWidget" name="widget_navigation" native="true">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="maximumSize">
       <size>
        <width>16777215</width>
        <height>16777215</height>
       </size>
      </property>
      <property name="styleSheet">
       <string notr="true"/>
      </property>
      <layout class="QVBoxLayout" name="verticalLayout_2">
       <property name="spacing">
        <number>0</number>
       </property>
       <property name="leftMargin">
        <number>0</number>
       </property>
       <property name="topMargin">
        <number>0</number>
       </property>
       <property name="rightMargin">
        <number>0</number>
       </property>
       <property name="bottomMargin">
        <number>0</number>
       </property>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout">
         <property name="topMargin">
          <number>15</number>
         </property>
         <property name="bottomMargin">
          <number>5</number>
         </property>
         <item>
          <spacer name="horizontalSpacer_2">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>18</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QLabel" name="label">
           <property name="maximumSize">
            <size>
             <width>35</width>
             <height>35</height>
            </size>
           </property>
           <property name="styleSheet">
            <string notr="true">border-radius: 20px</string>
           </property>
           <property name="text">
            <string/>
           </property>
           <property name="textFormat">
            <enum>Qt::PlainText</enum>
           </property>
           <property name="pixmap">
            <pixmap resource="../resource/resource.qrc">:/img/icon.png</pixmap>
           </property>
           <property name="scaledContents">
            <bool>true</bool>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>18</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </item>
       <item>
        <layout class="QVBoxLayout" name="verticalLayout">
         <property name="spacing">
          <number>15</number>
         </property>
         <property name="leftMargin">
          <number>10</number>
         </property>
         <property name="topMargin">
          <number>15</number>
         </property>
         <property name="rightMargin">
          <number>10</number>
         </property>
         <property name="bottomMargin">
          <number>10</number>
         </property>
         <item>
          <widget class="QPushButton" name="button_main_page">
           <property name="minimumSize">
            <size>
             <width>85</width>
             <height>40</height>
            </size>
           </property>
           <property name="font">
            <font>
             <pointsize>12</pointsize>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true">QPushButton:checked{
	background-color: rgb(229, 234, 240);
	border-color: rgb(229, 234, 240);
	border-radius: 20px;
	font-weight: bold;
}
QPushButton:hover{
	background-color: rgb(229, 234, 240);
	border-color: rgb(229, 234, 240);
	border-radius: 20px;
	font-weight: bold;
}

</string>
           </property>
           <property name="text">
            <string>主页</string>
           </property>
           <property name="icon">
            <iconset resource="../resource/resource.qrc">
             <normaloff>:/icons/home.svg</normaloff>
             <activeon>:/icons/home_filled.svg</activeon>
             <selectedon>:/icons/home_filled.svg</selectedon>:/icons/home.svg</iconset>
           </property>
           <property name="iconSize">
            <size>
             <width>30</width>
             <height>20</height>
            </size>
           </property>
           <property name="checkable">
            <bool>true</bool>
           </property>
           <property name="checked">
            <bool>true</bool>
           </property>
           <property name="autoExclusive">
            <bool>true</bool>
           </property>
           <property name="autoDefault">
            <bool>false</bool>
           </property>
           <property name="flat">
            <bool>true</bool>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="button_dev_tools">
           <property name="minimumSize">
            <size>
             <width>85</width>
             <height>40</height>
            </size>
           </property>
           <property name="font">
            <font>
             <pointsize>12</pointsize>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true">QPushButton:checked{
	background-color: rgb(229, 234, 240);
	border-color: rgb(229, 234, 240);
	border-radius: 20px;
	font-weight: bold;
}
QPushButton:hover{
	background-color: rgb(229, 234, 240);
	border-color: rgb(229, 234, 240);
	border-radius: 20px;
	font-weight: bold;
}

</string>
           </property>
           <property name="text">
            <string>工具</string>
           </property>
           <property name="icon">
            <iconset resource="../resource/resource.qrc">
             <normaloff>:/icons/dev_tools.svg</normaloff>
             <activeon>:/icons/dev_tools_filled.svg</activeon>:/icons/dev_tools.svg</iconset>
           </property>
           <property name="iconSize">
            <size>
             <width>20</width>
             <height>20</height>
            </size>
           </property>
           <property name="checkable">
            <bool>true</bool>
           </property>
           <property name="autoExclusive">
            <bool>true</bool>
           </property>
           <property name="flat">
            <bool>true</bool>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="button_data_process">
           <property name="minimumSize">
            <size>
             <width>85</width>
             <height>40</height>
            </size>
           </property>
           <property name="styleSheet">
            <string notr="true">QPushButton:checked{
	background-color: rgb(229, 234, 240);
	border-color: rgb(229, 234, 240);
	border-radius: 20px;
	font-weight: bold;
}
QPushButton:hover{
	background-color: rgb(229, 234, 240);
	border-color: rgb(229, 234, 240);
	border-radius: 20px;
	font-weight: bold;
}

</string>
           </property>
           <property name="text">
            <string>数据</string>
           </property>
           <property name="icon">
            <iconset>
             <activeoff>:/icons/data_scatter.svg</activeoff>
             <activeon>:/icons/data_scatter_filled.svg</activeon>
             <selectedoff>:/icons/data_scatter.svg</selectedoff>
            </iconset>
           </property>
           <property name="iconSize">
            <size>
             <width>20</width>
             <height>20</height>
            </size>
           </property>
           <property name="checkable">
            <bool>true</bool>
           </property>
           <property name="autoExclusive">
            <bool>true</bool>
           </property>
           <property name="flat">
            <bool>true</bool>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="button_function">
           <property name="minimumSize">
            <size>
             <width>85</width>
             <height>40</height>
            </size>
           </property>
           <property name="styleSheet">
            <string notr="true">QPushButton:checked{
	background-color: rgb(229, 234, 240);
	border-color: rgb(229, 234, 240);
	border-radius: 20px;
	font-weight: bold;
}
QPushButton:hover{
	background-color: rgb(229, 234, 240);
	border-color: rgb(229, 234, 240);
	border-radius: 20px;
	font-weight: bold;
}

</string>
           </property>
           <property name="text">
            <string>功能</string>
           </property>
           <property name="icon">
            <iconset>
             <activeoff>:/icons/data_scatter.svg</activeoff>
             <activeon>:/icons/data_scatter_filled.svg</activeon>
             <selectedoff>:/icons/data_scatter.svg</selectedoff>
            </iconset>
           </property>
           <property name="iconSize">
            <size>
             <width>20</width>
             <height>20</height>
            </size>
           </property>
           <property name="checkable">
            <bool>true</bool>
           </property>
           <property name="autoExclusive">
            <bool>true</bool>
           </property>
           <property name="flat">
            <bool>true</bool>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="button_param">
           <property name="minimumSize">
            <size>
             <width>85</width>
             <height>40</height>
            </size>
           </property>
           <property name="font">
            <font>
             <pointsize>12</pointsize>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true">QPushButton:checked{
	background-color: rgb(229, 234, 240);
	border-color: rgb(229, 234, 240);
	border-radius: 20px;
	font-weight: bold;
}
QPushButton:hover{
	background-color: rgb(229, 234, 240);
	border-color: rgb(229, 234, 240);
	border-radius: 20px;
	font-weight: bold;
}

</string>
           </property>
           <property name="text">
            <string>参数</string>
           </property>
           <property name="icon">
            <iconset resource="../resource/resource.qrc">
             <normaloff>:/icons/equalizer.svg</normaloff>
             <activeon>:/icons/equalizer_filled.svg</activeon>:/icons/equalizer.svg</iconset>
           </property>
           <property name="iconSize">
            <size>
             <width>20</width>
             <height>20</height>
            </size>
           </property>
           <property name="checkable">
            <bool>true</bool>
           </property>
           <property name="autoExclusive">
            <bool>true</bool>
           </property>
           <property name="flat">
            <bool>true</bool>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="verticalSpacer">
           <property name="orientation">
            <enum>Qt::Vertical</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>20</width>
             <height>565</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QPushButton" name="button_mes_setting">
           <property name="minimumSize">
            <size>
             <width>85</width>
             <height>40</height>
            </size>
           </property>
           <property name="font">
            <font>
             <pointsize>12</pointsize>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true">QPushButton:checked{
	background-color: rgb(229, 234, 240);
	border-color: rgb(229, 234, 240);
	border-radius: 20px;
	font-weight: bold;
}
QPushButton:hover{
	background-color: rgb(229, 234, 240);
	border-color: rgb(229, 234, 240);
	border-radius: 20px;
	font-weight: bold;
}

</string>
           </property>
           <property name="text">
            <string>mes</string>
           </property>
           <property name="icon">
            <iconset resource="../resource/resource.qrc">
             <normaloff>:/icons/setting.svg</normaloff>
             <activeon>:/icons/setting_filled.svg</activeon>:/icons/setting.svg</iconset>
           </property>
           <property name="iconSize">
            <size>
             <width>20</width>
             <height>20</height>
            </size>
           </property>
           <property name="checkable">
            <bool>true</bool>
           </property>
           <property name="autoExclusive">
            <bool>true</bool>
           </property>
           <property name="flat">
            <bool>true</bool>
           </property>
          </widget>
         </item>
        </layout>
       </item>
      </layout>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>1061</width>
     <height>22</height>
    </rect>
   </property>
  </widget>
 </widget>
 <customwidgets>
  <customwidget>
   <class>robosense::lidar::rsfsc_lib::MessageBrowser</class>
   <extends>QWidget</extends>
   <header>widgets/message_browser.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>WidgetLidarInfoList</class>
   <extends>QWidget</extends>
   <header>widgets/widget_lidar_info_list.h</header>
   <container>1</container>
   <slots>
    <signal>signalButtonConnectClicked(quint32)</signal>
    <signal>signalButtonDisconnectClicked(quint32)</signal>
    <signal>signalButtonOpenDataFolderClicked(quint32)</signal>
   </slots>
  </customwidget>
  <customwidget>
   <class>robosense::lidar::rsfsc_lib::WidgetLogSetting</class>
   <extends>QWidget</extends>
   <header>widget_log_setting.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>WidgetExtDevice</class>
   <extends>QWidget</extends>
   <header>widget_ext_device.h</header>
   <container>1</container>
   <slots>
    <signal>signalUpdateRotatorXAngle(float)</signal>
   </slots>
  </customwidget>
  <customwidget>
   <class>robosense::lidar::LabelTestState</class>
   <extends>QWidget</extends>
   <header>widgets/label_test_state.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources>
  <include location="../resource/resource.qrc"/>
 </resources>
 <connections>
  <connection>
   <sender>button_dev_tools</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>slotButtonDevToolsClicked()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>74</x>
     <y>169</y>
    </hint>
    <hint type="destinationlabel">
     <x>0</x>
     <y>168</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>button_main_page</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>slotButtonMainPageClicked()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>40</x>
     <y>111</y>
    </hint>
    <hint type="destinationlabel">
     <x>0</x>
     <y>113</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>button_mes_setting</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>slotButtonMesSettingClicked()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>44</x>
     <y>598</y>
    </hint>
    <hint type="destinationlabel">
     <x>-77</x>
     <y>608</y>
    </hint>
   </hints>
  </connection>
 </connections>
 <slots>
  <signal>signalUpdatePageCurrentIndex(int)</signal>
  <slot>slotButtonMesSettingClicked()</slot>
  <slot>slotButtonMainPageClicked()</slot>
  <slot>slotButtonDevToolsClicked()</slot>
  <slot>slotButtonParamClicked()</slot>
 </slots>
</ui>
