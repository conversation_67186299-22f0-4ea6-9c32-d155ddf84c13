﻿/******************************************************************************
 * Copyright 2024 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai
 *
 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONS<PERSON>QUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "mainwindow.h"
#include "app_event.h"
#include "config.h"
#include "lidar_ctl.h"
#include "para_info.h"
#include "rsfsc_log/rsfsc_log_macro.h"
#include "rsfsc_utils/csv_utils.h"
#include "ui_mainwindow.h"
#include "utils/common.h"
#include "widget_card_box.h"
#include <QComboBox>
#include <QDesktopServices>
#include <QDialogButtonBox>
#include <QFileDialog>
#include <QFuture>
#include <QJsonDocument>
#include <QMessageBox>
#include <QProgressDialog>
#include <QSerialPortInfo>
#include <QSettings>
#include <QTextEdit>
#include <QTimer>
#include <cmath>
#include <memory>
#include <utils/crc_utils.h>

#include "matplotlibcpp.h"
namespace plt = matplotlibcpp;

// NOLINTNEXTLINE(google-build-using-namespace)
using namespace robosense::lidar;

MainWindow::MainWindow(QWidget* _parent) :
  QMainWindow(_parent), ui_(new Ui::MainWindow), camera_window_(nullptr)  // 初始化为nullptr
{
  qRegisterMetaType<TestState>("TestState");
  qRegisterMetaType<MsgResult*>("MsgResult*");
  qRegisterMetaType<std::vector<double>>("std::vector<double>");
  qRegisterMetaType<std::vector<float>>("std::vector<float>");
  qRegisterMetaType<std::vector<int>>("std::vector<int>");
  ui_->setupUi(this);
  app()->setMainWindow(this);
  initWidgets();

  initMesWidget();
  initSignalSlots();
  loadCsvAndJson();

  readSettings();
}

MainWindow::~MainWindow() { delete ui_; }

void MainWindow::initMesWidget()
{
  RSFSCLog::getInstance()->setQtLogWidget(ui_->widget_browser, "slotShowMessage");
  // auto* widget_log_setting = new WidgetLogSetting();
  app()->setWidgetLogSetting(ui_->widget_mes);
  app()->getWidgetLogSetting()->addCheckSumDir(std::string(INSTALL_PREFIX_SHARE) + "config");
  ui_->widget_lidar_info_list->createLidarInfo();
}
void MainWindow::initWidgets()
{
  QString title = fmt::format("{} {}_{}", PROJECT_NAME_ZH, PROJECT_VERSION_STR, PROJECT_COMPILE_TIME).c_str();
  setWindowTitle(title);

  // 判断当前程序运行的路径，如果是build目录下
  LOG_INFO("current path: {}", QDir::currentPath().toStdString());

  for (int chn_num = 1; chn_num <= 96; ++chn_num)
  {
    ui_->combobox_display_chn->addItem(QString::number(chn_num));
  }
  ui_->combobox_display_chn->addItem("all");

  // add_checkbox_to_layout(0, ui_->groupbox_chn_1->layout(), chn_ctrl_group_1_);
  // add_checkbox_to_layout(8, ui_->groupbox_chn_2->layout(), chn_ctrl_group_2_);
  // add_checkbox_to_layout(16, ui_->groupbox_chn_3->layout(), chn_ctrl_group_3_);

  setupUiParaPage();

  authority_widget_list_ << ui_->groupbox_lidar_op;
  authority_widget_list_ << ui_->groupbox_rotator_op;
  authority_widget_list_ << ui_->groupbox_collect_op;
  authority_widget_list_ << ui_->groupbox_zero_calib_op;
  authority_widget_list_ << ui_->groupbox_data_op;
  authority_widget_list_ << ui_->groupbox_test;
  authority_widget_list_ << ui_->checkbox_onekey_first_zero;
  authority_widget_list_ << ui_->checkbox_onekey_wave;
  authority_widget_list_ << ui_->checkbox_onekey_sec_zero;
  authority_widget_list_ << ui_->button_test;

  for (auto& widget : authority_widget_list_)
  {
    widget->setEnabled(false);
  }

  // 创建相机窗口并保存引用
  camera_window_ = new CameraWindow(this);
  ui_->camera->layout()->addWidget(camera_window_);
}

void MainWindow::loadCsvAndJson()
{
  QString root_path = INSTALL_PREFIX_SHARE;
  if (QDir::currentPath().contains("build"))
  {
    root_path = QDir::currentPath() + "/../";
  }
  else if (QDir::current().exists("build"))
  {
    root_path = QDir::currentPath() + "/";
  }
  QDir file_dir = QDir(root_path);
  app()->setScriptPath(file_dir.filePath("script/"));
  app()->setConfigPath(file_dir.filePath("config/"));

  loadCsv(root_path);
  loadJson(root_path);
}
void MainWindow::loadJson(const QString& _root_path)
{
  QDir file_dir            = QDir(_root_path);
  QStringList support_type = { "airy" };
  for (const auto& type : support_type)
  {
    QString json_file_path = file_dir.filePath("config/" + type + "_info.json");
    QFile file(json_file_path);
    if (!file.exists())
    {
      LOG_ERROR("json文件不存在: {}", json_file_path.toStdString());
      continue;
    }
    file.open(QIODevice::ReadOnly);
    QJsonDocument json_doc = QJsonDocument::fromJson(file.readAll());
    app()->setJsonBoardInfo(type.toStdString(), json_doc.object());
    LOG_INFO("加载json文件成功: {}", json_file_path.toStdString());
  }
}
void MainWindow::loadCsv(const QString& _root_path)
{
  QDir file_dir                    = QDir(_root_path);
  QStringList support_project_list = { "airy" };

  for (const auto& project : support_project_list)
  {
    QString register_file_name = project + "_register.csv";
    QString limit_file_name    = project + "_limit.csv";

    QString register_file_path = file_dir.filePath("config/" + register_file_name);
    QString limit_file_path    = file_dir.filePath("config/" + limit_file_name);

    std::shared_ptr<CsvUtils> reg_csv_parser_ptr      = std::make_shared<CsvUtils>();
    std::shared_ptr<CsvUtils> limit_csv_parser_ptr    = std::make_shared<CsvUtils>();
    std::shared_ptr<CsvUtils> angle_48_csv_parser_ptr = std::make_shared<CsvUtils>();
    std::shared_ptr<CsvUtils> angle_96_csv_parser_ptr = std::make_shared<CsvUtils>();
    try
    {
      reg_csv_parser_ptr->loadRegisterCsvInfo(register_file_path);
      limit_csv_parser_ptr->loadLimitCsvInfo(limit_file_path);

      app()->setCsvUtils(project + "_reg", reg_csv_parser_ptr);
      app()->setCsvUtils(project + "_limit", limit_csv_parser_ptr);
    }
    catch (const std::exception& e)
    {
      LOG_ERROR("加载csv文件失败: {}, {}", register_file_path.toStdString(), e.what());
      continue;
    }
  }
}
void MainWindow::closeEvent(QCloseEvent* _event)
{
  writeSettings();
  app()->getWidgetLogSetting()->deleteLater();
  RSFSCLog::getInstance()->setQtLogWidget(nullptr);
  // ui_->widget_ext_device->deleteLater();

  ui_->widget_ext_device->ext_device_worker_thread_->quit();
  ui_->widget_ext_device->ext_device_worker_thread_->wait();
  ui_->widget_ext_device->deleteLater();

  QMainWindow::closeEvent(_event);
}

void MainWindow::readSettings()
{
  LOG_INFO("FUC: read settings");
  QString suffix(PROJECT_NAME);
  suffix += QString::fromUtf8("/main_window");
  QSettings settings("RoboSense", suffix);

  restoreGeometry(settings.value("geometry").toByteArray());
  restoreState(settings.value("window_state").toByteArray());

  auto apply_setting = [&settings](auto* _widget, const QString& _key) {
    QVariant value = settings.value(_key);
    if (value.isValid())
    {
      // LOG_DEBUG("key is valid: {}, value: {}", _key.toStdString(), value.toString().toStdString());
      if (auto* check_box = dynamic_cast<QCheckBox*>(_widget))
      {
        check_box->setChecked(value.toBool());
      }
      else if (auto* spin_box = dynamic_cast<QSpinBox*>(_widget))
      {
        spin_box->setValue(value.toInt());
      }
      else if (auto* double_spin_box = dynamic_cast<QDoubleSpinBox*>(_widget))
      {
        double_spin_box->setValue(value.toDouble());
      }
      else if (auto* line_edit = dynamic_cast<QLineEdit*>(_widget))
      {
        line_edit->setText(value.toString());
      }
      else if (auto* label = dynamic_cast<QLabel*>(_widget))
      {
        label->setText(value.toString());
      }
      else if (auto* text_edit = dynamic_cast<QTextEdit*>(_widget))
      {
        text_edit->setText(value.toString());
      }
      else if (auto* combo_box = dynamic_cast<QComboBox*>(_widget))
      {
        combo_box->setCurrentText(value.toString());
      }
    }
  };

  for (auto* widget : ui_->centralwidget->findChildren<QWidget*>())
  {
    QString object_name = widget->objectName();
    if (object_name.isEmpty() || object_name.startsWith("qt_"))
    {
      continue;
    }
    // LOG_DEBUG("object_name: {}", object_name.toStdString());

    QString key = "ui/" + object_name;
    apply_setting(widget, key);
  }
}

void MainWindow::writeSettings()
{
  RSFSCLog::getInstance()->info("write settings");
  QString suffix(PROJECT_NAME);
  suffix += QString::fromUtf8("/main_window");
  QSettings settings("RoboSense", suffix);
  settings.setValue("geometry", saveGeometry());
  settings.setValue("window_state", saveState());

  auto apply_setting = [&settings](auto* _widget, const QString& _key) {
    if (auto* check_box = dynamic_cast<QCheckBox*>(_widget))
    {
      settings.setValue(_key, check_box->isChecked());
    }
    else if (auto* spin_box = dynamic_cast<QSpinBox*>(_widget))
    {
      settings.setValue(_key, spin_box->value());
    }
    else if (auto* double_spin_box = dynamic_cast<QDoubleSpinBox*>(_widget))
    {
      settings.setValue(_key, double_spin_box->value());
    }
    else if (auto* line_edit = dynamic_cast<QLineEdit*>(_widget))
    {
      settings.setValue(_key, line_edit->text());
    }
    // else if (auto* label = dynamic_cast<QLabel*>(_widget))
    // {
    //   settings.setValue(_key, label->text());
    // }
    else if (auto* text_edit = dynamic_cast<QTextEdit*>(_widget))
    {
      settings.setValue(_key, text_edit->toPlainText());
    }
    else if (auto* combo_box = dynamic_cast<QComboBox*>(_widget))
    {
      settings.setValue(_key, combo_box->currentText());
    }
  };

  for (auto* widget : ui_->centralwidget->findChildren<QWidget*>())
  {
    QString object_name = widget->objectName();
    if (object_name.isEmpty())
    {
      continue;
    }
    QString key = "ui/" + object_name;
    apply_setting(widget, key);
  }
  RSFSCLog::getInstance()->info("write settings finish");
}

void MainWindow::initSignalSlots()
{

  connect(ui_->button_data_process, &QPushButton::clicked, this, &MainWindow::slotButtonDataProcessClicked);
  connect(ui_->button_param, &QPushButton::clicked, this, &MainWindow::slotButtonParamClicked);
  connect(ui_->button_function, &QPushButton::clicked, this, &MainWindow::slotButtonFunctionClicked);

  connect(ui_->button_main_open_relay, &QPushButton::clicked, []() { extDevice()->slotRelayTurnOn(); });
  connect(ui_->button_main_close_relay, &QPushButton::clicked, []() { extDevice()->slotRelayTurnOff(); });

  connect(ui_->button_select_process_pcap, &QPushButton::clicked, this,
          &MainWindow::slotButtonSelectProcessPcapClicked);
  connect(ui_->btn_zero_path, &QPushButton::clicked, this, &MainWindow::slotButtonZeroPathClicked);

  connect(ui_->button_load_vbd_file, &QPushButton::clicked, this, &MainWindow::slotButtonLoadVbdFileClicked);

  connect(AppEvent::getInstance(), &AppEvent::signalFsmStarting, this, &MainWindow::slotFsmStarting);
  connect(AppEvent::getInstance(), &AppEvent::signalFsmStarted, this, &MainWindow::slotFsmStarted);
  connect(AppEvent::getInstance(), &AppEvent::signalFsmStopping, this, &MainWindow::slotFsmStopping);
  connect(AppEvent::getInstance(), &AppEvent::signalFsmStopped, this, &MainWindow::slotFsmStopped);
  connect(AppEvent::getInstance(), &AppEvent::signalUpdateZeroAngle, this, &MainWindow::slotUpdateZeroAngle);

  connect(AppEvent::getInstance(), &AppEvent::signalUpdateProgressProcessTotalTask, ui_->progressbar_main_process_data,
          &QProgressBar::setMaximum);
  connect(AppEvent::getInstance(), &AppEvent::signalUpdateProgressProcessData, ui_->progressbar_main_process_data,
          &QProgressBar::setValue);
  connect(app(), &AppEvent::signalUpdateProgressCollectTotalTask, ui_->progressbar_collect, &QProgressBar::setMaximum);
  connect(AppEvent::getInstance(), &AppEvent::signalUpdateProgressCollect, ui_->progressbar_collect,
          &QProgressBar::setValue);
  connect(AppEvent::getInstance(), &AppEvent::signalUpdateProgressProcessTotalTask, ui_->progressbar_process,
          &QProgressBar::setMaximum);
  connect(AppEvent::getInstance(), &AppEvent::signalUpdateProgressSaveProcessDataTotalTask,
          ui_->progressbar_save_process_data, &QProgressBar::setMaximum);
  connect(AppEvent::getInstance(), &AppEvent::signalUpdateProgressGenerateFigureTotalTask,
          ui_->progressbar_generate_figure, &QProgressBar::setMaximum);
  connect(AppEvent::getInstance(), &AppEvent::signalUpdateProgressProcessData, ui_->progressbar_process,
          &QProgressBar::setValue);
  connect(AppEvent::getInstance(), &AppEvent::signalUpdateProgressSaveProcessData, ui_->progressbar_save_process_data,
          &QProgressBar::setValue);
  connect(AppEvent::getInstance(), &AppEvent::signalUpdateProgressGenerateFigure, ui_->progressbar_generate_figure,
          &QProgressBar::setValue);

  connect(ui_->widget_ext_device, &::WidgetExtDevice::signalUpdateRotatorXAngle, this,
          &MainWindow::slotUpdateLabelRotatorXAngle);

  connect(app(), &AppEvent::signalUpdateProcessDataPath, ui_->lineedit_data_path, &QLineEdit::setText);

  connect(app(), &AppEvent::signalRelayTurnOff, this, [this]() {
    ui_->label_relay_state->setStyleSheet("background-color: red");
    ui_->label_relay_state->setText("已断电");
  });
  connect(app(), &AppEvent::signalRelayTurnOn, this, [this]() {
    ui_->label_relay_state->setStyleSheet("background-color: green");
    ui_->label_relay_state->setText("已上电");
  });

  connect(AppEvent::getInstance(), &AppEvent::signalLidarConnecting, this, &MainWindow::slotLidarConnecting);
  connect(AppEvent::getInstance(), &AppEvent::signalLidarConnected, this, &MainWindow::slotLidarConnected);
  connect(AppEvent::getInstance(), &AppEvent::signalLidarDisconnecting, this, &MainWindow::slotLidarDisconnecting);
  connect(AppEvent::getInstance(), &AppEvent::signalLidarDisconnected, this, &MainWindow::slotLidarDisconnected);

  connect(app(), &AppEvent::signalShowInfoText, this, &MainWindow::slotShowInfoText);
  connect(app(), &AppEvent::signalShowWarningText, this, &MainWindow::slotShowWarningText);
  connect(app(), &AppEvent::signalShowErrorText, this, &MainWindow::slotShowErrorText);
  connect(app(), &AppEvent::signalShowErrorMsg, this, &MainWindow::slotShowErrorMsg);
  connect(app(), &AppEvent::signalShowInfoVariant, this, &MainWindow::slotShowInfoVariant);

  connect(app(), &AppEvent::signalMsgBoxConfirm, this, &MainWindow::slotMsgBoxConfirm);
  connect(app(), &AppEvent::signalMsgBoxConfirmSpotAngle, this, &MainWindow::slotMsgBoxConfirmSpotAngle);

  connect(app(), &AppEvent::signalRelayTurnOff, this, [this]() {
    ui_->label_relay_state->setStyleSheet("background-color: red");
    ui_->label_relay_state->setText("已断电");
  });
  connect(app(), &AppEvent::signalRelayTurnOn, this, [this]() {
    ui_->label_relay_state->setStyleSheet("background-color: green");
    ui_->label_relay_state->setText("已上电");
  });

  connect(app(), &AppEvent::signalUpdateProductDisplay, this,
          [this](const QString& _product_display) { ui_->label_product_display->setText(_product_display); });
  connect(app(), &AppEvent::signalUpdateMowAreaType, this,
          [this](const QString& _mow_area_type) { ui_->label_mow_area_type->setText(_mow_area_type); });

  connect(ui_->button_write_refl, &QPushButton::clicked, this, &MainWindow::slotButtonWriteReflClicked);
  connect(ui_->button_write_dynamic, &QPushButton::clicked, this, &MainWindow::slotButtonWriteDynamicClicked);
  connect(ui_->button_write_static, &QPushButton::clicked, this, &MainWindow::slotButtonWriteStaticClicked);
  connect(ui_->button_write_two_abs, &QPushButton::clicked, this, &MainWindow::slotButtonWriteTwoAbsClicked);
  connect(ui_->button_write_abs, &QPushButton::clicked, this, &MainWindow::slotButtonWriteAbsClicked);
  connect(ui_->button_select_bit_file, &QPushButton::clicked, this, &MainWindow::slotButtonSelectBitFileClicked);

  // draw
  connect(app(), &AppEvent::signalDrawFigureDouble, this,
          [](const std::vector<double>& _x_vec, const std::vector<double>& _y_vec, MsgResult* _msg_result) {
            // plt::plot(_x_vec, _y_vec);
            plt::scatter(_x_vec, _y_vec);
            plt::show();
            std::lock_guard<std::mutex> lock(_msg_result->mutex);
            _msg_result->ret = true;
            _msg_result->cv.notify_all();
          });
  connect(app(), &AppEvent::signalDrawFigureFloat, this,
          [](const std::vector<float>& _x_vec, const std::vector<float>& _y_vec, MsgResult* _msg_result) {
            // plt::plot(_x_vec, _y_vec);
            plt::scatter(_x_vec, _y_vec);
            plt::show();
            std::lock_guard<std::mutex> lock(_msg_result->mutex);
            _msg_result->ret = true;
            _msg_result->cv.notify_all();
          });
  connect(app(), &AppEvent::signalDrawFigureInt, this,
          [](const std::vector<int>& _x_vec, const std::vector<int>& _y_vec, MsgResult* _msg_result) {
            // plt::plot(_x_vec, _y_vec);
            plt::scatter(_x_vec, _y_vec);
            plt::show();
            std::lock_guard<std::mutex> lock(_msg_result->mutex);
            _msg_result->ret = true;
            _msg_result->cv.notify_all();
          });

  connect(app()->getWidgetLogSetting(), &WidgetLogSetting::signalAuthorityUpdate, this,
          &MainWindow::slotUpdateAuthority);
}

void MainWindow::slotUpdateAuthority(robosense::lidar::rsfsc_lib::UserAuthority* _user_authority)
{
  if (_user_authority == nullptr)
  {
    return;
  }
  bool is_developer =
    _user_authority->isNotLessThan(robosense::lidar::rsfsc_lib::UserAuthority::Level::LEVEL_DEVELOPER) ||
    _user_authority->isNotLessThan(robosense::lidar::rsfsc_lib::UserAuthority::Level::LEVEL_MANAGER);

  for (auto& widget : authority_widget_list_)
  {
    widget->setEnabled(is_developer);
  }
}

void MainWindow::setupUiParaPage()
{
  auto* grid_layout = qobject_cast<QGridLayout*>(ui_->para_page->layout());
  if (grid_layout == nullptr)
  {
    LOG_ERROR("注册para页失败");
    return;
  }

  // 创建卡片容器
  auto* card_box = new robosense::lidar::WidgetCardBox(ui_->para_page);
  card_box->setFixedWidth(300);

  // 创建参数控件的容器widget
  auto* content_widget = new QWidget(card_box);
  auto* v_layout       = new QVBoxLayout(content_widget);
  v_layout->setSpacing(4);
  v_layout->setContentsMargins(6, 6, 6, 6);

  // 创建并添加采样间隔控件组
  auto* sampling_layout = new QHBoxLayout();
  sampling_layout->setSpacing(4);
  sampling_layout->setContentsMargins(0, 0, 0, 0);
  para_widgets_.label_sampling_interval   = new QLabel("采样间隔:", content_widget);
  para_widgets_.spinbox_sampling_interval = new QSpinBox(content_widget);
  para_widgets_.spinbox_sampling_interval->setObjectName("spinbox_sampling_interval");
  para_widgets_.spinbox_sampling_interval->setFixedWidth(60);
  sampling_layout->addWidget(para_widgets_.label_sampling_interval);
  sampling_layout->addWidget(para_widgets_.spinbox_sampling_interval);
  sampling_layout->addStretch();
  v_layout->addLayout(sampling_layout);

  // 创建并添加工厂位置控件组
  auto* factory_layout = new QHBoxLayout();
  factory_layout->setSpacing(4);
  factory_layout->setContentsMargins(0, 0, 0, 0);
  para_widgets_.label_factory_loc    = new QLabel("工厂位置:", content_widget);
  para_widgets_.combobox_factory_loc = new QComboBox(content_widget);
  para_widgets_.combobox_factory_loc->setObjectName("combobox_factory_loc");
  para_widgets_.combobox_factory_loc->setFixedWidth(60);
  para_widgets_.combobox_factory_loc->addItem("HHL");
  para_widgets_.combobox_factory_loc->addItem("SS");
  factory_layout->addWidget(para_widgets_.label_factory_loc);
  factory_layout->addWidget(para_widgets_.combobox_factory_loc);
  factory_layout->addStretch();
  v_layout->addLayout(factory_layout);

  auto* line_num_layout = new QHBoxLayout();
  line_num_layout->setSpacing(4);
  line_num_layout->setContentsMargins(0, 0, 0, 0);
  para_widgets_.label_line_num    = new QLabel("线数:", content_widget);
  para_widgets_.combobox_line_num = new QComboBox(content_widget);
  para_widgets_.combobox_line_num->setObjectName("combobox_line_num");
  para_widgets_.combobox_line_num->setFixedWidth(60);
  para_widgets_.combobox_line_num->addItem("48");
  para_widgets_.combobox_line_num->addItem("96");
  para_widgets_.combobox_line_num->setCurrentText("96");
  line_num_layout->addWidget(para_widgets_.label_line_num);
  line_num_layout->addWidget(para_widgets_.combobox_line_num);
  line_num_layout->addStretch();
  v_layout->addLayout(line_num_layout);

  // 添加一个lineedit，为网卡输入
  auto* eth_layout = new QHBoxLayout();
  eth_layout->setSpacing(4);
  eth_layout->setContentsMargins(0, 0, 0, 0);
  para_widgets_.label_eth_name    = new QLabel("网卡:", content_widget);
  para_widgets_.lineedit_eth_name = new QLineEdit(content_widget);
  para_widgets_.lineedit_eth_name->setObjectName("lineedit_eth_name");
  para_widgets_.lineedit_eth_name->setFixedWidth(60);
  para_widgets_.button_auto_get_eth = new QPushButton("自动获取", content_widget);
  para_widgets_.button_auto_get_eth->setObjectName("button_auto_get_eth");
  eth_layout->addWidget(para_widgets_.label_eth_name);
  eth_layout->addWidget(para_widgets_.lineedit_eth_name);
  eth_layout->addWidget(para_widgets_.button_auto_get_eth);
  v_layout->addLayout(eth_layout);

  auto* button_reload_config_para_layout = new QHBoxLayout();
  button_reload_config_para_layout->setSpacing(4);
  button_reload_config_para_layout->setContentsMargins(0, 0, 0, 0);
  para_widgets_.button_reload_config_para = new QPushButton("重新加载配置文件", content_widget);
  para_widgets_.button_reload_config_para->setObjectName("button_reload_config_para");
  button_reload_config_para_layout->addWidget(para_widgets_.button_reload_config_para);
  v_layout->addLayout(button_reload_config_para_layout);

  // 创建并添加保存按钮
  auto* button_layout = new QHBoxLayout();
  button_layout->setContentsMargins(0, 2, 0, 0);
  para_widgets_.button_save_para = new QPushButton("保存参数", content_widget);
  para_widgets_.button_save_para->setObjectName("button_save_para");
  // para_widgets.button_save_para->setFixedWidth(60);  // 设置宽度
  button_layout->addStretch();
  button_layout->addWidget(para_widgets_.button_save_para);
  button_layout->addStretch();
  v_layout->addLayout(button_layout);

  // 将内容widget设置到卡片中
  card_box->setContentWidget(content_widget);

  // 设置列的拉伸因子，使卡片占据左侧更小的空间
  grid_layout->setColumnStretch(0, 2);
  grid_layout->setColumnStretch(1, 8);
  grid_layout->addWidget(card_box, 0, 0);
  grid_layout->setRowStretch(1, 1);
}

void MainWindow::slotButtonMainPageClicked() { ui_->widget_page_all->setCurrentIndex(0); }
void MainWindow::slotButtonDevToolsClicked() { ui_->widget_page_all->setCurrentIndex(1); }
void MainWindow::slotButtonDataProcessClicked() { ui_->widget_page_all->setCurrentIndex(2); }
void MainWindow::slotButtonFunctionClicked() { ui_->widget_page_all->setCurrentIndex(3); }
void MainWindow::slotButtonParamClicked() { ui_->widget_page_all->setCurrentIndex(4); }
void MainWindow::slotButtonMesSettingClicked() { ui_->widget_page_all->setCurrentIndex(5); }

void MainWindow::slotButtonSelectProcessPcapClicked()
{
  QString file_name = QFileDialog::getOpenFileName(this, "Open File", QDir::currentPath(), "PCAP Files (*.pcap)");
  if (file_name.isEmpty())
  {
    return;
  }

  ui_->lineedit_process_file_path->setText(file_name);
}
void MainWindow::slotButtonZeroPathClicked()
{
  QString file_name = QFileDialog::getOpenFileName(this, "Open File", QDir::currentPath());
  if (file_name.isEmpty())
  {
    return;
  }

  ui_->lineedit_path_zero->setText(file_name);
}
void MainWindow::slotButtonCompareClicked()
{

  QString bit1_path = ui_->lineedit_bit1->text();

  QString bit2_path = ui_->lineedit_bit2->text();

  std::vector<uint8_t> data1;
  QFile file1(bit1_path);
  if (!file1.open(QIODevice::ReadOnly))
  {
    LOG_ERROR("open file failed: {}", bit1_path.toStdString());
    return;
  }
  data1.resize(file1.size());
  file1.read(reinterpret_cast<char*>(data1.data()), file1.size());
  file1.close();
  if (data1.size() != sizeof(mech::CombineBit))
  {
    LOG_ERROR("file size error: {}", bit1_path.toStdString());
    return;
  }
  mech::CombineBit miles_bit;
  std::memcpy(&miles_bit, data1.data(), sizeof(mech::CombineBit));
  miles_bit.toBigEndian();

  std::vector<uint8_t> data2;
  QFile file2(bit2_path);
  if (!file2.open(QIODevice::ReadOnly))
  {
    LOG_ERROR("open file failed: {}", bit2_path.toStdString());
    return;
  }
  data2.resize(file2.size());
  file2.read(reinterpret_cast<char*>(data2.data()), file2.size());
  file2.close();
  if (data2.size() != sizeof(mech::CombineBit))
  {
    LOG_ERROR("file size error: {}", bit2_path.toStdString());
    return;
  }
  mech::CombineBit david_bit {};
  std::memcpy(&david_bit, data2.data(), sizeof(mech::CombineBit));
  david_bit.toBigEndian();
  // david_bit.dynamic_bit = miles_bit.dynamic_bit;

  // write file
  // QString save_path = root_path + "data/data/30/result/1109bddc0030_miles_dynamic_combine_calib_20241101_192338.bit";
  // if (!getWorkModel()->saveFile(save_path, david_bit.arr.data(), sizeof(david_bit)))
  // {
  //   LOG_ERROR("save file failed: {}", save_path.toStdString());
  //   return;
  // }

  // 对比动标
  for (size_t i = 0; i < miles_bit.dynamic_bit.comp.size(); i++)
  {
    plt::clf();
    int channel_num      = static_cast<int>(i) + 1;
    auto& comp1          = miles_bit.dynamic_bit.comp.at(i);
    auto& comp2          = david_bit.dynamic_bit.comp.at(i);
    auto dynamic_calib_x = mech::getDynamicCalibAreaArray();
    std::vector<int> dynamic_x_vec(dynamic_calib_x.begin(), dynamic_calib_x.end());
    std::vector<int> dynamic_y_vec1(comp1.dist_val_arr.size());
    std::vector<int> dynamic_y_vec2(comp2.dist_val_arr.size());

    std::transform(comp1.dist_val_arr.begin(), comp1.dist_val_arr.end(), dynamic_y_vec1.begin(),
                   [](auto& _val) { return _val; });
    std::transform(comp2.dist_val_arr.begin(), comp2.dist_val_arr.end(), dynamic_y_vec2.begin(),
                   [](auto& _val) { return _val; });
    plt::scatter(dynamic_x_vec, dynamic_y_vec1, 1, { { "label", "miles" } });
    plt::scatter(dynamic_x_vec, dynamic_y_vec2, 1, { { "label", "david" } });
    plt::xlabel("area");
    plt::ylabel("dist");
    plt::legend();
    plt::title(fmt::format("chn {} dynamic", channel_num));
    // plt::show();
    plt::save(fmt::format("dynamic_chn_{}.png", channel_num));
  }

  // 对比静标
  std::vector<int> static_x_vec(96);
  std::iota(static_x_vec.begin(), static_x_vec.end(), 1);

  std::vector<int> static_y_diff(96);

  for (size_t i = 0; i < miles_bit.static_bit.comp.size(); i++)
  {
    auto& comp1         = miles_bit.static_bit.comp.at(i);
    auto& comp2         = david_bit.static_bit.comp.at(i);
    static_y_diff.at(i) = comp1.code2_dist - comp2.code2_dist;
  }
  // plt::scatter(static_x_vec, static_y_vec1, 1, { { "label", "static1" } });
  // plt::scatter(static_x_vec, static_y_vec2, 1, { { "label", "static2" } });
  plt::plot(static_x_vec, static_y_diff, { { "label", "static_diff" } });
  plt::xlabel("channel");
  plt::ylabel("dist");
  plt::legend();
  plt::title("static");
  plt::show();

  // 对比反标， 幅值反标
  std::vector<float> refl_x_vec(mech::REFL_CALIB_DIST.begin(), mech::REFL_CALIB_DIST.end());
  for (auto& dist : refl_x_vec)
  {
    dist = dist * 5 / 1000;
  }

  std::vector<int> refl_vec = { 10, 40, 90, 255 };

  // for (auto channel_num : mech::getChnArray())
  // {
  //   plt::subplot(1, 2, 1);
  //   auto& comp1 = miles_bit.refl_bit.comp_chn.at(channel_num - 1);
  //   auto& comp2 = david_bit.refl_bit.comp_chn.at(channel_num - 1);
  //   for (auto refl : refl_vec)
  //   {
  //     std::vector<float> area_y_vec1(comp1.charge.at(0).dist_comp.size());
  //     std::vector<float> area_y_vec2(comp2.charge.at(0).dist_comp.size());
  //     for (size_t i = 0; i < refl_x_vec.size(); ++i)
  //     {
  //       area_y_vec1.at(i) = comp1.charge.at(0).dist_comp.at(i).getReflChargeComp(refl);
  //       area_y_vec2.at(i) = comp2.charge.at(0).dist_comp.at(i).getReflChargeComp(refl);
  //     }
  //     plt::plot(refl_x_vec, area_y_vec1, { { "label", "area1" } });
  //     plt::plot(refl_x_vec, area_y_vec2, { { "label", "area2" } });
  //   }
  //   plt::xlabel("dist");
  //   plt::ylabel("area");
  //   plt::legend();
  //   plt::title(fmt::format("refl area chn {}", channel_num));

  //   plt::subplot(1, 2, 2);
  //   for (auto refl : refl_vec)
  //   {
  //     std::vector<float> amp_y_vec1(comp1.charge.at(0).dist_comp.size());
  //     std::vector<float> amp_y_vec2(comp2.charge.at(0).dist_comp.size());
  //     for (size_t i = 0; i < refl_x_vec.size(); ++i)
  //     {
  //       amp_y_vec1.at(i) = comp1.charge.at(1).dist_comp.at(i).getReflChargeComp(refl);
  //       amp_y_vec2.at(i) = comp2.charge.at(1).dist_comp.at(i).getReflChargeComp(refl);
  //     }
  //     plt::plot(refl_x_vec, amp_y_vec1, { { "label", "amp1" } });
  //     plt::plot(refl_x_vec, amp_y_vec2, { { "label", "amp2" } });
  //   }
  //   plt::xlabel("dist");
  //   plt::ylabel("amp");
  //   plt::legend();
  //   plt::title(fmt::format("refl amp chn {}", channel_num));
  //   plt::show();
  // }
}

void MainWindow::slotFsmStarting(const int _index)
{
  ui_->button_one_key_run->setEnabled(false);
  ui_->button_one_key_run->setText("运行中...");
}
void MainWindow::slotFsmStarted(const int _index)
{
  ui_->button_one_key_run->setEnabled(true);
  ui_->button_one_key_run->setText("停止运行");
}
void MainWindow::slotFsmStopping(const int _index)
{
  ui_->button_one_key_run->setEnabled(false);
  ui_->button_one_key_run->setText("停止中");
}
void MainWindow::slotFsmStopped(const int _index)
{
  ui_->button_one_key_run->setEnabled(true);
  ui_->button_one_key_run->setText("一键运行");
}

void MainWindow::slotUpdateLabelRotatorXAngle(const float _angle)
{
  ui_->label_rotator_x_angle->setText(fmt::format("转台: {:.2f}°", _angle).c_str());
}

void MainWindow::slotUpdateZeroAngle(const float _angle, const bool _test)
{
  _test ? ui_->lineedit_test_slope->setText(fmt::format("{:.2f}°", _angle).c_str())
        : ui_->lineedit_cur_zero->setText(fmt::format("{:.2f}°", _angle).c_str());
}

void MainWindow::showNonBlockingMessage(const QString& _msg, MessageType _type)
{
  QMessageBox* msg_box = new QMessageBox(this);  // 动态分配，避免阻塞
  msg_box->setAttribute(Qt::WA_DeleteOnClose);   // 窗口关闭后自动释放内存

  // 根据类型设置图标和标题
  switch (_type)
  {
  case MessageType::INFO:
    msg_box->setIcon(QMessageBox::Information);
    msg_box->setWindowTitle(tr("信息"));
    break;
  case MessageType::WARNING:
    msg_box->setIcon(QMessageBox::Warning);
    msg_box->setWindowTitle(tr("警告"));
    break;
  case MessageType::ERROR:
    msg_box->setIcon(QMessageBox::Critical);
    msg_box->setWindowTitle(tr("错误"));
    break;
  }

  msg_box->setText(_msg);
  msg_box->setStandardButtons(QMessageBox::Ok | QMessageBox::Cancel);

  connect(msg_box, &QMessageBox::finished, msg_box, &QMessageBox::deleteLater);

  msg_box->show();  // 非阻塞显示
}
void MainWindow::slotButtonSelectBitFileClicked()
{
  QString file_name = QFileDialog::getOpenFileName(this, "Open File", QDir::currentPath(), "Bit Files (*.bit)");
  if (file_name.isEmpty())
  {
    return;
  }

  ui_->lineedit_bit_file_path->setText(file_name);
}
void MainWindow::slotButtonWriteReflClicked()
{
  QString file_name = ui_->lineedit_bit_file_path->text();
  if (file_name.isEmpty())
  {
    return;
  }
  if (!model_map_[1]->getLidarManager()->writeReflBit(file_name))
  {
    LOG_ERROR("写入反射标失败 {}", file_name.toStdString());
  }
  else
  {
    LOG_INFO("写入反射标成功 {}", file_name.toStdString());
  }
}
void MainWindow::slotButtonWriteDynamicClicked()
{
  QString file_name = ui_->lineedit_bit_file_path->text();
  if (file_name.isEmpty())
  {
    return;
  }
  if (!model_map_[1]->getLidarManager()->writeDynamicBit(file_name))
  {
    LOG_ERROR("写入动态标失败 {}", file_name.toStdString());
  }
  else
  {
    LOG_INFO("写入动态标成功 {}", file_name.toStdString());
  }
}
void MainWindow::slotButtonWriteStaticClicked()
{
  QString file_name = ui_->lineedit_bit_file_path->text();
  if (file_name.isEmpty())
  {
    return;
  }
  if (!model_map_[1]->getLidarManager()->writeStaticBit(file_name))
  {
    LOG_ERROR("写入静态标失败 {}", file_name.toStdString());
  }
  else
  {
    LOG_INFO("写入静态标成功 {}", file_name.toStdString());
  }
}
void MainWindow::slotButtonWriteTwoAbsClicked()
{
  QString file_name = ui_->lineedit_bit_file_path->text();
  if (file_name.isEmpty())
  {
    LOG_ERROR("写入失败, 文件路径不能为空");
    return;
  }
  if (!model_map_[1]->getLidarManager()->writeTwoDimBit(file_name))
  {
    LOG_ERROR("写入二维标失败 {}", file_name.toStdString());
  }
  else
  {
    LOG_INFO("写入二维标成功 {}", file_name.toStdString());
  }
}
void MainWindow::slotButtonWriteAbsClicked()
{
  QString file_name = ui_->lineedit_bit_file_path->text();
  if (file_name.isEmpty())
  {
    return;
  }
  if (!model_map_[1]->getLidarManager()->writeAbsBit(file_name))
  {
    LOG_ERROR("写入绝对标失败 {}", file_name.toStdString());
  }
  else
  {
    LOG_INFO("写入绝对标成功 {}", file_name.toStdString());
  }
}
void MainWindow::slotButtonLoadVbdFileClicked()
{
  QString file_name = QFileDialog::getOpenFileName(this, "Open File", QDir::currentPath(), "csv Files (*.csv)");
  if (file_name.isEmpty())
  {
    slotShowErrorText("文件路径不能为空");
    return;
  }

  QFileInfo file_info(file_name);
  QString dir_path = file_info.absoluteDir().absolutePath();

  ui_->lineedit_vbd_file_path->setText(file_name);
  ui_->lineedit_vbd_save_dir->setText(dir_path);
  signalLoadVbdFile();
}

void MainWindow::slotShowInfoText(const QString& _msg) { showNonBlockingMessage(_msg, MessageType::INFO); }
void MainWindow::slotShowWarningText(const QString& _msg) { showNonBlockingMessage(_msg, MessageType::WARNING); }
void MainWindow::slotShowErrorText(const QString& _msg) { showNonBlockingMessage(_msg, MessageType::ERROR); }
void MainWindow::slotShowErrorMsg(const int _error_type) {}
void MainWindow::slotShowInfoVariant(const QString& _name, const QVariant& _value)
{
  QString msg = fmt::format("{}: {}", _name, _value.toString()).c_str();
  showNonBlockingMessage(msg, MessageType::INFO);
}

void MainWindow::slotMsgBoxConfirm(const QString& _msg, robosense::lidar::MsgResult* _msg_result)
{
  QMessageBox msg_box(this);
  msg_box.setText(_msg);
  msg_box.setStandardButtons(QMessageBox::Ok | QMessageBox::Cancel);
  int ret = msg_box.exec();

  std::lock_guard<std::mutex> lock(_msg_result->mutex);
  _msg_result->ret = ret == QMessageBox::Ok;
  _msg_result->cv.notify_one();
}

void MainWindow::slotMsgBoxConfirmSpotAngle(robosense::lidar::MsgResult* _msg_result)
{
  // 创建一个 QDialog 实例
  QDialog dialog(this);
  dialog.setWindowTitle("角度确认");
  dialog.setModal(true);  // 设置为模态对话框

  // 创建主布局
  QVBoxLayout* main_layout = new QVBoxLayout(&dialog);

  // 添加信息标签
  QLabel* info_label = new QLabel("请确认角度并操作：", &dialog);
  main_layout->addWidget(info_label);

  // 创建一个水平布局，用于放置 QDoubleSpinBox 和 "停止电机" 按钮
  QHBoxLayout* control_layout = new QHBoxLayout();

  // 创建 QDoubleSpinBox
  QDoubleSpinBox* spin_box = new QDoubleSpinBox(&dialog);
  spin_box->setRange(-360, 360);
  spin_box->setDecimals(2);
  spin_box->setValue(ui_->spinbox_stop_motor_to_angle->value());
  spin_box->setSuffix("°");
  spin_box->setSingleStep(0.1);

  // 创建 "停止电机" 按钮
  QPushButton* stop_motor_button = new QPushButton("停止电机", &dialog);

  // 将 QDoubleSpinBox 和 "停止电机" 按钮添加到水平布局
  control_layout->addWidget(spin_box);
  control_layout->addWidget(stop_motor_button);

  // 将水平布局添加到主布局
  main_layout->addLayout(control_layout);

  // 创建 QDialogButtonBox，用于 "OK" 和 "Cancel" 按钮
  QDialogButtonBox* button_box = new QDialogButtonBox(QDialogButtonBox::Ok | QDialogButtonBox::Cancel, &dialog);

  // 将 QDialogButtonBox 添加到主布局
  main_layout->addWidget(button_box);

  // 连接 "OK" 和 "Cancel" 按钮的信号到 QDialog 的槽
  connect(button_box, &QDialogButtonBox::accepted, &dialog, &QDialog::accept);
  connect(button_box, &QDialogButtonBox::rejected, &dialog, &QDialog::reject);

  // 连接 "停止电机" 按钮的信号到 MainWindow 的槽
  connect(stop_motor_button, &QPushButton::clicked, this, &MainWindow::signalStopMotor);

  // 连接 QDoubleSpinBox 的 valueChanged 信号到 UI 中的 spinbox_stop_motor_to_angle 的 setValue 槽
  connect(spin_box, QOverload<double>::of(&QDoubleSpinBox::valueChanged), ui_->spinbox_stop_motor_to_angle,
          &QDoubleSpinBox::setValue);

  // 执行对话框并等待用户操作
  int ret = dialog.exec();

  // 根据用户的选择处理结果
  {
    std::lock_guard<std::mutex> lock(_msg_result->mutex);
    _msg_result->ret = (ret == QDialog::Accepted);
  }
  _msg_result->cv.notify_one();

  // 可选：根据用户选择执行进一步操作
  if (ret != QDialog::Accepted)
  {
    QMessageBox::information(this, "操作取消", "已取消操作。");
  }
}

void MainWindow::slotLidarConnecting(const int /*_lidar_index*/)
{
  // 遍历groupbox_lidar_op下的所有控件，设置为不可用
  // for (auto* widget : ui_->groupbox_lidar_op->findChildren<QWidget*>())
  // {
  //   widget->setEnabled(false);
  // }
}
void MainWindow::slotLidarConnected(const int /*_lidar_index*/)
{
  // 遍历groupbox_lidar_op下的所有控件，设置为可用
  for (auto* widget : ui_->groupbox_lidar_op->findChildren<QWidget*>())
  {
    widget->setEnabled(true);
  }
}
void MainWindow::slotLidarDisconnecting(const int /*_lidar_index*/)
{
  // for (auto* widget : ui_->groupbox_lidar_op->findChildren<QWidget*>())
  // {
  //   widget->setEnabled(false);
  // }
}
void MainWindow::slotLidarDisconnected(const int /*_lidar_index*/)
{
  // for (auto* widget : ui_->groupbox_lidar_op->findChildren<QWidget*>())
  // {
  //   widget->setEnabled(false);
  // }
}
