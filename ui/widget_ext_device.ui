﻿<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>WidgetExtDevice</class>
 <widget class="QWidget" name="WidgetExtDevice">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>930</width>
    <height>478</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <widget class="QPushButton" name="button_rotator_refresh_port">
       <property name="text">
        <string>刷新串口名</string>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer_2">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QLabel" name="label">
       <property name="text">
        <string>控制器类型:</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QComboBox" name="combobox_rotator_controller_type"/>
     </item>
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QLabel" name="label_2">
       <property name="text">
        <string>转台类型:</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QComboBox" name="combobox_rotator_type"/>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QGroupBox" name="groupBox">
     <property name="title">
      <string>转台X</string>
     </property>
     <layout class="QGridLayout" name="gridLayout">
      <item row="0" column="0">
       <layout class="QGridLayout" name="gridLayout_5">
        <item row="1" column="1">
         <widget class="QSpinBox" name="spinbox_rotator_x_speed">
          <property name="maximum">
           <number>50000</number>
          </property>
          <property name="value">
           <number>3000</number>
          </property>
         </widget>
        </item>
        <item row="0" column="2">
         <widget class="QPushButton" name="button_rotator_x_connect">
          <property name="text">
           <string>连接</string>
          </property>
         </widget>
        </item>
        <item row="1" column="2">
         <widget class="QPushButton" name="button_rotator_x_speed">
          <property name="text">
           <string>设置</string>
          </property>
         </widget>
        </item>
        <item row="0" column="5">
         <widget class="QDoubleSpinBox" name="spinbox_rotator_x_angle">
          <property name="suffix">
           <string notr="true">°</string>
          </property>
          <property name="minimum">
           <double>-360.000000000000000</double>
          </property>
          <property name="maximum">
           <double>1080.000000000000000</double>
          </property>
         </widget>
        </item>
        <item row="1" column="3">
         <spacer name="horizontalSpacer_10">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeType">
           <enum>QSizePolicy::Fixed</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item row="0" column="1">
         <widget class="QComboBox" name="combo_rotator_x_name"/>
        </item>
        <item row="0" column="6">
         <widget class="QPushButton" name="button_rotator_x_angle">
          <property name="text">
           <string>设置</string>
          </property>
         </widget>
        </item>
        <item row="0" column="0">
         <widget class="QLabel" name="label_13">
          <property name="text">
           <string>转台yaw串口X:</string>
          </property>
         </widget>
        </item>
        <item row="1" column="4">
         <widget class="QPushButton" name="button_rotator_x_reset_zero">
          <property name="text">
           <string>转台回零</string>
          </property>
         </widget>
        </item>
        <item row="0" column="4">
         <widget class="QLabel" name="label_11">
          <property name="text">
           <string>转台角度</string>
          </property>
         </widget>
        </item>
        <item row="1" column="0">
         <widget class="QLabel" name="label_12">
          <property name="text">
           <string>转速:</string>
          </property>
         </widget>
        </item>
        <item row="0" column="3">
         <spacer name="horizontalSpacer_9">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeType">
           <enum>QSizePolicy::Fixed</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="groupBox_2">
     <property name="title">
      <string>转台Y</string>
     </property>
     <layout class="QGridLayout" name="gridLayout_2">
      <item row="0" column="0">
       <layout class="QGridLayout" name="gridLayout_4">
        <item row="0" column="6">
         <widget class="QPushButton" name="button_rotator_y_angle">
          <property name="text">
           <string>设置</string>
          </property>
         </widget>
        </item>
        <item row="0" column="1">
         <widget class="QComboBox" name="combo_rotator_y_name"/>
        </item>
        <item row="0" column="4">
         <widget class="QLabel" name="label_9">
          <property name="text">
           <string>转台角度</string>
          </property>
         </widget>
        </item>
        <item row="1" column="2">
         <widget class="QPushButton" name="button_rotator_y_speed">
          <property name="text">
           <string>设置</string>
          </property>
         </widget>
        </item>
        <item row="0" column="2">
         <widget class="QPushButton" name="button_rotator_y_connect">
          <property name="text">
           <string>连接</string>
          </property>
         </widget>
        </item>
        <item row="0" column="5">
         <widget class="QDoubleSpinBox" name="spinbox_rotator_y_angle">
          <property name="suffix">
           <string notr="true">°</string>
          </property>
          <property name="minimum">
           <double>-360.000000000000000</double>
          </property>
          <property name="maximum">
           <double>1080.000000000000000</double>
          </property>
         </widget>
        </item>
        <item row="1" column="3">
         <spacer name="horizontalSpacer_8">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeType">
           <enum>QSizePolicy::Fixed</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item row="0" column="3">
         <spacer name="horizontalSpacer_7">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeType">
           <enum>QSizePolicy::Fixed</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item row="1" column="1">
         <widget class="QSpinBox" name="spinbox_rotator_y_speed">
          <property name="maximum">
           <number>50000</number>
          </property>
          <property name="value">
           <number>3000</number>
          </property>
         </widget>
        </item>
        <item row="1" column="0">
         <widget class="QLabel" name="label_10">
          <property name="text">
           <string>转速:</string>
          </property>
         </widget>
        </item>
        <item row="0" column="0">
         <widget class="QLabel" name="label_4">
          <property name="text">
           <string>转台pitch串口Y:</string>
          </property>
         </widget>
        </item>
        <item row="1" column="4">
         <widget class="QPushButton" name="button_rotator_y_reset_zero">
          <property name="text">
           <string>转台回零</string>
          </property>
         </widget>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="groupBox_4">
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>70</height>
      </size>
     </property>
     <property name="title">
      <string>继电器</string>
     </property>
     <layout class="QGridLayout" name="gridLayout_6">
      <item row="0" column="4">
       <widget class="QSpinBox" name="spinbox_relay_chn">
        <property name="minimum">
         <number>1</number>
        </property>
        <property name="maximum">
         <number>8</number>
        </property>
       </widget>
      </item>
      <item row="0" column="5">
       <widget class="QPushButton" name="button_turn_on_relay">
        <property name="text">
         <string>上电</string>
        </property>
       </widget>
      </item>
      <item row="0" column="2">
       <widget class="QPushButton" name="button_relay_connect">
        <property name="text">
         <string>连接</string>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <widget class="QComboBox" name="combobox_relay_port_name"/>
      </item>
      <item row="0" column="7">
       <spacer name="horizontalSpacer_3">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item row="0" column="0">
       <widget class="QLabel" name="label_3">
        <property name="text">
         <string>继电器串口:</string>
        </property>
       </widget>
      </item>
      <item row="0" column="3">
       <widget class="QLabel" name="label_5">
        <property name="text">
         <string>通道: </string>
        </property>
       </widget>
      </item>
      <item row="0" column="6">
       <widget class="QPushButton" name="button_turn_off_relay">
        <property name="text">
         <string>下电</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
