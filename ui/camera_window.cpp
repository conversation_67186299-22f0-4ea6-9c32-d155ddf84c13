﻿/******************************************************************************
 * Copyright 2024 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai
 *
 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONS<PERSON>QUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "camera_window.h"
#include <QCameraInfo>
#include <QImageReader>
#include <QMessageBox>
#include <QSizePolicy>
#include <QVBoxLayout>

// NOLINTNEXTLINE(modernize-concat-nested-namespaces)
namespace robosense
{
namespace lidar
{

CameraWindow::CameraWindow(QWidget* _parent) :
  QWidget(_parent),
  camera_device_(nullptr),
  image_capture_(nullptr),
  image_label_(new QLabel(this)),
  camera_button_(new QPushButton("打开相机", this)),
  capture_timer_(new QTimer(this)),
  is_camera_active_(false)
{
  auto* layout = new QVBoxLayout(this);

  // 配置图像标签
  image_label_->setAlignment(Qt::AlignCenter);
  image_label_->setMinimumHeight(240);

  // 设置尺寸策略为扩展，允许标签随窗口大小变化
  QSizePolicy size_policy(QSizePolicy::Expanding, QSizePolicy::Expanding);
  image_label_->setSizePolicy(size_policy);

  image_label_->setText("相机未打开");
  image_label_->setStyleSheet("QLabel { background-color : #f0f0f0; }");

  // 连接按钮信号到槽
  connect(camera_button_, &QPushButton::clicked, this, &CameraWindow::toggleCamera);

  // 设置图像捕获定时器
  connect(capture_timer_, &QTimer::timeout, this, &CameraWindow::captureImage);

  // 添加组件到布局
  layout->addWidget(image_label_);
  layout->addWidget(camera_button_);

  setLayout(layout);
}

void CameraWindow::toggleCamera()
{
  if (!is_camera_active_)
  {
    // 获取可用相机列表
    QList<QCameraInfo> available_cameras = QCameraInfo::availableCameras();

    if (available_cameras.isEmpty())
    {
      camera_button_->setText("未检测到相机设备");
      return;
    }

    // 创建并初始化相机设备
    camera_device_ = std::make_unique<QCamera>(available_cameras.first());

    // 连接相机状态和错误信号
    connect(camera_device_.get(), &QCamera::stateChanged, this, &CameraWindow::handleCameraStateChange);
    connect(camera_device_.get(), QOverload<QCamera::Error>::of(&QCamera::error), this,
            &CameraWindow::handleCameraError);

    // 创建图像捕获对象
    image_capture_ = std::make_unique<QCameraImageCapture>(camera_device_.get());

    // 检查相机是否支持图像捕获
    if (!image_capture_->isAvailable())
    {
      camera_button_->setText("此相机不支持图像捕获");
      camera_device_.reset();
      image_capture_.reset();
      return;
    }

    // 设置图像捕获格式
    QImageEncoderSettings image_settings;
    image_settings.setCodec("image/jpeg");
    image_settings.setResolution(640, 480);
    image_capture_->setEncodingSettings(image_settings);

    // 连接图像捕获信号到槽函数
    connect(image_capture_.get(), &QCameraImageCapture::imageCaptured, this, &CameraWindow::processImage);
    connect(image_capture_.get(),
            QOverload<int, QCameraImageCapture::Error, const QString&>::of(&QCameraImageCapture::error),
            [this](int, QCameraImageCapture::Error, const QString& _error_string) {
              image_label_->setText("图像捕获失败: " + _error_string);
            });

    // 加载相机并启动捕获
    camera_device_->load();
    camera_device_->start();

    // 确保相机完全启动后再开始捕获
    QTimer::singleShot(500, [this]() {
      if (camera_device_ && camera_device_->state() == QCamera::ActiveState)
      {
        capture_timer_->start(100);  // 每100毫秒捕获一帧
        is_camera_active_ = true;
        camera_button_->setText("关闭相机");
      }
    });
  }
  else
  {
    // 关闭相机
    if (capture_timer_ != nullptr)
    {
      capture_timer_->stop();
    }

    if (camera_device_)
    {
      camera_device_->stop();
      camera_device_.reset();
    }

    image_capture_.reset();
    is_camera_active_ = false;
    camera_button_->setText("打开相机");
    image_label_->setText("相机已关闭");
  }
}

void CameraWindow::captureImage()
{
  if (is_camera_active_ && camera_device_ && image_capture_ && camera_device_->state() == QCamera::ActiveState)
  {
    image_capture_->capture();
  }
}

void CameraWindow::processImage(int _request_id, const QImage& _image)
{
  Q_UNUSED(_request_id);

  if (!_image.isNull())
  {
    // 保存原始图像，而不是每次都基于当前标签尺寸缩放
    if (original_image_.isNull() || original_image_.size() != _image.size())
    {
      original_image_ = _image;
      // 触发一次调整大小事件，立即适应当前窗口
      adjustImageSize();
    }
    else
    {
      original_image_     = _image;
      QSize current_size  = image_label_->size();
      QImage scaled_image = original_image_.scaled(current_size, Qt::KeepAspectRatio, Qt::SmoothTransformation);
      image_label_->setPixmap(QPixmap::fromImage(scaled_image));
    }
  }
}

// 添加一个新的方法来调整图像大小
void CameraWindow::adjustImageSize()
{
  if (!original_image_.isNull())
  {
    QSize current_size  = image_label_->size();
    QImage scaled_image = original_image_.scaled(current_size, Qt::KeepAspectRatio, Qt::SmoothTransformation);
    image_label_->setPixmap(QPixmap::fromImage(scaled_image));
  }
}

// 重写调整大小事件，当窗口大小改变时调整图像
void CameraWindow::resizeEvent(QResizeEvent* _event)
{
  QWidget::resizeEvent(_event);
  adjustImageSize();
}

void CameraWindow::handleCameraStateChange(QCamera::State _state)
{
  switch (_state)
  {
  case QCamera::ActiveState:
    // 相机成功激活
    break;
  case QCamera::UnloadedState:
  case QCamera::LoadedState:
    // 相机还未完全激活或已卸载
    break;
  }
}

void CameraWindow::handleCameraError(QCamera::Error _error)
{
  QString error_message;

  switch (_error)
  {
  case QCamera::NoError: return;
  case QCamera::CameraError: error_message = "相机一般错误"; break;
  case QCamera::InvalidRequestError: error_message = "相机请求无效"; break;
  case QCamera::ServiceMissingError: error_message = "相机服务缺失"; break;
  case QCamera::NotSupportedFeatureError: error_message = "不支持的相机功能"; break;
  default: error_message = "未知相机错误"; break;
  }

  image_label_->setText(error_message);
  is_camera_active_ = false;
  camera_button_->setText("打开相机");

  if (capture_timer_ != nullptr)
  {
    capture_timer_->stop();
  }
}

CameraWindow::~CameraWindow()
{
  // 确保在析构前停止所有操作
  if (capture_timer_ != nullptr)
  {
    capture_timer_->stop();
  }

  if (camera_device_)
  {
    camera_device_->stop();
  }

  // 智能指针会自动释放资源
}

}  // namespace lidar
}  // namespace robosense