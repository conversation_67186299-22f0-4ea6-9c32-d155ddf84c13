﻿/******************************************************************************
 * Copyright 2024 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai
 *
 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONS<PERSON>QUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#ifndef WIDGET_EXT_DEVICE_H
#define WIDGET_EXT_DEVICE_H

#include "relay_controller_driver/include/relay_controller_driver/relay_controller_interface.h"
#include "rotator_driver/include/rotator_controller_factory.h"
#include "rotator_driver/include/rotator_controller_interface.h"
#include <QEventLoop>
#include <QMetaObject>
#include <QThread>
#include <QVariant>
#include <QWidget>
#include <memory>
#include <mutex>

using namespace robosense::lidar;  // NOLINT

namespace Ui
{
class WidgetExtDevice;
}  // namespace Ui

struct OperationContext
{
public:
  QEventLoop event_loop;
  std::mutex mutex;
  std::unique_lock<std::mutex> lock { mutex };
  bool success = false;  // 操作结果是否成功
  QVariant result;       // 操作结果

  OperationContext()
  {
    QMetaObject::invokeMethod(&event_loop, [&] { lock.unlock(); });
  }

  // 参考条件变量的wait，结束等待的时候需要重新获取回锁，避免发生竞态问题
  bool wait()
  {
    event_loop.exec();
    lock.lock();
    return success;
  }
};

// 在析构的时候自动notify，并解锁，使用与lock_guard一样的机制来保证在析构的时候自动解锁，自动notify
class ContextGuard
{
public:
  ContextGuard(const ContextGuard&) = delete;
  ContextGuard(ContextGuard&&)      = delete;
  ContextGuard& operator=(const ContextGuard&) = delete;
  ContextGuard& operator=(ContextGuard&&) = delete;
  explicit ContextGuard(OperationContext* _context) : context_ptr_(_context), lock_(_context->mutex) {}
  ~ContextGuard() { context_ptr_->event_loop.quit(); }

private:
  OperationContext* context_ptr_;
  std::unique_lock<std::mutex> lock_;
};

class ExtDeviceWorker : public QObject
{
  Q_OBJECT
public:
  explicit ExtDeviceWorker(QObject* _parent = nullptr);
  explicit ExtDeviceWorker(ExtDeviceWorker&&)      = delete;
  explicit ExtDeviceWorker(const ExtDeviceWorker&) = delete;
  ExtDeviceWorker& operator=(ExtDeviceWorker&&) = delete;
  ExtDeviceWorker& operator=(const ExtDeviceWorker&) = delete;
  ~ExtDeviceWorker() override;

  void setRotatorPtr(std::shared_ptr<RotatorControllerInterface> _rotator_ptr);
  std::shared_ptr<RotatorControllerInterface> getRotatorPtr();

  std::shared_ptr<RotatorControllerInterface> connectRotator(
    const QString& _port_name,
    const RotatorControllerFactory::RotatorControllerType& _rotator_ctrl_type =
      RotatorControllerFactory::RotatorControllerType::SC101,
    const std::string& _rotator_motor_type = "MRS102");

  bool isRotatorXConnected();
  bool isRotatorYConnected();

Q_SIGNALS:
  void signalUpdateRotatorXAngle(const float _ange);

public Q_SLOTS:
  void slotSetRotatorXAngle(const float _angle, OperationContext* _context);
  void slotSetRotatorYAngle(const float _angle, OperationContext* _context);
  void slotSetRotatorXSpeed(const int _speed, OperationContext* _context);
  void slotSetRotatorYSpeed(const int _speed, OperationContext* _context);
  void slotIsRotatorXMoving(OperationContext* _context);
  void slotGetRotatorXAngle(OperationContext* _context);
  void slotConnectRotatorX(const QString& _rotator_name,
                           const QString& _ctrl_type,
                           const QString& _rotator_type,
                           OperationContext* _context);
  void slotDisconnectRotatorX(OperationContext* _context);
  void slotConnectRotatorY(const QString& _rotator_name,
                           const QString& _ctrl_type,
                           const QString& _rotator_type,
                           OperationContext* _context);
  void slotDisconnectRotatorY(OperationContext* _context);
  void slotRotatorXResetZero(OperationContext* _context);
  void slotRotatorYResetZero(OperationContext* _context);

  void slotConnectRelay(const QString& _port_name, OperationContext* _context);
  void slotDisconnectRelay(OperationContext* _context);
  void slotTurnRelay(const int _chn, const bool _is_open, OperationContext* _context);

private:
  std::shared_ptr<RotatorControllerInterface> rotator_x_ptr_ = nullptr;
  std::shared_ptr<RotatorControllerInterface> rotator_y_ptr_ = nullptr;
  std::shared_ptr<RelayControllerInterface> relay_ptr_       = nullptr;
};

class WidgetExtDevice : public QWidget
{
  friend class MainWindow;
  Q_OBJECT

public:
  explicit WidgetExtDevice(QWidget* _parent = nullptr);
  explicit WidgetExtDevice(WidgetExtDevice&&)      = delete;
  explicit WidgetExtDevice(const WidgetExtDevice&) = delete;
  WidgetExtDevice& operator=(WidgetExtDevice&&) = delete;
  WidgetExtDevice& operator=(const WidgetExtDevice&) = delete;
  ~WidgetExtDevice() override;

public:
  void init();

Q_SIGNALS:
  void signalUpdateRotatorXAngle(const float _ange);

  void signalSetRotatorXAngle(const float _angle, OperationContext* _context);
  void signalSetRotatorYAngle(const float _angle, OperationContext* _context);
  void signalSetRotatorXSpeed(const int _speed, OperationContext* _context);
  void signalSetRotatorYSpeed(const int _speed, OperationContext* _context);
  void signalIsRotatorXMoving(OperationContext* _context);
  void signalGetRotatorXAngle(OperationContext* _context);
  void signalConnectRotatorX(const QString& _rotator_name,
                             const QString& _ctrl_type,
                             const QString& _rotator_type,
                             OperationContext* _context);
  void signalDisconnectRotatorX(OperationContext* _context);
  void signalConnectRotatorY(const QString& _rotator_name,
                             const QString& _ctrl_type,
                             const QString& _rotator_type,
                             OperationContext* _context);
  void signalDisconnectRotatorY(OperationContext* _context);
  void signalRotatorXResetZero(OperationContext* _context);
  void signalRotatorYResetZero(OperationContext* _context);

  void signalConnectRelay(const QString& _port_name, OperationContext* _context);
  void signalDisconnectRelay(OperationContext* _context);
  void signalTurnRelay(const int _chn, const bool _is_open, OperationContext* _context);

public Q_SLOTS:
  void slotRefreshRotatorPortName();
  void slotRelayTurnOff();
  void slotRelayTurnOn();

public:
  bool setRotatorXAngle(const float _angle);
  bool setRotatorYAngle(const float _angle);
  bool setRotatorXSpeed(const int _speed);
  bool setRotatorYSpeed(const int _speed);
  bool isRotatorXMoving(bool& _is_moving);
  bool getRotatorXAngle(float& _angle);
  bool connectRotatorX();
  void disconnectRotatorX();
  bool connectRotatorY();
  void disconnectRotatorY();

  bool connectRelay();
  void disconnectRelay();
  bool turnRelay(const int _chn, const bool _is_open);

private:
  bool checkRotatorXConnection();
  bool checkRotatorYConnection();

private:
  Ui::WidgetExtDevice* ext_dev_ui_;
  ExtDeviceWorker* ext_device_worker_ = nullptr;
  QThread* ext_device_worker_thread_  = nullptr;
  void initSignalSlots();
};

#endif  // WIDGET_EXT_DEVICE_H
