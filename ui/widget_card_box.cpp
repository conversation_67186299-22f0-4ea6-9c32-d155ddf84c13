﻿/******************************************************************************
 * Copyright 2024 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai
 *
 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONS<PERSON>QUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "widget_card_box.h"
#include <QGraphicsDropShadowEffect>
#include <QLabel>
#include <QVBoxLayout>

// NOLINTNEXTLINE(modernize-concat-nested-namespaces)
namespace robosense
{
namespace lidar
{

WidgetCardBox::WidgetCardBox(QWidget* _parent) : QFrame(_parent), shadow_effect_(new QGraphicsDropShadowEffect(this))
{
  // 设置阴影效果，四周都有阴影
  shadow_effect_->setBlurRadius(10);
  shadow_effect_->setOffset(0, 0);  // 将偏移设为0，使阴影均匀分布
  shadow_effect_->setColor(QColor(0, 0, 0, 50));
  this->setGraphicsEffect(shadow_effect_);

  // 设置卡片样式
  this->setStyleSheet(R"(
    QFrame {
      background-color: white;
      border-radius: 8px;
      min-height: 50px;
      min-width: 150px;
    }
  )");

  // 创建主布局
  main_layout_ = new QVBoxLayout(this);
  main_layout_->setContentsMargins(6, 6, 6, 6);  // 进一步减小卡片边框的内边距
  main_layout_->setSpacing(0);
  setLayout(main_layout_);

  this->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Preferred);
}

void WidgetCardBox::setContentWidget(QWidget* _widget)
{
  if (!_widget)
  {
    return;
  }

  // 清除现有内容
  QLayoutItem* child;
  while ((child = main_layout_->takeAt(0)) != nullptr)
  {
    delete child->widget();
    delete child;
  }

  // 添加新的内容widget
  main_layout_->addWidget(_widget);
}

void WidgetCardBox::enterEvent(QEvent* _event)
{
  Q_UNUSED(_event);
  // 鼠标进入时，阴影效果变化
  shadow_effect_->setBlurRadius(15);
  shadow_effect_->setOffset(0, 0);                // 保持居中的阴影
  shadow_effect_->setColor(QColor(0, 0, 0, 80));  // 稍微加深阴影

  QFrame::enterEvent(_event);
}

void WidgetCardBox::leaveEvent(QEvent* _event)
{
  Q_UNUSED(_event);
  // 鼠标离开时，恢复默认阴影效果
  shadow_effect_->setBlurRadius(10);
  shadow_effect_->setOffset(0, 0);  // 保持居中的阴影
  shadow_effect_->setColor(QColor(0, 0, 0, 50));

  QFrame::leaveEvent(_event);
}

}  // namespace lidar
}  // namespace robosense