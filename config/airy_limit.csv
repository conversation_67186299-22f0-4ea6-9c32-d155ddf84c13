﻿# 参数名称会直接作为索引读到程序中，请注意不要随意更改,,,,
# 下限、上限不得留空，如果范围不确定或者很广，则写一个很大的数即可。默认为浮点数,,,,
# 上下限采用小于等于、大于等于的形式，即当特性等于上下限时为合格,,,,
# 单位写明方便测试和工程人员查看分析，若没有单位，则统一写 unitless,,,,
# 备注用于说明一些特殊情况，方便其他人查看,,,,
,,,,
参数名称,下限,上限,单位,备注
fsm_ping,1,1,unitless,ping状态
fsm_control_rotator,1,1,unitless,控制转台失败
fsm_connect_lidar,1,1,unitless,连接雷达状态
fsm_reset_motor,1,1,unitless,重置电机状态
fsm_collect_data,1,1,unitless,数据采集状态
fsm_chn_process,1,1,unitless,数据处理状态
fsm_fail,0,0,unitless,失败状态
product_area_type,PASS,PASS,text,积分值类型
fsm_load_pcap,1,1,unitless,加载pcap文件状态
fsm_auto_process,1,1,unitless,自动处理标定数据
fsm_write_calib_data,1,1,unitless,写入融合标定数据
fsm_zero_angle,300,308,unitless,零角度标定
fsm_zero_test_error,-0.5,0.5,unitless,零角度校验误差
fsm_dynamic_left_dist,50,120,unitless,动标低反数据范围
fsm_area_file_generate,1,1,unitless,积分值文件生成
top_firmware_version,0x10040F08,0x10040F08,unitless,顶板固件版本
bot_firmware_version,0x10030600,0x10030600,unitless,底板固件版本
app_firmware_version,0x25041812,0x25041812,unitless,APP固件版本
motor_firmware_version,0x25021414,0x25021415,unitless,电机固件版本
config_version,0xF0000F06,0xF0000F06,unitless,整机配置版本
area_ng_max_num,0,6,unitless,10m90反射率积分值NG数
area_rate_min,0.55,nan,unitless,最小积分值比例
area_ng_not_allow_chn,0,0,unitless,积分值不允许出现的通道判定NG
area,0.7,1.0001,unitless,通道积分值
area_type,A,C,text,积分值分类
area_amp_chn1_8,0,1600,unitless,通道幅值最大值
area_amp_chn9_32,0,1600,unitless,通道幅值最大值
area_amp_chn33_64,0,1600,unitless,通道幅值最大值
area_amp_chn65_88,0,1600,unitless,通道幅值最大值
area_amp_chn89_96,0,1600,unitless,通道幅值最大值
calib_status,0x10,nan,unitless,标定状态
calib_version,0x02,nan,unitless,标定版本
vbd_crc_status,0x1,0x1,unitless,vbd校验位