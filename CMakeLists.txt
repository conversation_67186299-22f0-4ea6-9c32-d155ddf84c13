﻿cmake_minimum_required(VERSION 3.16)
string(TIMESTAMP PROJECT_COMPILE_DATE %Y%m%d)
string(TIMESTAMP PROJECT_COMPILE_TIME %H%M%S)
project(
  airy_wave_signal_calib
  VERSION 3.20.9.${PROJECT_COMPILE_DATE}
  DESCRIPTION "Airy Wave Signal Calibration Project"
  LANGUAGES C CXX)

set(PROJECT_CODE "440")
set(PROJECT_NAME_EN "Airy Signal Wave Calibration")
set(PROJECT_NAME_ZH "Airy一体化标定")

set(USE_QT_SERIALPORT ON)
set(USE_QT_CONCURRENT OFF)
set(USE_QT_CHARTS OFF)

include_directories(${CMAKE_CURRENT_SOURCE_DIR}/lib)

# =========================
# Option
# =========================
option(BUILD_UTEST "build gtest or not" ON)

set(CMAKE_MODULE_PATH ${CMAKE_MODULE_PATH} "${PROJECT_SOURCE_DIR}/cmake")

# =========================
# Set C++ Standard
# =========================
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)
set(FACTORY_LOCATION "HHL")

include(cmake/base.cmake)
include(cmake/compile_flag.cmake)
include(cmake/system_name.cmake)

# set(INSTALL_PREFIX_LIB ${CMAKE_INSTALL_PREFIX}/lib/${PROJECT_NAME}/)
# set(INSTALL_PREFIX_BIN ${CMAKE_INSTALL_PREFIX}/bin/${PROJECT_NAME}/)
set(INSTALL_PREFIX_SHARE ${CMAKE_INSTALL_PREFIX}/share/${PROJECT_NAME}/)
message(STATUS "CMAKE_INSTALL_PREFIX: ${CMAKE_INSTALL_PREFIX}")
set(CMAKE_INSTALL_RPATH ${CMAKE_INSTALL_PREFIX}/lib/${PROJECT_NAME} ${CMAKE_INSTALL_PREFIX}/lib/${PROJECT_NAME}/lib)
set(MATLAB_RUNTIME_PATH /usr/local/MATLAB/MATLAB_Runtime/v99)
set(CMAKE_INSTALL_RPATH ${CMAKE_INSTALL_RPATH} ${MATLAB_RUNTIME_PATH}/runtime/glnxa64)

# 使 RPATH 在安装后仍然有效
# set(CMAKE_INSTALL_RPATH_USE_LINK_PATH TRUE)

# 避免系统路径覆盖自定义 RPATH
# set(CMAKE_SKIP_BUILD_RPATH FALSE)
# set(CMAKE_BUILD_WITH_INSTALL_RPATH FALSE)
# set(CMAKE_INSTALL_RPATH_USE_LINK_PATH TRUE)

# include(cmake/qt5_auto.cmake)

# for static analysis
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)
find_package(Git QUIET)
execute_process(COMMAND ${GIT_EXECUTABLE} config core.hooksPath .githooks
                WORKING_DIRECTORY "${CMAKE_CURRENT_SOURCE_DIR}")
execute_process(
  COMMAND ${GIT_EXECUTABLE} rev-parse --short HEAD
  WORKING_DIRECTORY "${CMAKE_CURRENT_SOURCE_DIR}"
  OUTPUT_VARIABLE GIT_OUTPUT
  OUTPUT_VARIABLE PROJECT_COMPILE_COMMIT
  ERROR_QUIET OUTPUT_STRIP_TRAILING_WHITESPACE)
configure_file("${PROJECT_SOURCE_DIR}/include/config.h.in" "${PROJECT_SOURCE_DIR}/src/config.h")

find_package(Threads REQUIRED)
find_package(
  Qt5
  COMPONENTS Widgets SerialPort Network MultimediaWidgets
  REQUIRED)

# find package PCL
# set(CMAKE_SUPPRESS_DEVELOPER_WARNINGS
#     1
#     CACHE BOOL "Suppress warnings" FORCE)
# if(POLICY CMP0144)
#   cmake_policy(SET CMP0144 OLD)
# endif()
# find_package(PCL CONFIG REQUIRED QUIET)
# set(CMAKE_SUPPRESS_DEVELOPER_WARNINGS
#     0
#     CACHE BOOL "Suppress warnings" FORCE)

set(CMAKE_FIND_LIBRARY_SUFFIXES_TEMP ${CMAKE_FIND_LIBRARY_SUFFIXES})
set(CMAKE_FIND_LIBRARY_SUFFIXES ".a")
find_package(GSL 2.8 REQUIRED)
get_target_property(GSL_LOC GSL::gsl IMPORTED_LOCATION)
get_target_property(GSLCBLAS_LOC GSL::gslcblas IMPORTED_LOCATION)
message(STATUS "GSL path: ${GSL_LOC}")
message(STATUS "GSL gslcblas path: ${GSLCBLAS_LOC}")
set(CMAKE_FIND_LIBRARY_SUFFIXES ${CMAKE_FIND_LIBRARY_SUFFIXES_TEMP})
message(STATUS "GSL version: ${GSL_VERSION}")

find_package(PCAP MODULE REQUIRED)

list(APPEND CMAKE_PREFIX_PATH "${CMAKE_CURRENT_SOURCE_DIR}/lib/matplotlibcpp")
find_package(matplotlib_cpp REQUIRED)

# 设置头文件和源文件
file(GLOB_RECURSE HEADER_FILES CONFIGURE_DEPENDS include/*.h include/*.hpp include/*.cc)
file(GLOB_RECURSE SOURCE_FILES CONFIGURE_DEPENDS src/*.cpp src/*.h src/*.ui)
file(GLOB_RECURSE QT_UI_FILES CONFIGURE_DEPENDS ui/*.cpp ui/*.ui ui/*.h)
set(QT_RESOURCES resource/resource.qrc resource/appicon.rc)
list(REMOVE_ITEM SOURCE_FILES src/main.cpp)
# add_executable(${PROJECT_NAME} ${HEADER_FILES} ${SOURCE_FILES} ${QT_UI_FILES} ${QT_RESOURCES})
add_library(${PROJECT_NAME}_static STATIC ${HEADER_FILES} ${SOURCE_FILES} ${QT_UI_FILES})
add_executable(${PROJECT_NAME} src/main.cpp ${QT_RESOURCES})

set_target_properties(
  ${PROJECT_NAME}
  PROPERTIES AUTOMOC ON
             AUTORCC ON
             AUTOUIC ON)

set_target_properties(
  ${PROJECT_NAME}_static
  PROPERTIES AUTOMOC ON
             AUTORCC ON
             AUTOUIC ON)
target_include_directories(${PROJECT_NAME} SYSTEM PUBLIC "${CMAKE_BINARY_DIR}/${PROJECT_NAME}_autogen/include")
target_include_directories(${PROJECT_NAME}_static SYSTEM
                           PUBLIC "${CMAKE_BINARY_DIR}/${PROJECT_NAME}_static_autogen/include")
# 设置 AUTOUIC 相关属性
set(AUTOUIC_SEARCH_PATHS ${CMAKE_CURRENT_SOURCE_DIR}/ui)
set_target_properties(${PROJECT_NAME}_static PROPERTIES AUTOUIC ON AUTOUIC_SEARCH_PATHS "${AUTOUIC_SEARCH_PATHS}")

target_include_directories(${PROJECT_NAME}_static PUBLIC include src lib ui)

# 添加子目录构建
add_definitions(-DRSFSCLOG_USE_SPDLOG -DRSFSCLOG_USE_QT -DRSFSCLOG_FORMAT_ENUM)
add_definitions(-DRSFSCLOG_SHOW_FUNCNAME)

add_subdirectory(lib/rsfsc_lib)
find_package(RSFSCLog REQUIRED)
add_subdirectory(lib/mech_comm_lib)

add_subdirectory(lib/mech_communication)
add_subdirectory(lib/rotator_driver)
add_subdirectory(lib/relay_controller_driver)
# add_subdirectory(lib/matlab_lib_cpp)

# 链接库和其他设置
target_link_libraries(
  ${PROJECT_NAME}_static
  PUBLIC rsfsc_lib
         rsfsc_utils
         rsfsc_fsm
         mech_comm_func
         mech_third_party
         Threads::Threads
         mech_communication
         rotator_driver_static
         relay_controller_driver_static
         Qt5::Widgets
         Qt5::Network
         Qt5::MultimediaWidgets
         GSL::gsl
         GSL::gslcblas
         pcap
         matplotlib_cpp::matplotlib_cpp
         # matlab_lib_cpp
         ${PYTHON_LIBRARIES})

target_compile_options(
  ${PROJECT_NAME}_static
  PRIVATE -Wall
          -Werror
          -Wno-deprecated-declarations
          -Wno-unused-parameter
          -Wno-unused-variable
          -Wno-format-security
          -Wno-sign-compare
          -Wno-variadic-macros
          -Wno-error=variadic-macros
          -fno-omit-frame-pointer)

target_link_libraries(${PROJECT_NAME} ${PROJECT_NAME}_static)

if(BUILD_UTEST)
  enable_testing()
  add_subdirectory(test)
endif()

include(cmake/cpack.cmake)
