﻿/******************************************************************************
 * Copyright 2020 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#ifndef APP_EVENT_H
#define APP_EVENT_H

#include "data_struct.h"
#include "rsfsc_utils/csv_utils.h"
#include "widget_ext_device.h"
#include "widgets/project_lidar_info.h"
#include <QJsonObject>
#include <QObject>
#include <cstdint>

QT_BEGIN_NAMESPACE
namespace Ui
{
class MainWindow;
}  // namespace Ui
QT_END_NAMESPACE

class MainWindow;

namespace robosense  // NOLINT(modernize-concat-nested-namespaces)
{
namespace lidar
{

using ErrorT = uint64_t;
enum ErrorType : ErrorT
{
  //  error for all projects
  ERROR_TYPE_NO_ERROR,
  ERROR_TYPE_LIDAR_PARAM_PATH_WRONG,
  ERROR_TYPE_PCAP_WRONG,
  ERROR_TYPE_IP_WRONG,
  ERROR_TYPE_DRIVER_ALREADY_START,
  ERROR_TYPE_LIDAR_CONNECT_FAILED,
  ERROR_TYPE_LIDAR_NOT_CONNECT,
  ERROR_TYPE_LIDAR_SN_EMPTY,
  ERROR_TYPE_CHANNEL_SELECT_WRONG,
  //  error specify for your project
  ERROR_TYPE_THERMOSTAT_POWER_ON_FAILED,

};

class AppEvent : public QObject

{
  Q_OBJECT
public:
  static AppEvent* getInstance()
  {
    static AppEvent instance;
    return &instance;
  }

Q_SIGNALS:

  /****************************************************************
   * RULES:
   * 1. use "update" but not "change"          
  ****************************************************************/
  //  signal that all ui should connect
  void signalUpdateNameSpace(const QString&);
  void signalSaveParam();
  void signalSaveParamFinished();
  void signalUpdateProductDisplay(const QString&);
  void signalUpdateMowAreaType(const QString&);

  //  signal that all ui can connect
  void signalShowInfoText(const QString& _msg);
  void signalShowWarningText(const QString& _msg);
  void signalShowErrorText(const QString& _msg);
  void signalShowErrorMsg(ErrorT _error_type);
  void signalShowInfoVariant(const QString& _name, const QVariant& _value);

  void signalMsgBoxConfirm(const QString& _msg, MsgResult* _msg_result);

  void signalMsgBoxConfirmSpotAngle(MsgResult* _msg_result);

  // draw
  void signalDrawFigureDouble(const std::vector<double>& _x_vec,
                              const std::vector<double>& _y_vec,
                              MsgResult* _msg_result);
  void signalDrawFigureFloat(const std::vector<float>& _x_vec,
                             const std::vector<float>& _y_vec,
                             MsgResult* _msg_result);
  void signalDrawFigureInt(const std::vector<int>& _x_vec, const std::vector<int>& _y_vec, MsgResult* _msg_result);

  //  signal that only for specify ui
  void signalUpdateProgress(const float _current_progress);

  // lidar info
  void signalLidarConnecting(const int _lidar_index);
  void signalLidarConnected(const int _lidar_index);
  void signalLidarDisconnected(const int _lidar_index);
  void signalLidarDisconnecting(const int _lidar_index);

  void signalUpdateProgressWriteTopFlash(const int _current_progress);

  void signalRelayTurnOff();
  void signalRelayTurnOn();

  // machine state
  void signalFsmStarting(const int _index);
  void signalFsmStarted(const int _index);
  void signalFsmStopping(const int _index);
  void signalFsmStopped(const int _index);

  // handler
  void signalUpdateProgressCollectTotalTask(const int _total);
  void signalUpdateProgressCollect(const qint32 _progress);
  void signalUpdateProgressSaveData(const qint32 _progress);

  // data process
  void signalUpdateProgressProcessTotalTask(const int _total);
  void signalUpdateProgressProcessData(const int _progress);
  void signalUpdateProgressSaveProcessDataTotalTask(const int _total);
  void signalUpdateProgressSaveProcessData(const int _progress);
  void signalUpdateProgressGenerateFigureTotalTask(const int _total);
  void signalUpdateProgressGenerateFigure(const int _progress);

  void signalUpdateProcessDataPath(const QString& _path);

  void signalTest();
  void signalUpdateZeroAngle(const float _angel, const bool _test = false);

  void signalUpdateTestState(const TestState _ts);

public:
  explicit AppEvent(QObject* /*_parent*/ = nullptr) {};
  AppEvent(AppEvent&&) = delete;
  AppEvent& operator=(AppEvent&&) = delete;
  AppEvent(const AppEvent&)       = delete;
  AppEvent& operator=(const AppEvent&) = delete;
  ~AppEvent() noexcept override        = default;

  void setWidgetExtDevice(WidgetExtDevice* _widget_ext_device) { widget_ext_device_ = _widget_ext_device; }
  WidgetExtDevice* getWidgetExtDevice() { return widget_ext_device_; }

  void setMainWindow(MainWindow* _ui) { main_window_ = _ui; }
  MainWindow* getManWindow() { return main_window_; }

  void setWidgetLogSetting(WidgetLogSetting* _widget) { widget_log_setting_ = _widget; }
  WidgetLogSetting* getWidgetLogSetting() { return widget_log_setting_; }

  std::shared_ptr<CsvUtils> getCsvUtils(const QString& _project_str)
  {
    if (!csv_parser_hash_.contains(_project_str))
    {
      return nullptr;
    }
    return csv_parser_hash_.value(_project_str);
  }

  void setCsvUtils(const QString& _project_str, std::shared_ptr<CsvUtils> _csv_parser)
  {
    csv_parser_hash_[_project_str] = std::move(_csv_parser);
  }

  QString getFactoryLoc();
  QString getLineNum();
  QString getEthName();

  QString getScriptPath() { return script_path_; }
  void setScriptPath(const QString& _script_path) { script_path_ = _script_path; }

  QString getConfigPath() { return config_path_; }
  void setConfigPath(const QString& _config_path) { config_path_ = _config_path; }

  QJsonObject getJsonBoardInfo(const std::string& _key)
  {
    if (json_board_info_map_.find(_key) == json_board_info_map_.end())
    {
      return {};
    }
    return json_board_info_map_[_key];
  }
  void setJsonBoardInfo(const std::string& _key, const QJsonObject& _json_board_info)
  {
    json_board_info_map_[_key] = _json_board_info;
  }

  bool loadVbdFile(const QString& _vbd_file_path);
  bool loadVbdBySn(const QString& _sn, double& _vbd);
  bool isVbdFileLoaded() { return !vbd_map_.empty(); }

private:
  std::unordered_map<std::string, double> vbd_map_;
  std::unordered_map<std::string, QJsonObject> json_board_info_map_;
  WidgetExtDevice* widget_ext_device_ = nullptr;
  MainWindow* main_window_            = nullptr;
  QHash<QString, std::shared_ptr<CsvUtils>> csv_parser_hash_;
  WidgetLogSetting* widget_log_setting_ = nullptr;
  QString script_path_;
  QString config_path_;
};

inline AppEvent* app() { return AppEvent::getInstance(); }
inline WidgetExtDevice* extDevice() { return app()->getWidgetExtDevice(); }

}  // namespace lidar
}  // namespace robosense

#endif  // _APP_EVENT_H_