﻿/******************************************************************************
 * Copyright 2024 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai
 *
 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONS<PERSON>QUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef DATA_STRUCT_H
#define DATA_STRUCT_H

#include "work_model/b_spline_fit.hpp"

#include "mech_communication/protocol/data_struct/mech.h"
#include "rsfsc_log/rsfsc_log_macro.h"
#include <algorithm>
#include <array>
#include <condition_variable>
#include <cstddef>
#include <cstdint>
#include <map>
// NOLINTNEXTLINE(modernize-concat-nested-namespaces)
namespace robosense
{
namespace lidar
{

enum class TestState
{
  NOT_START = 0,
  RUNNING,
  PASS,
  ABORT,
  FAILED
};

struct MsgResult
{
  int index;

  std::mutex mutex;
  std::condition_variable cv;

  bool ret;
};

/*修改为支持状态机的形式*/
enum class ActionState
{
  STATE_CHECK_ALL_STATE,
  STATE_CONNECT_LIDAR,
  STATE_ZERO_CHECK_ALL_STATE,
  STATE_ZERO_CONNECT_LIDAR,
  STATE_ZERO_ROTATE,
  STATE_ZERO_COLLECT,
  STATE_ZERO_CALIB,
  STATE_ZERO_TEST,
  STATE_INIT_LIDAR_WAVE,
  STATE_RESET_MOTOR,
  STATE_COLLECT_DATA,
  STATE_SAVE_DATA,
  STATE_SUCCESS,
  STATE_FAIL,
  STATE_ABORT,
  STATE_FINAL,
  STATE_END = -1
};

struct PointsInt
{
  std::vector<int> x_vec;
  std::vector<int> y_vec;

  void sortByX()
  {
    // 创建索引数组
    std::vector<size_t> indices(x_vec.size());
    for (size_t i = 0; i < indices.size(); ++i)
    {
      indices[i] = i;
    }

    // 根据x_vec中的值对索引进行排序
    std::sort(indices.begin(), indices.end(),
              [this](const size_t& _lhs, const size_t& _rhs) { return x_vec[_lhs] < x_vec[_rhs]; });

    // 根据排序后的索引重新排列x_vec和y_vec
    std::vector<int> sorted_x(x_vec.size());
    std::vector<int> sorted_y(y_vec.size());
    for (size_t i = 0; i < indices.size(); ++i)
    {
      sorted_x[i] = x_vec[indices[i]];
      sorted_y[i] = y_vec[indices[i]];
    }

    // 将排序后的结果赋值回原向量
    x_vec = std::move(sorted_x);
    y_vec = std::move(sorted_y);
  }
};
struct ReflBoardPos
{
  int refl               = -1;
  float width            = -1;
  float pos_factor       = -1;
  int area_index         = -1;
  int amp_index          = -1;
  float distance_05cm    = -1;
  float distance         = -1;
  float dist_mean        = 0;
  float dist_range       = 0;
  float dist_err_sd      = 0;  // 误差标准差
  float area_mean        = 0;
  float amp_mean         = 0;
  float dist_mean_code_0 = 0;
  float area_mean_code_0 = 0;
  float amp_mean_code_0  = 0;
  float dist_mean_code_1 = 0;
  float area_mean_code_1 = 0;
  float amp_mean_code_1  = 0;
};
using BoardRefl = int;
using BoardId   = int;
struct BoardInfo
{
  int board_id                    = -1;
  int raw_dist_min                = -1;
  int raw_dist_max                = -1;
  int comped_dist_min             = -1;
  int comped_dist_max             = -1;
  int board_index_min             = -1;
  int board_index_max             = -1;
  int ignored_dist                = -1;
  int detected_data_start         = -1;
  int detected_data_end           = -1;
  float distance_05cm             = -1;
  float distance                  = -1;
  float width                     = -1;
  float board_start_angle         = -1;
  float board_end_angle           = -1;
  int board_index_length          = -1;
  int board_start_area_min        = -1;
  int board_end_area_min          = -1;
  float board_start_remove_factor = -1;
  float board_end_remove_factor   = -1;
  bool is_found                   = false;
  std::vector<ReflBoardPos> refl_board_vec;

  ReflBoardPos getReflBoard(const BoardRefl& _refl)
  {
    for (auto& refl_board_pos : refl_board_vec)
    {
      if (refl_board_pos.refl == _refl)
      {
        return refl_board_pos;
      }
    }
    return {};
  }
};
struct DistAreaCodeMark
{
  std::vector<int> dist_vec;
  std::vector<int> area_vec;
  std::vector<int> amp_vec;
  std::vector<int> code_mark_vec;

  std::vector<float> dist_comped_vec;

  std::map<int, BoardInfo> board_info_map;
  std::map<int, BoardInfo> board_info_map_bak;

  // std::vector<float> dynamic_comp_fit_result;
  std::vector<std::vector<float>> dynamic_comp_result;

  std::vector<float> static_comp_result;

  std::map<BoardRefl, std::vector<int>> area_comp_fit_result_map;

  std::map<BoardRefl, std::vector<int>> amp_comp_fit_result_map;

  std::vector<float> abs_coe_k_vec;
  std::vector<float> abs_coe_b_vec;

  std::vector<float> dist_true_vec;
  std::vector<float> dist_test_vec;
  std::vector<float> dist_error_vec;

  int dynamic_start_index;
  int dynamic_board_id;
  int dynamic_code_mark;
  float dynamic_min_dist;

  bool refl90_is_min_area;
  bool is_dynamic_static_comp;

  ReflBoardPos getReflBoard(const int _board_id, const BoardRefl& _refl)
  {
    if (board_info_map.find(_board_id) != board_info_map.end())
    {
      return board_info_map[_board_id].getReflBoard(_refl);
    }
    return {};
  }
};

struct ZeroDataPoint
{
  uint16_t dist;
  uint16_t area;
  uint16_t azi;
};
struct ZeroAziMapData
{
  double dist_sum { 0. };
  double area_sum { 0. };
  double dist_mean { 0. };
  double area_mean { 0. };
  int count { 1 };
};

}  // namespace lidar
}  // namespace robosense
#endif  // DATA_STRUCT_H