﻿/******************************************************************************
 * Copyright 2024 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai
 *
 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONS<PERSON>QUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef PARA_INFO_H
#define PARA_INFO_H

#include "rsfsc_log/rsfsc_log.h"
#include <QMap>
#include <QMetaProperty>
#include <QObject>
#include <QVariant>

// NOLINTNEXTLINE(modernize-concat-nested-namespaces)
namespace robosense
{
namespace lidar
{

// 定义宏，用于声明具有特定类型的属性和相关的 getter/setter 方法，并指定默认值、最小值和最大值
// NOLINTNEXTLINE(cppcoreguidelines-macro-usage)
#define DECLARE_PROPERTY_WITH_LIMITS(TYPE, NAME, FUNC_NAME, COMMENT, DEFAULT_VALUE, MIN_VALUE, MAX_VALUE) \
  Q_PROPERTY(QVariant NAME READ get##FUNC_NAME WRITE set##FUNC_NAME)                                      \
  Q_PROPERTY(QString NAME##comment READ get##FUNC_NAME##Comment)                                          \
  Q_PROPERTY(QVariant NAME##type READ get##FUNC_NAME##Type)                                               \
  Q_PROPERTY(QVariant NAME##Min READ get##FUNC_NAME##Min)                                                 \
  Q_PROPERTY(QVariant NAME##Max READ get##FUNC_NAME##Max)                                                 \
public:                                                                                                   \
  QVariant get##FUNC_NAME() const { return (NAME) == QVariant::fromValue(NAME); }                         \
  void set##FUNC_NAME(const QVariant& value) { (NAME) = value.value<TYPE>(); }                            \
                                                                                                          \
private:                                                                                                  \
  QString get##FUNC_NAME##Comment() const { return COMMENT; }                                             \
  QString get##FUNC_NAME##Key() const { return #NAME; }                                                   \
  QVariant get##FUNC_NAME##Type() const { return QVariant::fromValue(NAME); }                             \
  QVariant get##FUNC_NAME##Min() const { return QVariant::fromValue(MIN_VALUE); }                         \
  QVariant get##FUNC_NAME##Max() const { return QVariant::fromValue(MAX_VALUE); }                         \
                                                                                                          \
public:                                                                                                   \
  TYPE NAME = DEFAULT_VALUE

// NOLINTNEXTLINE(cppcoreguidelines-macro-usage)
#define DECLARE_PARA_PROPERTY(TYPE, NAME, FUNC_NAME)                         \
  Q_PROPERTY(TYPE NAME##_para_var_ READ get##FUNC_NAME WRITE set##FUNC_NAME) \
public:                                                                      \
  TYPE get##FUNC_NAME() const { return NAME##_para_var_; }                   \
  void set##FUNC_NAME(const TYPE& _value) { (NAME##_para_var_) = _value; }   \
                                                                             \
private:                                                                     \
  TYPE NAME##_para_var_

class ParaInfo : public QObject
{
  Q_OBJECT
public:
  explicit ParaInfo(QObject* _parent = nullptr);
  explicit ParaInfo(ParaInfo&&)      = delete;
  explicit ParaInfo(const ParaInfo&) = delete;
  ParaInfo& operator=(ParaInfo&&) = delete;
  ParaInfo& operator=(const ParaInfo&) = delete;
  ~ParaInfo() override;

  DECLARE_PARA_PROPERTY(float, spinbox_rotator_x_angle_start, RotatorXAngleStart);
  DECLARE_PARA_PROPERTY(float, spinbox_rotator_x_angle_end, RotatorXAngleEnd);
  DECLARE_PARA_PROPERTY(int, spinbox_rotator_x_speed_start, RotatorXSpeedStart);
  DECLARE_PARA_PROPERTY(bool, checkbox_init_lidar, InitLidar);
  DECLARE_PARA_PROPERTY(bool, checkbox_deinit_lidar, DeinitLidar);
  DECLARE_PARA_PROPERTY(QString, lineedit_pc_password, PcPassword);
  DECLARE_PARA_PROPERTY(int, spinbox_sampling_interval, SamplingInterval);
  DECLARE_PARA_PROPERTY(bool, checkbox_wave_angle_confirm, WaveAngleConfirm);
  DECLARE_PARA_PROPERTY(bool, checkbox_zero_confirm, ZeroConfirm);
  DECLARE_PARA_PROPERTY(bool, checkbox_eval_result, EvalResult);
  DECLARE_PARA_PROPERTY(float, spinbox_rotate_light_spot, RotateLightSpot);
  DECLARE_PARA_PROPERTY(float, spinbox_stop_motor_to_angle, StopMotorToAngle);
  DECLARE_PARA_PROPERTY(int, spinbox_rotate_default_speed, RotateDefaultSpeed);
  DECLARE_PARA_PROPERTY(float, spinbox_rotate_setup_lidar, RotateSetupLidar);
  DECLARE_PARA_PROPERTY(int, spinbox_relay_chn, RelayChn);
  DECLARE_PARA_PROPERTY(bool, checkbox_check_firmware_version, CheckFirmwareVersion);
  DECLARE_PARA_PROPERTY(bool, checkbox_dyn_angle_comp, DynAngleComp);

  DECLARE_PARA_PROPERTY(QString, lineedit_azi_start, AziStart);
  DECLARE_PARA_PROPERTY(QString, lineedit_azi_end, AziEnd);
  DECLARE_PARA_PROPERTY(QString, lineedit_zero_pos, ZeroPos);
  DECLARE_PARA_PROPERTY(QString, lineedit_test_azi_start, TestAziStart);
  DECLARE_PARA_PROPERTY(QString, lineedit_test_azi_end, TestAziEnd);
  DECLARE_PARA_PROPERTY(float, spinbox_zero_stable_error, ZeroStableError);
  DECLARE_PARA_PROPERTY(bool, checkbox_rot, ZeroRotate);

  DECLARE_PARA_PROPERTY(bool, checkbox_onekey_first_zero, OneKeyFirstZero);
  DECLARE_PARA_PROPERTY(bool, checkbox_onekey_sec_zero, OneKeySecZero);
  DECLARE_PARA_PROPERTY(bool, checkbox_onekey_wave, OneKeyWave);
};

}  // namespace lidar
}  // namespace robosense
#endif  // PARA_INFO_H