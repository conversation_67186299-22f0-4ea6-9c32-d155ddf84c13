﻿/******************************************************************************
 * Copyright 2022 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#ifndef THREAD_H
#define THREAD_H

#include "decl_name.h"
#include "singleton.h"
#include <atomic>
#include <condition_variable>
#include <deque>
#include <future>
#include <memory>
#include <mutex>
#include <thread>
#include <vector>
/**
 *  @file   Thread.h   
 *  @brief  线程
 */
namespace robosense  // NOLINT
{
namespace lidar
{

class Thread;
/**
 *  @class  Runnable
 *  @brief  线程运行体,子类必须重写run方法.	
 */
class Runnable : public std::enable_shared_from_this<Runnable>
{
public:
  Runnable()                = default;
  Runnable(const Runnable&) = default;
  Runnable& operator=(const Runnable&) = default;
  Runnable(Runnable&&) noexcept        = default;
  Runnable& operator=(Runnable&&) noexcept = default;
  virtual ~Runnable()                      = default;

  /**
     * @brief 线程体主函数
     * @param thread 当前线程体依附的线程
     * @note 所有子类必须实现
     */
  virtual void run(Thread& _thread)             = 0;
  [[nodiscard]] virtual const char* className() = 0;
};

class Task
{
public:
  Task()            = default;
  Task(const Task&) = default;
  Task& operator=(const Task&) = default;
  Task(Task&&) noexcept        = default;
  Task& operator=(Task&&) noexcept = default;
  virtual ~Task()                  = default;

  virtual void run() = 0;
};

template <typename ReturnT>
class TaskWithReturn : public Task
{
public:
  TaskWithReturn()                      = default;
  TaskWithReturn(const TaskWithReturn&) = default;
  TaskWithReturn& operator=(const TaskWithReturn&) = default;
  TaskWithReturn(TaskWithReturn&&) noexcept        = default;
  TaskWithReturn& operator=(TaskWithReturn&&) noexcept = default;
  ~TaskWithReturn() override                           = default;

  void run() override = 0;
  ReturnT getResult()
  {
    std::future<ReturnT> return_future = result_promise_.get_future();
    return return_future.get();
  };

  void setResult(const ReturnT& _result) { result_promise_.set_value(_result); }

private:
  std::promise<ReturnT> result_promise_;
};

class Thread
{
public:
  enum ThreadState
  {
    STATE_INIT = 0,
    STATE_START,
    STATE_RUNNING,
    STATE_EXIT
  };

public:
  Thread()              = default;
  Thread(const Thread&) = delete;
  Thread& operator=(const Thread&) = delete;
  Thread(Thread&& _other) noexcept;
  Thread& operator=(Thread&& _other) noexcept;
  virtual ~Thread();

  /**
     * @brief 克隆函数
     * @return std::unique_ptr<Thread> 
     */
  virtual std::unique_ptr<Thread> clone();
  /**
     *  @brief  启动线程
     *  @param  runnable Runnable对象
     *  @return 成功返回true,失败返回false
     */
  virtual bool start(std::shared_ptr<Runnable> _ptr_runnable);
  /**
     *  @brief  停止线程
     *  @param  _ms_timeout 超时时间,<0时表示阻塞等待线程退出
     *  @return 成功返回true,失败返回false
     */
  bool stop();
  virtual bool stop(int _ms_timeout);
  /**
     *  @brief  判断线程是否正在运行
     *  @param  void
     *  @return 正在运行返回true,否则返回false
     */
  virtual bool isRunning();
  /**
     *  @brief  微秒延时函数
     *  @param  us 要延时的微秒数InstanceClass
     *  @return void
     *  @note   函数休眠时,当前进程可能会让出CPU,引发进程调度
     *          注意使用usleep时,时间不能设置得太短,否则调度时
     *          进程间切换太频繁非常耗资源!!!推荐最小值为100us
     */
  static void usleep(int _us);
  /**
     *  @brief  毫秒延时函数
     *  @param  ms 要延时的毫秒数
     *  @return void
     *  @note   函数休眠时,当前进程可能会让出CPU,引发进程调度
     */
  static void msleep(int _ms);
  /**
     * @brief 获取当前代码所在线程ID
     * @return int 
     */
  static int threadID();

private:
  void threadMain();
  bool run_flag_ { false };
  int thread_status_ { STATE_INIT };
  std::shared_ptr<Runnable> ptr_runnable_ { nullptr };
  std::unique_ptr<std::thread> ptr_thread_ { nullptr };
};

/**
 *  @class  ThreadPool
 *  @brief  一个简单的线程池的实现	
 */

class ThreadPool : public Singleton<ThreadPool>
{
  DECL_CLASSNAME(ThreadPool)

  friend class Singleton<ThreadPool>;

public:
  enum TaskType
  {
    NORMAL = 1,
    CYCLE
  };
  using TaskPtr = std::shared_ptr<Task>;
  //  using Task_ = std::function<void()>;

public:
  explicit ThreadPool(int _thread_num = 4);
  ThreadPool(ThreadPool&&) = delete;
  ThreadPool& operator=(ThreadPool&&) = delete;
  ~ThreadPool() override;

public:
  /**
     * @brief 线程池中添加任务
     * @return std::size_t
     */
  size_t addTask(const TaskPtr& _task);
  /**
     * @brief 停止线程池
     * @return  
     */
  void stop();
  /**
     * @brief 获取线程池的大小
     * @return  
     */
  std::size_t size();

private:
  TaskPtr take();
  int createThreads();
  static void* threadFunc(void* _arg);

public:
  ThreadPool& operator=(const ThreadPool&) = delete;

  ThreadPool(const ThreadPool&) = delete;

private:
  std::atomic<bool> is_running_;
  int threads_num_;

  std::vector<std::unique_ptr<std::thread>> threads_;
  std::deque<TaskPtr> task_queue_;

  std::mutex mutex_;
  std::condition_variable condition_;
};

/**
 *  @class  Barrier
 *  @brief  实现一个简单的栅栏
 */
class Barrier
{
  DECL_CLASSNAME(Barrier)

public:
  explicit Barrier(int _n_thread_count = 5) : thread_count_(_n_thread_count), release_(false) {}

  Barrier(const Barrier&) = delete;
  Barrier(Barrier&&)      = delete;
  Barrier& operator=(const Barrier&) = delete;
  Barrier& operator=(Barrier&&) = delete;
  virtual ~Barrier()            = default;

public:
  void waitFinish();
  void finish();
  void abort();

private:
  std::mutex mutex_;
  std::condition_variable condition_;
  unsigned int thread_count_;
  std::atomic<unsigned int> count_ { 0 };
  std::atomic<bool> release_;
};

class ThreadRAII
{
public:
  enum class DtorAction
  {
    JOIN,
    DETACH
  };

  ThreadRAII(ThreadRAII&&) = default;
  ThreadRAII& operator=(ThreadRAII&&) = default;
  ThreadRAII(const ThreadRAII&)       = delete;
  ThreadRAII& operator=(const ThreadRAII&) = delete;
  ThreadRAII(std::thread&& _thread, DtorAction _action) : action_(_action), t_(std::move(_thread)) {}

  ~ThreadRAII()
  {
    if (t_.joinable())
    {
      if (action_ == DtorAction::JOIN)
      {
        t_.join();
      }
      else
      {
        t_.detach();
      }
    }
  }

  std::thread& get() { return t_; }

private:
  DtorAction action_;
  std::thread t_;
};
}  // namespace lidar
}  // namespace robosense
#endif
