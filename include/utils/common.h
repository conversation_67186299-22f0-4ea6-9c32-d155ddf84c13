﻿/******************************************************************************
 * Copyright 2020 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef RSLIDAR_COMMON_H
#define RSLIDAR_COMMON_H

#include "rsfsc_log/rsfsc_log.h"
#include <QString>
#include <chrono>
#include <ctime>
#include <iostream>
#include <string>

namespace robosense  // NOLINT
{
namespace lidar
{

class ScopedTimer
{
public:
  explicit ScopedTimer() = default;
  explicit ScopedTimer(const char* _description) : description_(_description), start_(std::chrono::steady_clock::now())
  {}
  explicit ScopedTimer(const std::string& _description) :
    description_(_description), start_(std::chrono::steady_clock::now())
  {}
  explicit ScopedTimer(const QString& _description) :
    description_(_description.toStdString()), start_(std::chrono::steady_clock::now())
  {}

  explicit ScopedTimer(ScopedTimer&&)      = delete;
  explicit ScopedTimer(const ScopedTimer&) = delete;
  ScopedTimer& operator=(ScopedTimer&&) = delete;
  ScopedTimer& operator=(const ScopedTimer&) = delete;

  ~ScopedTimer()
  {
    auto end           = std::chrono::steady_clock::now();
    auto duration      = std::chrono::duration_cast<std::chrono::microseconds>(end - start_);
    float milliseconds = static_cast<float>(duration.count()) / 1000.0F;  // 转换为毫秒
                                                                          // 如果超过10s，打印为秒
    if (milliseconds > 10000)
    {
      std::cout << description_ << " 耗时: " << milliseconds / 1000.0F << " s" << std::endl;
      // RSFSCLog::getInstance()->info("{} 耗时: {} s", description_, milliseconds / 1000.0F);
    }
    else
    {
      std::cout << description_ << " 耗时: " << milliseconds << " ms" << std::endl;
      // RSFSCLog::getInstance()->info("{} 耗时: {} ms", description_, milliseconds);
    }
  }

private:
  std::string description_;
  std::chrono::time_point<std::chrono::steady_clock> start_;
};

int roundToInt(const double& _val);

}  // namespace lidar
}  // namespace robosense

#endif  // _RSLIDAR_COMMON_H_
