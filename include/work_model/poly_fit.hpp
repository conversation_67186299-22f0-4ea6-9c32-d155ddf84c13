﻿/******************************************************************************
 * Copyright 2021 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#ifndef POLY_FIT_H
#define POLY_FIT_H
#include <cmath>
#include <cstddef>
#include <cstdint>
#include <vector>

namespace robosense
{
namespace lidar
{
///
/// \brief 曲线拟合类
///
class PolyFit
{
public:
  PolyFit() : ssr_(0), sse_(0), rmse_(0) { factor_.resize(2, 0); }
  ///
  /// \brief 直线拟合-一元回归,拟合的结果可以使用getFactor获取，或者使用getSlope获取斜率，getIntercept获取截距
  /// \param x 观察值的x
  /// \param y 观察值的y
  /// \param isSaveFitYs 拟合后的数据是否保存，默认否
  ///
  template <typename T>
  bool linearFit(const std::vector<T>& _x, const std::vector<T>& _y, bool _is_save_fit_ys = false)
  {
    return linearFit(&_x[0], &_y[0], getSeriesLength(_x, _y), _is_save_fit_ys);
  }

  template <typename T>
  bool linearFit(const T* _x, const T* _y, size_t _length, bool _is_save_fit_ys = false)
  {
    factor_.resize(2, 0);
    T t1 = 0;
    T t2 = 0;
    T t3 = 0;
    T t4 = 0;
    for (int i = 0; i < static_cast<int>(_length); ++i)
    {
      t1 += _x[i] * _x[i];
      t2 += _x[i];
      t3 += _x[i] * _y[i];
      t4 += _y[i];
    }
    factor_[1] = (t3 * _length - t2 * t4) / (t1 * _length - t2 * t2);
    factor_[0] = (t1 * t4 - t2 * t3) / (t1 * _length - t2 * t2);
    //
    //计算误差
    calcError(_x, _y, _length, this->ssr_, this->sse_, this->rmse_, _is_save_fit_ys);
    return true;
  }
  ///
  /// \brief 多项式拟合，拟合y=a0+a1*x+a2*x^2+……+a poly_n*x^poly_n
  /// \param x 观察值的x
  /// \param y 观察值的y
  /// \param poly_n 期望拟合的阶数，若poly_n=2，则y=a0+a1*x+a2*x^2
  /// \param isSaveFitYs 拟合后的数据是否保存，默认是
  ///
  template <typename T>
  void polyFit(const std::vector<T>& _x, const std::vector<T>& _y, int _poly_n, bool _is_save_fit_ys = true)
  {
    polyFit(&_x[0], &_y[0], getSeriesLength(_x, _y), _poly_n, _is_save_fit_ys);
  }

  template <typename T>
  void polyFit(const T* _x, const T* _y, int _length, int _poly_n, bool _is_save_fit_ys = true)
  {
    factor_.resize(_poly_n + 1, 0);
    int i = 0;
    int j = 0;
    //double *temp_x,*temp_y,*sum_xx,*sum_xy,*ata;
    std::vector<double> temp_x(_length, 1.0);

    std::vector<double> temp_y(_y, _y + _length);

    std::vector<double> sum_xx(_poly_n * 2 + 1);
    std::vector<double> ata((_poly_n + 1) * (_poly_n + 1));
    std::vector<double> sum_xy(_poly_n + 1);
    for (i = 0; i < 2 * _poly_n + 1; i++)
    {
      for (sum_xx[i] = 0, j = 0; j < _length; j++)
      {
        sum_xx[i] += temp_x[j];
        temp_x[j] *= _x[j];
      }
    }
    for (i = 0; i < _poly_n + 1; i++)
    {
      for (sum_xy[i] = 0, j = 0; j < _length; j++)
      {
        sum_xy[i] += temp_y[j];
        temp_y[j] *= _x[j];
      }
    }
    for (i = 0; i < _poly_n + 1; i++)
    {
      for (j = 0; j < _poly_n + 1; j++)
      {
        ata[i * (_poly_n + 1) + j] = sum_xx[i + j];
      }
    }
    gaussSolve(_poly_n + 1, ata, factor_, sum_xy);
    //计算拟合后的数据并计算误差
    fitted_ys_.reserve(_length);
    calcError(&_x[0], &_y[0], _length, this->ssr_, this->sse_, this->rmse_, _is_save_fit_ys);
  }
  ///
  /// \brief 获取系数
  /// \param 存放系数的数组
  ///
  void getFactor(std::vector<double>& _factor) { _factor = this->factor_; }
  ///
  /// \brief 获取拟合方程对应的y值，前提是拟合时设置isSaveFitYs为true
  ///
  void getFittedYs(std::vector<double>& _fitted_ys) { _fitted_ys = this->fitted_ys_; }

  ///
  /// \brief 根据x获取拟合方程的y值
  /// \return 返回x对应的y值
  ///
  template <typename T>
  double getY(const T _x) const
  {
    double ans(0);
    for (size_t i = 0; i < factor_.size(); ++i)
    {
      ans += factor_[i] * pow((double)_x, static_cast<int>(i));
    }
    return ans;
  }
  [[nodiscard]] uint16_t getYtoU16(const uint16_t _x_value) const
  {
    double ans = getY(_x_value);
    if (ans < 0)
    {
      return 0;
    }
    if (ans > 65535)
    {
      return 65535;
    }
    return static_cast<uint16_t>(ans);
  }

  ///
  /// \brief 获取斜率
  /// \return 斜率值
  ///
  double getSlope() const { return factor_[1]; }
  ///
  /// \brief 获取截距
  /// \return 截距值
  ///
  double getIntercept() const { return factor_[0]; }
  ///
  /// \brief 剩余平方和
  /// \return 剩余平方和
  ///
  double getSSE() const { return sse_; }
  ///
  /// \brief 回归平方和
  /// \return 回归平方和
  ///
  double getSSR() const { return ssr_; }
  ///
  /// \brief 均方根误差
  /// \return 均方根误差
  ///
  double getRMSE() const { return rmse_; }
  ///
  /// \brief 确定系数，系数是0~1之间的数，是数理上判定拟合优度的一个量
  /// \return 确定系数
  ///
  double getRSquare() const { return 1 - (sse_ / (ssr_ + sse_)); }
  ///
  /// \brief 获取两个vector的安全size
  /// \return 最小的一个长度
  ///
  template <typename T>
  size_t getSeriesLength(const std::vector<T>& _x, const std::vector<T>& _y) const
  {
    return (_x.size() > _y.size() ? _y.size() : _x.size());
  }
  ///
  /// \brief 计算均值
  /// \return 均值
  ///
  template <typename T>
  static T mean(const std::vector<T>& _v)
  {
    return mean(&_v[0], _v.size());
  }

  template <typename T>
  static T mean(const T* _v, size_t _length)
  {
    T total(0);
    for (size_t i = 0; i < _length; ++i)
    {
      total += _v[i];
    }
    return (total / _length);
  }
  ///
  /// \brief 获取拟合方程系数的个数
  /// \return 拟合方程系数的个数
  ///
  size_t getFactorSize() const { return factor_.size(); }
  ///
  /// \brief 根据阶次获取拟合方程的系数，
  /// 如getFactor(2),就是获取y=a0+a1*x+a2*x^2+……+a poly_n*x^poly_n中a2的值
  /// \return 拟合方程的系数
  ///
  double getFactor(size_t _index) const { return factor_.at(_index); }

private:
  template <typename T>
  void calcError(const T* _x,
                 const T* _y,
                 size_t _length,
                 double& _r_ssr,
                 double& _r_sse,
                 double& _r_rmse,
                 bool _is_save_fit_ys = true)
  {
    T mean_y = mean<T>(_y, _length);
    T yi(0);
    fitted_ys_.reserve(_length);
    for (int i = 0; i < static_cast<int>(_length); ++i)
    {
      yi = getY(_x[i]);
      _r_ssr += ((yi - mean_y) * (yi - mean_y));  //计算回归平方和
      _r_sse += ((yi - _y[i]) * (yi - _y[i]));    //残差平方和
      if (_is_save_fit_ys)
      {
        fitted_ys_.push_back(double(yi));
      }
    }
    _r_rmse = sqrt(_r_sse / (double(_length)));
  }

  template <typename T>
  void gaussSolve(int _n, std::vector<T>& _a, std::vector<T>& _x, std::vector<T>& _b)
  {
    gaussSolve(_n, &_a[0], &_x[0], &_b[0]);
  }

  template <typename T>
  void gaussSolve(int _n, T* _a, T* _x, T* _b)
  {
    int i = 0;
    int j = 0;
    int k = 0;
    int r = 0;
    double max;
    for (k = 0; k < _n - 1; k++)
    {
      max = fabs(_a[k * _n + k]); /*find max num*/
      r   = k;
      for (i = k + 1; i < _n - 1; i++)
      {
        if (max < fabs(_a[i * _n + i]))
        {
          max = fabs(_a[i * _n + i]);
          r   = i;
        }
      }
      if (r != k)
      {
        for (i = 0; i < _n; i++) /*change array:A[k]&A[r] */
        {
          max            = _a[k * _n + i];
          _a[k * _n + i] = _a[r * _n + i];
          _a[r * _n + i] = max;
        }
      }
      max   = _b[k]; /*change array:b[k]&b[r]     */
      _b[k] = _b[r];
      _b[r] = max;
      for (i = k + 1; i < _n; i++)
      {
        for (j = k + 1; j < _n; j++)
          _a[i * _n + j] -= _a[i * _n + k] * _a[k * _n + j] / _a[k * _n + k];
        _b[i] -= _a[i * _n + k] * _b[k] / _a[k * _n + k];
      }
    }

    for (i = _n - 1; i >= 0; _x[i] /= _a[i * _n + i], i--)
    {
      for (j = i + 1, _x[i] = _b[i]; j < _n; j++)
      {
        _x[i] -= _a[i * _n + j] * _x[j];
      }
    }
  }

private:
  std::vector<double> factor_;     ///<拟合后的方程系数
  double ssr_;                     ///<回归平方和
  double sse_;                     ///<(剩余平方和)
  double rmse_;                    ///<RMSE均方根误差
  std::vector<double> fitted_ys_;  ///<存放拟合后的y值，在拟合时可设置为不保存节省内存
};
}  // namespace lidar
}  // namespace robosense
#endif  //POLY_FIT_H