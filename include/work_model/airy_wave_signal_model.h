﻿/******************************************************************************
 * Copyright 2024 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai
 *
 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONS<PERSON>QUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef AIRY_WAVE_SIGNAL_MODEL_H
#define AIRY_WAVE_SIGNAL_MODEL_H

#include "data_struct.h"
#include "para_info.h"
#include "rsfsc_fsm/finite_state_machine.h"
#include "rsfsc_fsm/mech/mech_work_model.h"
#include "rsfsc_lib/include/widget_log_setting.h"
#include "rsfsc_utils/tcpdump_utils.h"
#include "rsfsc_utils/thread_ext.h"
#include "tsl/ordered_map.h"
#include "utils/pcap_utils.h"
#include <QDir>
#include <mutex>
#include <queue>
#include <set>
#include <unistd.h>
#include <vector>

class QString;
class QTextStream;

// NOLINTNEXTLINE(modernize-concat-nested-namespaces)
namespace robosense
{
namespace lidar
{

class MechUdp;
struct MSOPMotorData;
class FairyWaveSignalModel;

class AiryWaveSignalModel : public MechWorkModel
{
  friend class FairyWaveSignalModel;

public:
  explicit AiryWaveSignalModel(rsfsc_lib::WidgetLidarInfo* _lidar_info);
  AiryWaveSignalModel(AiryWaveSignalModel&&)      = delete;
  AiryWaveSignalModel(const AiryWaveSignalModel&) = delete;
  AiryWaveSignalModel& operator=(AiryWaveSignalModel&&) = delete;
  AiryWaveSignalModel& operator=(const AiryWaveSignalModel&) = delete;
  ~AiryWaveSignalModel() override;

  struct DataPath
  {
    QDir root_dir;
    QDir data_dir;
    QDir temp_dir;
    QDir result_dir;

    QDir detect_board_dir;
    QDir dynamic_comp_dir;
    QDir static_comp_dir;
    QDir refl_comp_dir;
    QDir amp_comp_dir;
    QDir abs_comp_dir;
    QDir process_data_dir;
    QDir zero_dir;
    QDir difop_dir;
    QString pcap_zero_file_path_unformat;
    QString pcap_zero_file_path;
    QString pcap_wave_file_path;

    QString difop_file_path;

    QString dynamic_bit_file_name;
    QString static_bit_file_name;
    QString refl_bit_file_name;
    QString abs_bit_file_name;
    QString combine_bit_file_name;

    // 供光学分析使用
    QString refl90_10m_file_name;

    QString comped_eval_file_name;
  };

  struct AreaState
  {
    int area;
    int dist;
    float rate;
    std::vector<float> limit_min_vec;
    std::vector<bool> result_vec;
  };
  struct AreaTypeLimit
  {
    int area_ng_max_num        = 96;
    float area_ng_min_rate     = 0.001F;
    mech::MountType mount_type = mech::MountType::UNKNOWN;
    std::set<int> area_allow_chn;
    std::map<int, float> area_th_min;
  };

  enum class AreaType
  {
    PASS,
    ABNORMAL,
    NG,
    UNKNOWN
  };

  virtual std::shared_ptr<FiniteStateMachine<AiryWaveSignalModel>> createFsm();

  rsfsc_lib::WidgetLogSetting* getWidgetLogSetting() override;

  void loadConfigData();
  virtual QJsonObject getJsonBoardInfo();
  void loadJsonData();

  std::map<int, BoardInfo> getDefaultBoardInfoMap();
  [[nodiscard]] std::map<int, BoardInfo> initBoardInfo(const size_t _data_size);

  [[nodiscard]] QString getDataPath() const { return path_.data_dir.absolutePath(); };
  [[nodiscard]] QString getProcessDataPath() const { return path_.process_data_dir.absolutePath(); };

  void setParaInfo(const std::shared_ptr<ParaInfo>& _para_info_ptr) { para_info_ptr_ = _para_info_ptr; }
  [[nodiscard]] std::shared_ptr<ParaInfo> getParaInfo() const { return para_info_ptr_; }

  [[nodiscard]] bool getIsCollectWave() const { return is_collect_wave_; }
  void setCollectWave(const bool _is_collect) { is_collect_wave_ = true; }

  int getZeroCalibCount() const { return zero_calib_count_; }

  [[nodiscard]] float getZeroAngle() const { return zero_angle_; }
  void resetState();

  bool addMeasureMessage(const QString& _name, const bool _data);
  bool addMeasureMessage(const QString& _name, const double _data, const rsfsc_lib::MeasureDataType _data_type);
  bool addMeasureMessage(const LimitInfo& _limit_info, const double _data, const rsfsc_lib::MeasureDataType _data_type);
  bool addMeasureMessage(const QString& _name, const std::string& _data);
  bool addMeasureMessage(const LimitInfo& _limit_info, const QVariant& _value);
  bool addMeasureMessageWithSuffix(const QString& _name, const QString& _suffix, const double _data);
  void setFailMsg(const QString& _fail_label, const QString& _fail_msg);
  void finishProcess(const rsfsc_lib::LogTestStatus _ts);

  virtual bool scanConnectLidarAndWaitForTop();
  bool initZeroCalib();
  bool connectLidar();
  bool disconnectLidar();
  bool checkAllState();
  bool checkIfChnAngleZero();
  bool checkVersion();

  bool initWaveCalib();
  bool collectPcap(const std::string& _path);
  bool startCollectPcap();
  bool startCollectZero(const bool _test = false);
  void stopCollectPcap();
  bool startDumpDifop();
  void stopDumpDifop();
  using ChannelNumType = int;
  using BoardIDType    = int;
  void initPath(const QString& _pcap_file_path = "", const bool _is_zero = false);
  bool loadPcapFile(const QString& _pcap_file_path);
  bool loadPcapFile();

  bool loadPcapZero(const QString& _pcap_file_path, const bool _test = false);
  bool loadPcapZero(const bool _test = false);

  std::map<uint16_t, ZeroAziMapData> getZeroAziMap(const std::vector<ZeroDataPoint>& _zero_data,
                                                   const bool _is_test = false);
  std::optional<float> getHigReflCenterAzi(const std::map<uint16_t, ZeroAziMapData>& _azi_zero_map);
  bool processZero();
  bool processZeroTest();

  void parseMsopPackets(const std::vector<uint8_t>& _msop_packet_vec);
  DistAreaCodeMark& getDistAreaMap(const int _chn_num) { return dist_area_map_[_chn_num]; }
  std::map<BoardIDType, BoardInfo> getBoardInfoMap() { return board_info_map_; }
  bool saveDetectAllChnBoard();

  bool detectBoard(const int _channel_num);
  bool detectDynamicBoard(const int _channel_num);
  bool detect10mBoard(const int _channel_num);
  bool detect20mBoard(const int _channel_num);
  bool detectStaticBoard(const int _channel_num);
  bool detectReflBoardCenter(const int _channel_num);
  bool detectBoardCenter(const int _channel_num, BoardInfo& _board_info, const int _window_size = 100);
  bool fix10mBoard(const int _channel_num);
  bool detectBoardUseTrueDist(const int _channel_num);
  bool fixBoardCompedDist(const int _channel_num);

  std::vector<float> fitDynamic(const int _channel_num,
                                std::vector<float>& _area,
                                std::vector<float>& _dist,
                                std::vector<float>& _area_20m,
                                std::vector<float>& _dist_20m);
  bool extractReflCenterMeanData(const int _channel_num,
                                 const int _board_id,
                                 const int _refl,
                                 std::vector<std::vector<float>>& _dist_vec,
                                 std::vector<std::vector<float>>& _area_vec);
  bool extractReflCenterData(const int _channel_num,
                             const int _board_id,
                             const int _refl,
                             std::vector<std::vector<float>>& _dist_vec,
                             std::vector<std::vector<float>>& _area_vec);
  bool checkDynConditions(const int _channel_num, BoardInfo& _board_info);
  bool getDynamicTargetBoardData(const int _channel_num,
                                 const BoardInfo& _board_info,
                                 const double _angle_per_sample,
                                 std::vector<std::vector<float>>& _dyn_dist_vec,
                                 std::vector<std::vector<float>>& _dyn_area_vec,
                                 int& _max_area,
                                 int& _dynamic_start_index);
  bool preprocessAndCheckDyn(const int _channel_num,
                             std::vector<std::vector<float>>& _dyn_dist_vec,
                             std::vector<std::vector<float>>& _dyn_area_vec,
                             std::vector<std::vector<float>>& _dyn_dist_vec_20m,
                             std::vector<std::vector<float>>& _dyn_area_vec_20m);
  bool processDynamicData(const int _channel_num);

  bool processStaticData(const int _channel_num);
  bool processReflData(const int _channel_num);
  bool processAbsData(const int _channel_num);

  void backupBoardInfoMap(const int _channel_num);

  bool checkReflAmpData(const int _channel_num);
  bool checkDistReflData(const int _channel_num);
  bool checkBoardDistRefl(const int _channel_num);

  uint8_t transformAreaToRefl(const int _channel_num, const float _area, const float _dist);

  static uint16_t sign2fi(const double& _val, const int& _width_bit, const int& _frac_bit);

  void equivalentDistRefl(const int _channel_num, std::map<BoardRefl, PointsInt>& _dist_area_points_map);
  void interpolateRefl(const BoardRefl _refl, PointsInt& _raw_points);
  static void equivalentDistAmp(std::map<BoardRefl, PointsInt>& _refl_dist_amp_points_map);
  static bool linearInterpolation(const BoardRefl _refl,
                                  const PointsInt& _raw_points,
                                  std::vector<int>& _fit_res_points);

  bool autoProcessAllData();

  bool generateAllBit();
  bool saveFile(const QString& _save_path, const char* _data, const int _size);
  bool generateDynamicBit(mech::DynamicBit& _dynamic_bit, const QString& _save_path);
  bool generateStaticBit(mech::StaticBit& _static_bit, const QString& _save_path);
  bool generateReflBit(mech::ReflBit& _refl_bit, const QString& _save_path);
  bool generateAbsBit(mech::AbsBit& _abs_bit, const QString& _save_path);
  static bool getEmpAbsBit(mech::EmpAbsBit& _emp_abs_bit);

  bool ifAllBoardNotFound();
  std::map<int, AreaState> getAreaStateMap();
  AreaType checkAreaType(std::map<int, AreaState>& _area_state_map,
                         tsl::ordered_map<mech::MountType, AreaType>& _mount_area_result);
  AreaType checkMappingAreaType(std::map<int, AreaState>& _area_state_map);
  AreaType checkFrontAreaType(std::map<int, AreaState>& _area_state_map);
  AreaType checkSideAreaType(std::map<int, AreaState>& _area_state_map);
  AreaType checkMowAreaType(std::map<int, AreaState>& _area_state_map);
  AreaType checkAreaTypeLimit(const AreaTypeLimit& _area_type_limit, std::map<int, AreaState>& _area_state_map);

  bool saveAreaData(std::map<int, AreaState>& _area_state_map,
                    tsl::ordered_map<mech::MountType, AreaType>& _mount_area_result);
  bool save90Refl10m();
  [[nodiscard]] AreaType getAreaType() const { return product_area_type_; }

  bool writeBitToLidar(const QString& _bit_file_path = "");
  bool writeChnAngle();
  bool writeVbdGdi(const uint32_t _vbd_intercept_hex, const uint32_t _vbd_err_hex);
  bool readVbdCurve(uint16_t& _v0, uint16_t& _v1, uint16_t& _v2);
  bool readVbd(uint32_t& _vbd_intercept_hex, uint32_t& _vbd_err_hex);

  bool setPcapFilePath(const QString& _pcap_file_path);

  bool saveZero(const bool _is_test = false);
  bool saveDistAreaMap(const int _channel_num);
  bool saveInfo(const int _channel_num);
  bool saveAllProcessData();

  bool showData(const int _channel_num,
                const int _process_num,
                QString _type,
                QString _data_path,
                const bool _is_show,
                const bool _is_save) const;
  bool showZeroData(QString _data_path, const bool _is_show, const bool _is_save) const;

  void updateAreaType(const AreaType _area_type);
  void updateIfMowOK(const bool _is_mow_ok);
  void clearMowType();

  // void abort() override;

private:
  TcpdumpUtils tcpdump_utils_;
  TcpdumpUtils fsm_tcpdump_utils_;
  std::shared_ptr<ParaInfo> para_info_ptr_ = nullptr;
  DataPath path_;
  std::shared_ptr<MechUdp> mech_udp_;
  std::mutex data_mutex_;
  std::queue<mech::MsopPacket> data_queue_;

  std::map<int, BoardInfo> default_board_info_map_;

  float zero_slope_          = 0;
  float zero_angle_          = 0;
  float zero_azi_start_      = 0;
  float zero_azi_end_        = 0;
  float zero_hor_chn1_angle_ = 0;
  float zero_stable_error_   = 0;
  std::vector<ZeroDataPoint> zero_data_;

  float zero_test_slope_           = 0;
  float zero_test_angle_           = 0;
  float zero_test_angle_fit_start_ = 0;
  float zero_test_angle_fit_end_   = 0;
  std::vector<ZeroDataPoint> zero_test_data_;

  std::vector<float> chn_angle_vec_;

  std::vector<int> zero_area_chn_vec_;
  std::vector<int> dynamic_len_not_enough_chn_vec_;

  std::map<ChannelNumType, DistAreaCodeMark> dist_area_map_;
  std::map<BoardIDType, BoardInfo> board_info_map_;

  int sample_interval_        = 1;
  float rotator_speed_factor_ = 1;

  QString combine_bit_file_path_;

  std::vector<int> temp_refl_;

  pid_t tcpdump_pid_ = -1;

  std::vector<float> chn_angle_hor_vec_;
  std::vector<float> chn_angle_ver_vec_;

  QString fail_label_;
  QString fail_msg_;

  int board_3m_id_   = 0;
  int board_10m_id_  = 0;
  int board_20m_id_  = 0;
  int board_1_2m_id_ = 0;
  int board_0_2m_id_ = 0;

  int total_chn_num_ = 96;

  // int zero_dist_min_      = 1000;
  // int zero_dist_max_      = 2500;
  // int zero_high_refl_min_ = 150;
  int zero_dist_min_      = 0;
  int zero_dist_max_      = 0;
  int zero_high_refl_min_ = 0;

  int dynamic_area_min_                  = 0;
  int dynamic_area_max_                  = 0;
  double dynamic_remove_low_refl_factor_ = 0;

  int dynamic_board_id_ = 0;
  int static_board_id_  = 0;
  uint16_t temperature_ = 0;
  std::vector<int> abs_board_id_vec_;
  std::vector<int> refl_board_id_vec_;

  std::vector<int> block_3m_refl_delta_vec_;

  mech::MountType mount_type_ = static_cast<mech::MountType>(255);

  int zero_calib_count_ = 0;
  bool is_collect_wave_ = false;

  mech::CombineBit combine_bit_;

  QString pcap_factory_loc_;

  AreaType product_area_type_ = AreaType::UNKNOWN;

  mech::DifopPacket difop_pkt_;

  // PCAP文件名信息结构体
  struct PcapFileInfo
  {
    QString file_path;
    QString lidar_sn;
    QString factory_location;
    int rotation_angle = 0;
    bool is_reverse    = false;
    bool is_zero_file  = false;
    bool is_test_file  = false;
  };

  // PCAP文件加载相关的私有函数
  // 第一步：提取文件名信息
  PcapFileInfo extractFileNameInfo(const QString& _pcap_file_path,
                                   const bool _is_zero = false,
                                   const bool _is_test = false);

  // 第二步：提取UDP包
  bool extractUdpPackets(PcapUtils& _pcap_utils,
                         std::vector<mech::MsopPacket>& _msop_packets,
                         std::vector<mech::DifopPacket>& _difop_packets);

  // 第三步：处理MSOP包和DIFOP包
  bool processMsopPackets(std::vector<mech::MsopPacket>& _msop_packets);

  // 零度标定专用的MSOP包处理函数
  bool processZeroMsopPackets(const std::vector<mech::MsopPacket>& _msop_packets, const bool _is_test);

  // 第四步：清除状态
  void clearLoadingState();

  // 第五步：确认雷达线数
  virtual bool confirmLidar();
};

class PyProcessTask : public TaskWithReturn<bool>
{
public:
  PyProcessTask(const QString& _program, const QStringList& _arguments, const QString& _info) :
    program_(_program), arguments_(_arguments), info_(_info)
  {}

  void run() override;

private:
  QString program_;
  QStringList arguments_;
  QString info_;
};

}  // namespace lidar
}  // namespace robosense
#endif  // AIRY_WAVE_SIGNAL_MODEL_H
