﻿/******************************************************************************
 * Copyright 2023 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

/**
 * @file   b_spline_fit.hpp
 * <AUTHOR> Dai(<EMAIL>)
 * @brief  A b-spline differs from an interpolating spline in that the
 *resulting curve is not required to pass through each data point.
 * @date 2023-11-27
 **/

#ifndef B_SPLINE_FIT_HPP
#define B_SPLINE_FIT_HPP

#include <algorithm>
#include <cmath>
#include <cstddef>
#include <gsl/gsl_multifit.h>
#include <gsl/gsl_multifit_nlinear.h>

#include <gsl/gsl_bspline.h>
#include <gsl/gsl_math.h>
#include <gsl/gsl_spline.h>
#include <memory>
#include <vector>

#include <cstdio>
#include <cstdlib>
#include <gsl/gsl_linalg.h>
#include <gsl/gsl_matrix.h>
#include <gsl/gsl_vector.h>

#include <gsl/gsl_errno.h>
#include <stdexcept>

// NOLINTNEXTLINE(modernize-concat-nested-namespaces)
namespace robosense
{
namespace lidar
{

inline void gslErrorHandler(const char* _reason, const char* /*_file*/, int /*_line*/, int /*_gsl_errno*/)
{
  // 将GSL错误转化为std::runtime_error异常
  throw std::runtime_error(_reason);
}

struct BsplineInfo
{
  // 正则化参数
  double lambda_sq = 20.;
  // 正则化的导数阶数
  size_t nderiv = 2;
  // 样条的阶数
  size_t order = 3;
  // 断点的个数
  size_t nbreak = 10;
};

class BSplineFit
{
public:
  BSplineFit() { gsl_set_error_handler(&gslErrorHandler); }
  BSplineFit(const BSplineFit&) = delete;
  BSplineFit& operator=(const BSplineFit&) = delete;
  BSplineFit(BSplineFit&&)                 = default;
  BSplineFit& operator=(BSplineFit&&) = default;
  ~BSplineFit()                       = default;

  /**
 * Regularized B-spline fit using template types.
 * 
 * @param _x The x data points.
 * @param _y The y data points.
 * @param _lambda_sq The regularization parameter.
 * @param result The vector where the fitted y values will be stored.
 */
  template <typename T1, typename T2>
  void regularizedEndpointBsplineFit(const std::vector<T1>& _x_vec,
                                     const std::vector<T2>& _y_vec,
                                     const BsplineInfo& _bspline_info = BsplineInfo())
  {
    size_t data_size      = _x_vec.size();
    gsl_vector* x_gsl_vec = gsl_vector_alloc(data_size);
    gsl_vector* y_gsl_vec = gsl_vector_alloc(data_size);

    double x_min = *std::min_element(_x_vec.begin(), _x_vec.end());
    double x_max = *std::max_element(_x_vec.begin(), _x_vec.end());

    work_ = std::unique_ptr<gsl_bspline_workspace, decltype(&gsl_bspline_free)>(
      gsl_bspline_alloc(_bspline_info.order, _bspline_info.nbreak), gsl_bspline_free);
    size_t ctrl_points = gsl_bspline_ncontrol(work_.get());  // Number of control points
    ctrl_points_reg_ =
      std::unique_ptr<gsl_vector, decltype(&gsl_vector_free)>(gsl_vector_alloc(ctrl_points), gsl_vector_free);
    gsl_vector* weights   = gsl_vector_alloc(data_size);                         // Data weights
    gsl_matrix* xtx       = gsl_matrix_alloc(ctrl_points, _bspline_info.order);  // Normal equation matrix
    gsl_vector* xty       = gsl_vector_alloc(ctrl_points);                       // Right-hand side vector
    gsl_matrix* cov_reg   = gsl_matrix_alloc(ctrl_points, ctrl_points);  // Covariance matrix for regularized model
    gsl_matrix* a1_matrix = gsl_matrix_alloc(ctrl_points, _bspline_info.order);  // Left regularization matrix
    gsl_matrix* a2_matrix = gsl_matrix_alloc(ctrl_points, _bspline_info.order);  // Right regularization matrix

    for (size_t i = 0; i < data_size; ++i)
    {
      gsl_vector_set(x_gsl_vec, i, static_cast<double>(_x_vec[i]));
      gsl_vector_set(y_gsl_vec, i, static_cast<double>(_y_vec[i]));
      gsl_vector_set(weights, i, 1.0);  // Assuming equal weights for simplicity
    }

    // Use uniform breakpoints on [a, b]
    gsl_bspline_init_uniform(x_min, x_max, work_.get());

    // Compute regularization matrices
    gsl_bspline_oprod(_bspline_info.nderiv, x_min, a1_matrix, work_.get());
    gsl_bspline_oprod(_bspline_info.nderiv, x_max, a2_matrix, work_.get());

    // Multiply by lambda^2 for regularization
    gsl_matrix_scale(a1_matrix, _bspline_info.lambda_sq);
    gsl_matrix_scale(a2_matrix, _bspline_info.lambda_sq);

    // Form the normal equations matrix
    gsl_bspline_lsnormal(x_gsl_vec, y_gsl_vec, weights, xty, xtx, work_.get());

    // Add regularization terms to the normal equations matrix
    gsl_matrix_add(xtx, a1_matrix);
    gsl_matrix_add(xtx, a2_matrix);

    // Solve the system with Cholesky decomposition
    gsl_linalg_cholesky_band_decomp(xtx);
    gsl_linalg_cholesky_band_solve(xtx, xty, ctrl_points_reg_.get());
    gsl_bspline_covariance(xtx, cov_reg, work_.get());

    // Clean up memory
    gsl_vector_free(x_gsl_vec);
    gsl_vector_free(y_gsl_vec);
    gsl_vector_free(weights);
    gsl_matrix_free(xtx);
    gsl_vector_free(xty);
    gsl_matrix_free(cov_reg);
    gsl_matrix_free(a1_matrix);
    gsl_matrix_free(a2_matrix);
  }

  template <typename T1, typename T2>
  void regularizedFullIntervalBsplineFit(const std::vector<T1>& _x_vec,
                                         const std::vector<T2>& _y_vec,
                                         const BsplineInfo& _bspline_info = BsplineInfo())
  {
    size_t data_size      = _x_vec.size();
    gsl_vector* x_gsl_vec = gsl_vector_alloc(data_size);
    gsl_vector* y_gsl_vec = gsl_vector_alloc(data_size);

    double x_min = *std::min_element(_x_vec.begin(), _x_vec.end());
    double x_max = *std::max_element(_x_vec.begin(), _x_vec.end());

    work_ = std::unique_ptr<gsl_bspline_workspace, decltype(&gsl_bspline_free)>(
      gsl_bspline_alloc(_bspline_info.order, _bspline_info.nbreak), gsl_bspline_free);
    size_t ctrl_points = gsl_bspline_ncontrol(work_.get());  // Number of control points
    ctrl_points_reg_ =
      std::unique_ptr<gsl_vector, decltype(&gsl_vector_free)>(gsl_vector_alloc(ctrl_points), gsl_vector_free);
    gsl_vector* weights    = gsl_vector_alloc(data_size);                         // Data weights
    gsl_matrix* xtx        = gsl_matrix_alloc(ctrl_points, _bspline_info.order);  // Normal equation matrix
    gsl_vector* xty        = gsl_vector_alloc(ctrl_points);                       // Right-hand side vector
    gsl_matrix* cov_reg    = gsl_matrix_alloc(ctrl_points, ctrl_points);  // Covariance matrix for regularized model
    gsl_matrix* reg_matrix = gsl_matrix_alloc(ctrl_points, _bspline_info.order);  // regularization matrix

    for (size_t i = 0; i < data_size; ++i)
    {
      gsl_vector_set(x_gsl_vec, i, static_cast<double>(_x_vec[i]));
      gsl_vector_set(y_gsl_vec, i, static_cast<double>(_y_vec[i]));
      gsl_vector_set(weights, i, 1.0);  // Assuming equal weights for simplicity
    }

    gsl_bspline_init_uniform(x_min, x_max, work_.get());

    gsl_bspline_gram_interval(x_min, x_max, 2, reg_matrix, work_.get());
    gsl_matrix_scale(reg_matrix, _bspline_info.lambda_sq);

    gsl_bspline_lsnormal(x_gsl_vec, y_gsl_vec, weights, xty, xtx, work_.get());
    gsl_matrix_add(xtx, reg_matrix);
    gsl_linalg_cholesky_band_decomp(xtx);
    gsl_linalg_cholesky_band_solve(xtx, xty, ctrl_points_reg_.get());
    gsl_bspline_covariance(xtx, cov_reg, work_.get());

    gsl_vector_free(x_gsl_vec);
    gsl_vector_free(y_gsl_vec);
    gsl_vector_free(weights);
    gsl_matrix_free(xtx);
    gsl_vector_free(xty);
    gsl_matrix_free(cov_reg);
    gsl_matrix_free(reg_matrix);
  }

  template <typename T>
  double getY(const T _x_value) const
  {
    double result_reg = NAN;
    gsl_bspline_calc(_x_value, ctrl_points_reg_.get(), &result_reg, work_.get());
    return result_reg;
  }
  [[nodiscard]] uint16_t getYtoU16(const uint16_t _x_value) const
  {
    double result_reg = NAN;
    gsl_bspline_calc(_x_value, ctrl_points_reg_.get(), &result_reg, work_.get());
    if (result_reg < 0)
    {
      return 0;
    }

    if (result_reg > 65535)
    {
      return 65535;
    }
    return std::lround(result_reg);
  }

  ///
  /// \brief 获取拟合方程对应的y值，前提是拟合时设置isSaveFitYs为true
  ///
  void getFittedYs(std::vector<double>& _fitted_ys) { _fitted_ys = this->fitted_ys_; }

  ///
  /// \brief 剩余平方和
  /// \return 剩余平方和
  ///
  [[nodiscard]] double getSSE() const { return sse_; }
  ///
  /// \brief 回归平方和
  /// \return 回归平方和
  ///
  [[nodiscard]] double getSSR() const { return ssr_; }
  ///
  /// \brief 均方根误差
  /// \return 均方根误差
  ///
  [[nodiscard]] double getRMSE() const { return rmse_; }
  ///
  /// \brief 确定系数，系数是0~1之间的数，是数理上判定拟合优度的一个量
  /// \return 确定系数
  ///
  [[nodiscard]] double getRSquare() const { return 1 - (sse_ / (ssr_ + sse_)); }

  ///
  /// \brief 计算均值
  /// \return 均值
  ///
  template <typename T>
  static double mean(const std::vector<T>& _val)
  {
    double sum = 0;
    for (const auto& val : _val)
    {
      sum += static_cast<double>(val);
    }
    return sum / static_cast<double>(_val.size());
  }

private:
  template <typename T1, typename T2>
  void calcError(const std::vector<T1>& _x_vec,
                 const std::vector<T2>& _y_vec,
                 double& _r_ssr,
                 // NOLINTNEXTLINE(bugprone-easily-swappable-parameters)
                 double& _r_sse,
                 double& _r_rmse,
                 bool _is_save_fit_ys = true)
  {
    T1 mean_y = mean(_y_vec);
    T1 yi_value(0);
    fitted_ys_.reserve(_y_vec.size());
    for (int i = 0; i < _y_vec.size(); ++i)
    {
      yi_value = getY(_x_vec[i]);
      _r_ssr += ((yi_value - mean_y) * (yi_value - mean_y));        //计算回归平方和
      _r_sse += ((yi_value - _y_vec[i]) * (yi_value - _y_vec[i]));  //残差平方和
      if (_is_save_fit_ys)
      {
        fitted_ys_.push_back(double(yi_value));
      }
    }
    _r_rmse = sqrt(_r_sse / (double(_y_vec.size())));
  }

private:
  std::unique_ptr<gsl_bspline_workspace, decltype(&gsl_bspline_free)> work_ { gsl_bspline_alloc(10, 30),
                                                                              gsl_bspline_free };
  std::unique_ptr<gsl_vector, decltype(&gsl_vector_free)> ctrl_points_reg_ { gsl_vector_alloc(30), gsl_vector_free };

  double ssr_ {};                  ///<回归平方和
  double sse_ {};                  ///<(剩余平方和)
  double rmse_ {};                 ///< RMSE均方根误差
  std::vector<double> fitted_ys_;  ///<存放拟合后的y值，在拟合时可设置为不保存节省内存
};

}  // namespace lidar
}  // namespace robosense
#endif  // B_SPLINE_FIT_HPP