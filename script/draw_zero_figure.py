﻿# /******************************************************************************
#  * Copyright 2020 RoboSense All rights reserved.
#  * Suteng Innovation Technology Co., Ltd. www.robosense.ai

#  * This software is provided to you directly by RoboSense and might
#  * only be used to access RoboSense LiDAR. Any compilation,
#  * modification, exploration, reproduction and redistribution are
#  * restricted without RoboSense's prior consent.

#  * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
#  * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
#  * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
#  * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
#  * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CO<PERSON><PERSON>QUE<PERSON>IAL DAMAGES
#  * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
#  * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
#  * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
#  * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
#  * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
#  * POSSIBILITY OF SUCH DAMAGE.
#  *****************************************************************************/

import pandas as pd
import matplotlib.pyplot as plt
import os
import numpy as np
import itertools
import json
import argparse
from pathlib import Path

from matplotlib import rcParams
import matplotlib.pyplot as plt
from matplotlib.transforms import Affine2D
from matplotlib.projections import PolarAxes
from mpl_toolkits.axisartist import angle_helper
from mpl_toolkits.axisartist.grid_finder import MaxNLocator, FixedLocator
from mpl_toolkits.axisartist import GridHelperCurveLinear, HostAxes
import matplotlib.ticker as mticker # Import ticker
from matplotlib.colors import LinearSegmentedColormap, Normalize

# Custom Formatter to handle axisartist's internal calls
class RobustAngleFormatter(mticker.Formatter):
    def __call__(self, value, pos=None, *args): # Accept arbitrary extra args
        """Formats angle robustly, accepting extra args from axisartist."""
        # Check if the value is numeric before performing modulo
        if isinstance(value, (int, float)):
            angle_normalized = value % 360
            # Return the formatted string
            return f"{int(round(angle_normalized))}°"
        else:
            # If value is not numeric, return an empty string or handle as needed
            return "" # Avoid placing non-numeric labels

def create_polar_axes(fig, subplot_position, max_radius=600):
    """创建可平移的极坐标轴，标签在边缘且兼容缩放/错误"""
    # 创建变换
    tr = Affine2D().scale(np.pi/180., 1.) + PolarAxes.PolarTransform()
    
    # 设置极值查找器 - 使用标准的-180到180范围
    extreme_finder = angle_helper.ExtremeFinderCycle(
        20, 20,
        lon_cycle=360,
        lat_cycle=None,
        lon_minmax=(-360, 360),  # 使用-180到180范围更标准
        lat_minmax=(0, max_radius)
    )
    
    # --- 定位器 --- 
    # 使用固定间隔的角度刻度，确保 10 度一个，覆盖全部360度
    angle_ticks = np.arange(-360, 360, 5)  # 包括-180到180，10度间隔
    grid_locator1 = FixedLocator(angle_ticks)  # 角度网格线位置
    grid_locator2 = MaxNLocator(5)  # 半径网格线/刻度位置
    
    # --- 格式化器 --- 
    # 使用健壮的自定义格式化器，可以处理任意数量的参数
    tick_formatter1 = RobustAngleFormatter()
    tick_formatter2 = None  # 半径使用默认格式
    
    # --- 网格辅助器 --- 
    grid_helper = GridHelperCurveLinear(
        tr,
        extreme_finder=extreme_finder,
        grid_locator1=grid_locator1,
        grid_locator2=grid_locator2,
        tick_formatter1=tick_formatter1,
        tick_formatter2=tick_formatter2
    )
    
    # --- 创建轴 --- 
    ax = fig.add_subplot(subplot_position, axes_class=HostAxes, grid_helper=grid_helper)
    
    # --- 调整基本外观 --- 
    # 显示完整的矩形边框
    ax.axis["right"].set_visible(True)
    ax.axis["top"].set_visible(True)
    ax.axis["left"].set_visible(True)
    ax.axis["bottom"].set_visible(True)
    
    # --- 创建浮动轴用于刻度和标签 ---
    # 水平径向轴（处理0/180度方向）
    ax.axis["lon1"] = axis = ax.new_floating_axis(1, 0)
    axis.label.set_visible(False)
    axis.major_ticklabels.set_visible(False)
    axis.major_ticks.set_visible(True)
    axis.line.set_visible(False)
    
    # 垂直径向轴（处理90/270度方向）
    ax.axis["lon2"] = axis = ax.new_floating_axis(1, 90)
    axis.label.set_visible(False)
    axis.major_ticklabels.set_visible(False)
    axis.major_ticks.set_visible(True)
    axis.line.set_visible(False)
    
    # 纬度轴（处理半径）- 放在左侧
    ax.axis["lat"] = axis = ax.new_floating_axis(0, 0)
    axis.label.set_visible(False)
    axis.major_ticklabels.set_visible(True)  # 显示半径刻度标签
    axis.major_ticks.set_visible(True)
    axis.set_axis_direction("left")
    axis.line.set_visible(False)
    
    # 创建新的浮动轴专门用于显示角度标签
    # 这个轴将放在外圆周
    for angle in range(0, 360, 5):
        ax.axis[f"angle_{angle}"] = axis = ax.new_floating_axis(
            0,  # 径向轴
            angle  # 角度位置
        )
        axis.major_ticklabels.set_visible(False)  # 不显示径向刻度
        axis.major_ticks.set_visible(False)  # 不显示径向刻度线
        axis.line.set_visible(False)  # 不显示轴线
        
        # 添加角度标签在边缘
        angle_rad = np.deg2rad(angle)
        label_x = np.cos(angle_rad) * max_radius * 1.05
        label_y = np.sin(angle_rad) * max_radius * 1.05
        
        # 根据角度调整文本对齐方式
        if angle == 0 or angle == 180:
            ha = 'left' if angle == 0 else 'right'
            va = 'center'
            rotation = 0
        elif angle == 90 or angle == 270:
            ha = 'center'
            va = 'bottom' if angle == 90 else 'top'
            rotation = 0
        elif 0 < angle < 180:
            ha = 'right' if angle > 90 else 'left'
            va = 'center'
            rotation = angle - 180 if angle > 90 else angle
        else:  # 180 < angle < 360
            ha = 'right' if angle < 270 else 'left'
            va = 'center'
            rotation = angle - 180 if angle < 270 else angle
        
        # 添加文本标签
        ax.text(label_x, label_y, f"{angle}°",
               ha=ha, va=va, fontsize=8,
               rotation=rotation, rotation_mode='anchor',
               bbox=dict(facecolor='white', alpha=0.7, edgecolor='none', pad=1),
               zorder=100)  # 确保显示在最前面
    
    # --- 设置整体外观 --- 
    ax.set_aspect(1.)
    radius_limit = max_radius * 1.15  # 增加边缘空间以容纳标签
    ax.set_xlim(-radius_limit, radius_limit)
    ax.set_ylim(-radius_limit, radius_limit)
    
    # 允许刻度超出绘图区域
    ax.axis["lon1"].major_ticks.set_clip_on(False)
    ax.axis["lon2"].major_ticks.set_clip_on(False)
    ax.axis["lat"].major_ticks.set_clip_on(False)
    
    # 启用网格
    ax.grid(True, linestyle='--', color='gray', alpha=0.5)
    
    # 创建辅助轴用于绘制数据
    aux_ax = ax.get_aux_axes(tr)
    aux_ax.set_ylim(0, max_radius)
    
    return ax, aux_ax

def load_and_plot_data(root_path, input_type="", show_plots=False, save_plots=False):
    json_file_name = "zero_info.json"
    test_json_file_name = "zero_test_info.json"
    print(f"root_path: {root_path}")
    process_path = os.path.join(root_path, "process_data")
    data_path = os.path.join(process_path, json_file_name)
    zero_info = json.load(open(data_path))
    
    # 检查是否存在测试数据文件
    test_data_path = os.path.join(process_path, test_json_file_name)
    has_test_data = os.path.exists(test_data_path)
    
    # 获取数据并过滤距离
    try:
        zero_hor_chn1_angle = np.array(zero_info["zero_hor_chn1_angle"])
        zero_data_azi = np.array(zero_info["zero_data_azi"]) * 0.01 + zero_hor_chn1_angle  # 转换到度
    except KeyError:
        # 如果没有zero_hor_chn1_angle，直接使用zero_data_azi
        zero_data_azi = np.array(zero_info["zero_data_azi"]) * 0.01  # 转换到度
    
    zero_data_dist = np.array(zero_info["zero_data_dist"])
    zero_data_area = np.array(zero_info["zero_data_area"])
    
    zero_angle = zero_info["zero_angle"]
    zero_high_refl_min = int(zero_info["zero_high_refl_min"])
    
    # 标准化角度，确保在0-360度范围内
    zero_data_azi = np.mod(zero_data_azi, 360)
    
    # 添加距离过滤
    max_dist = 600
    dist_mask = zero_data_dist <= max_dist
    zero_data_azi = zero_data_azi[dist_mask]
    zero_data_dist = zero_data_dist[dist_mask]
    zero_data_area = zero_data_area[dist_mask]  # 确保area数据也应用相同的距离过滤
    
    # 创建area > zero_high_refl_min的掩码，用于突出显示这些点
    area_mask = zero_data_area > zero_high_refl_min
    zero_data_azi_highlighted = zero_data_azi[area_mask]
    zero_data_dist_highlighted = zero_data_dist[area_mask]
    
    azi_start = zero_info["zero_azi_start"]
    azi_end = zero_info["zero_azi_end"]
    
    # 确保扇区角度在0-360度范围内
    azi_start = np.mod(azi_start, 360)
    azi_end = np.mod(azi_end, 360)
    if azi_start > azi_end:  # 如果跨越了0度线
        # 我们需要画两个扇区
        split_sector = True
        azi_ranges = [(azi_start, 360), (0, azi_end)]
    else:
        split_sector = False
        azi_ranges = [(azi_start, azi_end)]
    
    # 如果存在测试数据，加载测试数据
    if has_test_data:
        test_zero_info = json.load(open(test_data_path))
        
        # TODO: 添加1.639的读取，不写死
        test_zero_data_azi = np.array(test_zero_info["zero_data_azi"]) * 0.01 - 1.639
        
        test_zero_data_dist = np.array(test_zero_info["zero_data_dist"])
        test_zero_data_area = np.array(test_zero_info["zero_data_area"])
        
        # 标准化角度
        test_zero_data_azi = np.mod(test_zero_data_azi, 360)
        
        # 距离过滤
        test_dist_mask = test_zero_data_dist <= max_dist
        test_zero_data_azi = test_zero_data_azi[test_dist_mask]
        test_zero_data_dist = test_zero_data_dist[test_dist_mask]
        test_zero_data_area = test_zero_data_area[test_dist_mask]
        
        # 高反射率掩码
        test_area_mask = test_zero_data_area > zero_high_refl_min
        test_zero_data_azi_highlighted = test_zero_data_azi[test_area_mask]
        test_zero_data_dist_highlighted = test_zero_data_dist[test_area_mask]

    # 创建图形 - 调整图形大小和布局
    if has_test_data:
        fig = plt.figure(figsize=(18, 12))  # 更大的图形以容纳三个子图
        subplot_layout = 130  # 1行3列布局
    else:
        fig = plt.figure(figsize=(16, 8))  # 原始大小
        subplot_layout = 120  # 1行2列布局
    
    # 创建第一个极坐标图（原始数据）
    ax1, aux_ax1 = create_polar_axes(fig, subplot_layout + 1, max_radius=max_dist)
    ax1.set_title('Original Polar View', fontsize=12, fontweight='bold')
    
    # 创建第二个极坐标图（补偿后数据）
    ax2, aux_ax2 = create_polar_axes(fig, subplot_layout + 2, max_radius=max_dist)
    ax2.set_title('Compensated Polar View', fontsize=12, fontweight='bold')
    
    # 如果有测试数据，创建第三个极坐标图
    if has_test_data:
        ax3, aux_ax3 = create_polar_axes(fig, subplot_layout + 3, max_radius=max_dist)
        ax3.set_title('Test Data Polar View', fontsize=12, fontweight='bold')
        handles3, labels3 = [], []
    
    # 创建图例处理器来存储图例项目
    handles1, labels1 = [], []
    handles2, labels2 = [], []
    
    # 创建自定义的颜色映射 - 根据area值
    cmap_blue = LinearSegmentedColormap.from_list("BlueGradient", ['#DDEEFF', '#0000AA'])
    
    # 创建一个标准化器，将小于zero_high_refl_min的值映射到0-1范围
    norm = Normalize(vmin=0, vmax=zero_high_refl_min)
    
    # 第一个图：绘制扇形区域（可能需要绘制两个扇形）
    for azi_range in azi_ranges:
        start, end = azi_range
        theta = np.linspace(start, end, 100)
        r = np.ones_like(theta) * max_dist
        
        sector = aux_ax1.fill_between(theta, 0, r, 
                                     alpha=0.3,  # 提高透明度便于查看
                                     color='#FFD700')
        if len(handles1) == 0:  # 只添加一次到图例
            handles1.append(sector)
            labels1.append('Fitting Range')
    
    # 绘制数据点，使用颜色映射
    # 先绘制所有非高反射率的点，使用蓝色渐变
    low_refl_mask = zero_data_area <= zero_high_refl_min
    points1 = aux_ax1.scatter(
        zero_data_azi[low_refl_mask], 
        zero_data_dist[low_refl_mask], 
        s=2, 
        alpha=0.6,
        c=zero_data_area[low_refl_mask],  # 颜色根据area值
        cmap=cmap_blue,  # 使用蓝色渐变色图
        norm=norm  # 应用标准化
    )
    handles1.append(points1)
    labels1.append(f'Area ≤ {zero_high_refl_min}')
    
    # 然后绘制高反射率的点，使用红色
    highlight1 = aux_ax1.scatter(
        zero_data_azi_highlighted, 
        zero_data_dist_highlighted, 
        s=4, 
        alpha=0.8, 
        color='red'
    )
    handles1.append(highlight1)
    labels1.append(f'Area > {zero_high_refl_min}')
    
    # 为第二个图准备补偿后的数据
    # 注意角度补偿的计算方式
    zero_data_azi_comp = np.mod(zero_data_azi + (360 - zero_angle), 360)
    zero_data_azi_comp_highlighted = np.mod(zero_data_azi_highlighted + (360 - zero_angle), 360)
    
    # 计算补偿后的扇形区域边界
    print(f"azi_start: {azi_start}, zero_angle: {zero_angle}")
    compensated_start = np.mod(azi_start + (360 - zero_angle), 360)
    compensated_end = np.mod(azi_end + (360 - zero_angle), 360)
    
    # 处理补偿后的扇区，检查是否跨越了0度线
    if compensated_start > compensated_end:
        # 需要画两个扇区
        comp_azi_ranges = [(compensated_start, 360), (0, compensated_end)]
    else:
        comp_azi_ranges = [(compensated_start, compensated_end)]
    
    print(f"compensated_start: {compensated_start}, compensated_end: {compensated_end}")
    
    # 绘制补偿后的扇形区域（可能需要绘制两个扇形）
    for comp_azi_range in comp_azi_ranges:
        start, end = comp_azi_range
        theta_comp = np.linspace(start, end, 100)
        r_comp = np.ones_like(theta_comp) * max_dist
        
        sector = aux_ax2.fill_between(theta_comp, 0, r_comp, 
                                      alpha=0.3,  # 提高透明度便于查看
                                      color='#FFD700')
        if len(handles2) == 0:  # 只添加一次到图例
            handles2.append(sector)
            labels2.append('Compensated Fitting Range')
    
    # 绘制补偿后的数据点，同样使用颜色映射
    # 先绘制所有非高反射率的点，使用蓝色渐变
    points2 = aux_ax2.scatter(
        zero_data_azi_comp[low_refl_mask], 
        zero_data_dist[low_refl_mask], 
        s=2, 
        alpha=0.6,
        c=zero_data_area[low_refl_mask],  # 颜色根据area值
        cmap=cmap_blue,  # 使用蓝色渐变色图
        norm=norm  # 应用标准化
    )
    handles2.append(points2)
    labels2.append(f'Area ≤ {zero_high_refl_min}')
    
    # 然后绘制高反射率的点，使用红色
    highlight2 = aux_ax2.scatter(
        zero_data_azi_comp_highlighted, 
        zero_data_dist_highlighted, 
        s=4, 
        alpha=0.8, 
        color='red'
    )
    handles2.append(highlight2)
    labels2.append(f'Area > {zero_high_refl_min}')
    
    # 如果有测试数据，绘制第三个图（测试数据）
    if has_test_data:
        # 绘制非高反射率的测试数据点
        test_low_refl_mask = test_zero_data_area <= zero_high_refl_min
        points3 = aux_ax3.scatter(
            test_zero_data_azi[test_low_refl_mask], 
            test_zero_data_dist[test_low_refl_mask], 
            s=2, 
            alpha=0.6,
            c=test_zero_data_area[test_low_refl_mask],
            cmap=cmap_blue,
            norm=norm
        )
        handles3.append(points3)
        labels3.append(f'Test Area ≤ {zero_high_refl_min}')
        
        # 绘制高反射率的测试数据点
        highlight3 = aux_ax3.scatter(
            test_zero_data_azi_highlighted, 
            test_zero_data_dist_highlighted, 
            s=4, 
            alpha=0.8, 
            color='red'
        )
        handles3.append(highlight3)
        labels3.append(f'Test Area > {zero_high_refl_min}')
    
    # 手动添加图例到主轴
    ax1.legend(handles1, labels1, loc='upper right', fontsize=10, framealpha=0.9)
    ax2.legend(handles2, labels2, loc='upper right', fontsize=10, framealpha=0.9)
    if has_test_data:
        ax3.legend(handles3, labels3, loc='upper right', fontsize=10, framealpha=0.9)
    
    # 添加标题
    plt.suptitle('Lidar Zero Analysis', fontsize=16, fontweight='bold')
    plt.tight_layout()
    
    if save_plots:
        plt.savefig(os.path.join(process_path, 'zero_angle_analysis.png'), dpi=300)
    if show_plots:
        plt.show()
    plt.close()

def parse_arguments():
    parser = argparse.ArgumentParser(description='Process data files.')
    parser.add_argument('root_directory', type=str, nargs='?', default="/home/<USER>/development/projects",
                        help='Root directory path containing process data. Default is the debug path.')
    parser.add_argument('input_type', type=str, nargs='?', default="board,dynamic,static,refl,abs",
                        help="Input type to process, e.g., 'board,dyn,static,refl,abs'. Default includes all types.")
    parser.add_argument('--show', action='store_true', help='Show the plots in a window.')
    parser.add_argument('--save', action='store_true', help='Save the plots to files.')
    return parser.parse_args()

def main():
    args = parse_arguments()

    root_path = Path(args.root_directory)
    input_type = args.input_type
    show_plots = args.show
    save_plots = args.save

    if os.path.exists(root_path):
        load_and_plot_data(root_path, input_type, show_plots, save_plots)
    else:
        print("Error: The specified path does not exist.")

if __name__ == "__main__":
    main()
