﻿#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# /******************************************************************************
#  * Copyright 2025 RoboSense All rights reserved.
#  * Suteng Innovation Technology Co., Ltd. www.robosense.ai

#  * This software is provided to you directly by RoboSense and might
#  * only be used to access RoboSense LiDAR. Any compilation,
#  * modification, exploration, reproduction and redistribution are
#  * restricted without RoboSense's prior consent.

#  * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
#  * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
#  * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
#  * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
#  * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CO<PERSON>EQUENTIAL DAMAGES
#  * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
#  * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
#  * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
#  * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
#  * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
#  * POSSIBILITY OF SUCH DAMAGE.
#  *****************************************************************************/

import os
import sys
import pandas as pd
import glob
import argparse

# 集中定义所有标准
class MachineStandards:
    """存储所有机器类型的标准"""
    # 定义所有机器类型
    TYPES = ["正装版", "侧装版", "测绘版", "正装放宽版", "割草版", "ng下探"]
    
    # 定义积分值阈值
    THRESHOLDS = {
        "正装版": {
            "high_channels": range(69, 97),  # CH69~96
            "high_threshold": 0.6,           # 60%
            "low_channels": range(1, 69),    # CH1~68
            "low_threshold": 0.65,           # 65%
        },
        "侧装版": {
            "high_channels": range(1, 25),   # CH1~24
            "high_threshold": 0.6,           # 60%
            "low_channels": range(25, 97),   # CH25~96
            "low_threshold": 0.65,           # 65%
        },
        "测绘版": {
            "uncontrolled_channels": list(range(1, 17)) + list(range(85, 97)),  # CH1~16, CH85~96
            "uncontrolled_pattern": lambda x: x % 4 == 0,  # 其余4倍数通道
            "normal_threshold": 0.7,         # 70%
            "ng_min_threshold": 0.6,         # 60%
        },
        "正装放宽版": {
            "high_channels": range(69, 97),  # CH69~96
            "high_threshold": 0.54,          # 60% * 0.9 = 54%
            "low_channels": range(1, 69),    # CH1~68
            "low_threshold": 0.585,          # 65% * 0.9 = 58.5%
        },
        "割草版": {
            "high_channels": range(69, 97),  # CH69~96
            "high_threshold": 0.54,          # 60% * 0.9 = 54%
            "low_channels": range(1, 49),    # CH1~48
            "low_threshold": 0.585,          # 65% * 0.9 = 58.5%
            "uncontrolled_channels": range(49, 97),  # CH49~96不管控
        },
        "ng下探": {
            "high_channels": range(69, 97),  # CH69~96
            "high_threshold": 0.45,          # 45%
            "low_channels": range(1, 69),    # CH1~68
            "low_threshold": 0.50,           # 50%
            "extra_ng_channels": 5,          # 额外允许5个或以上NG通道
        }
    }
    
    # 定义允许的NG通道
    ALLOWED_NG_CHANNELS = {
        "正装版": [4, 8, 12, 69] + list(range(80, 97)),
        "侧装版": list(range(1, 9)) + [69, 89, 93],
        "测绘版": [],  # 测绘版无特定NG通道限制
        "正装放宽版": [4, 8, 12, 69] + list(range(80, 97)),  # 与正装版相同
        "割草版": [4, 8, 12, 69] + list(range(80, 97)),      # 与正装版相同
        "ng下探": [4, 8, 12, 69] + list(range(80, 97))       # 基础NG通道与正装版相同
    }
    
    # 定义NG通道数量限制
    NG_LIMITS = {
        "正装版": 5,
        "侧装版": 5,
        "测绘版": 2,
        "正装放宽版": 5,  # 与正装版相同
        "割草版": 5,      # 与正装版相同
        "ng下探": 10      # 基础NG通道 + 额外5个或以上
    }

class AreaChecker:
    """区域积分值检查器"""
    def __init__(self, machine_type):
        self.machine_type = machine_type
        if machine_type not in MachineStandards.TYPES:
            raise ValueError(f"不支持的机器类型: {machine_type}")
    
    def check_channel(self, channel, rate):
        """检查单个通道是否通过标准"""
        # 转换为整数
        chn = int(channel)
        rate_value = float(rate)
        
        if self.machine_type in ["正装版", "正装放宽版", "ng下探"]:
            # 获取对应阈值
            standards = MachineStandards.THRESHOLDS[self.machine_type]
            threshold = standards["high_threshold"] if chn in standards["high_channels"] else standards["low_threshold"]
            
            # 检查是否通过阈值
            if rate_value >= threshold:
                return True
            
            # 检查是否在允许的NG通道列表中
            if chn in MachineStandards.ALLOWED_NG_CHANNELS[self.machine_type]:
                return False  # 是允许的NG通道，标记为fail
            
            return False  # 不在允许的NG通道列表中，整体会被判为ng
            
        elif self.machine_type == "割草版":
            standards = MachineStandards.THRESHOLDS[self.machine_type]
            
            # 检查是否在不管控通道范围内
            if chn in standards["uncontrolled_channels"]:
                return True
            
            threshold = standards["high_threshold"] if chn in standards["high_channels"] else standards["low_threshold"]
            
            # 检查是否通过阈值
            if rate_value >= threshold:
                return True
            
            # 检查是否在允许的NG通道列表中
            if chn in MachineStandards.ALLOWED_NG_CHANNELS[self.machine_type]:
                return False  # 是允许的NG通道，标记为fail
            
            return False  # 不在允许的NG通道列表中，整体会被判为ng
            
        elif self.machine_type == "侧装版":
            # 获取对应阈值
            standards = MachineStandards.THRESHOLDS["侧装版"]
            threshold = standards["high_threshold"] if chn in standards["high_channels"] else standards["low_threshold"]
            
            # 检查是否通过阈值
            if rate_value >= threshold:
                return True
            
            # 检查是否在允许的NG通道列表中
            if chn in MachineStandards.ALLOWED_NG_CHANNELS["侧装版"]:
                return False  # 是允许的NG通道，标记为fail
            
            return False  # 不在允许的NG通道列表中，整体会被判为ng
            
        elif self.machine_type == "测绘版":
            standards = MachineStandards.THRESHOLDS["测绘版"]
            
            # 不管控的通道
            if (chn in standards["uncontrolled_channels"] or 
                standards["uncontrolled_pattern"](chn)):
                return True
            
            # 检查是否通过阈值
            if rate_value >= standards["normal_threshold"]:
                return True
            
            # 可以作为NG通道，但数量有限制
            if rate_value >= standards["ng_min_threshold"]:
                return False
            
            return False  # 积分值低于最低要求，整体会被判为ng
        
        return True  # 默认通过

    def count_ng_channels(self, df):
        """计算NG通道数量"""
        ng_count = 0
        extra_ng_count = 0  # 用于ng下探版本的额外NG通道计数
        
        for _, row in df.iterrows():
            chn = int(row["chn"])
            rate = float(row["rate"])
            
            if self.machine_type in ["正装版", "正装放宽版", "ng下探"]:
                standards = MachineStandards.THRESHOLDS[self.machine_type]
                threshold = standards["high_threshold"] if chn in standards["high_channels"] else standards["low_threshold"]

                # 检查是否是NG通道
                if rate < threshold:
                    if chn in MachineStandards.ALLOWED_NG_CHANNELS[self.machine_type]:
                        ng_count += 1
                    elif self.machine_type == "ng下探":
                        extra_ng_count += 1

            elif self.machine_type == "割草版":
                standards = MachineStandards.THRESHOLDS[self.machine_type]
                
                # 跳过不管控通道
                if chn in standards["uncontrolled_channels"]:
                    continue

                threshold = standards["high_threshold"] if chn in standards["high_channels"] else standards["low_threshold"]

                # 只计算允许的NG通道
                if chn in MachineStandards.ALLOWED_NG_CHANNELS[self.machine_type] and rate < threshold:
                    ng_count += 1

            elif self.machine_type == "侧装版":
                standards = MachineStandards.THRESHOLDS["侧装版"]
                threshold = standards["high_threshold"] if chn in standards["high_channels"] else standards["low_threshold"]
                
                # 只计算允许的NG通道
                if chn in MachineStandards.ALLOWED_NG_CHANNELS["侧装版"] and rate < threshold:
                    ng_count += 1
                    
            elif self.machine_type == "测绘版":
                standards = MachineStandards.THRESHOLDS["测绘版"]
                
                # 排除不管控的通道
                if (chn in standards["uncontrolled_channels"] or 
                    standards["uncontrolled_pattern"](chn)):
                    continue
                
                # 计算积分值在60%~70%之间的通道
                if standards["ng_min_threshold"] <= rate < standards["normal_threshold"]:
                    ng_count += 1
        
        # 对于ng下探版本，需要考虑额外的NG通道要求
        if self.machine_type == "ng下探":
            if extra_ng_count < MachineStandards.THRESHOLDS["ng下探"]["extra_ng_channels"]:
                return ng_count  # 如果额外NG通道数量不足，则只返回基础NG通道数
            return ng_count + extra_ng_count  # 否则返回总NG通道数
        
        return ng_count

    def check_file_status(self, df):
        """检查文件整体状态"""
        # 计算NG通道数量
        ng_count = self.count_ng_channels(df)
        
        # 检查NG通道数量是否超限
        if ng_count > MachineStandards.NG_LIMITS[self.machine_type]:
            return "ng"
        
        if self.machine_type in ["正装版", "正装放宽版", "ng下探"]:
            standards = MachineStandards.THRESHOLDS[self.machine_type]
            
            # 检查是否有不符合标准且不在允许NG列表中的通道
            for _, row in df.iterrows():
                chn = int(row["chn"])
                rate = float(row["rate"])
                
                # 获取对应阈值
                threshold = standards["high_threshold"] if chn in standards["high_channels"] else standards["low_threshold"]
                
                # 如果积分值低于标准且不在允许的NG通道列表中
                if rate < threshold and chn not in MachineStandards.ALLOWED_NG_CHANNELS[self.machine_type]:
                    if self.machine_type != "ng下探":
                        return "ng"
                    # 对于ng下探版本，需要检查额外NG通道数量是否满足要求
                    extra_ng_count = sum(1 for _, r in df.iterrows() 
                                      if int(r["chn"]) not in MachineStandards.ALLOWED_NG_CHANNELS[self.machine_type]
                                      and float(r["rate"]) < (standards["high_threshold"] if int(r["chn"]) in standards["high_channels"] 
                                                            else standards["low_threshold"]))
                    if extra_ng_count < standards["extra_ng_channels"]:
                        return "ng"
        
        elif self.machine_type == "割草版":
            standards = MachineStandards.THRESHOLDS[self.machine_type]
            
            # 检查是否有不符合标准且不在允许NG列表中的通道
            for _, row in df.iterrows():
                chn = int(row["chn"])
                rate = float(row["rate"])
                
                # 跳过不管控通道
                if chn in standards["uncontrolled_channels"]:
                    continue
                
                # 获取对应阈值
                threshold = standards["high_threshold"] if chn in standards["high_channels"] else standards["low_threshold"]
                
                # 如果积分值低于标准且不在允许的NG通道列表中
                if rate < threshold and chn not in MachineStandards.ALLOWED_NG_CHANNELS[self.machine_type]:
                    return "ng"
        
        elif self.machine_type == "侧装版":
            standards = MachineStandards.THRESHOLDS["侧装版"]
            
            # 检查是否有不符合标准且不在允许NG列表中的通道
            for _, row in df.iterrows():
                chn = int(row["chn"])
                rate = float(row["rate"])
                
                # 获取对应阈值
                threshold = standards["high_threshold"] if chn in standards["high_channels"] else standards["low_threshold"]
                
                # 如果积分值低于标准且不在允许的NG通道列表中
                if rate < threshold and chn not in MachineStandards.ALLOWED_NG_CHANNELS["侧装版"]:
                    return "ng"
        
        elif self.machine_type == "测绘版":
            standards = MachineStandards.THRESHOLDS["测绘版"]
            
            for _, row in df.iterrows():
                chn = int(row["chn"])
                rate = float(row["rate"])
                
                # 排除不管控的通道
                if (chn in standards["uncontrolled_channels"] or 
                    standards["uncontrolled_pattern"](chn)):
                    continue
                
                # 如果积分值低于最低要求
                if rate < standards["ng_min_threshold"]:
                    return "ng"
        
        # 默认通过
        return "pass"

def process_files(input_folder, output_folder, machine_type):
    """处理文件夹中的所有CSV文件"""
    # 确保输出文件夹存在
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)
    
    # 获取所有CSV文件
    csv_files = glob.glob(os.path.join(input_folder, "*.csv"))
    
    # 创建检查器
    checker = AreaChecker(machine_type)
    
    for file_path in csv_files:
        file_name = os.path.basename(file_path)
        
        # 读取CSV文件，跳过第一行说明
        with open(file_path, 'r', encoding='utf-8') as f:
            first_line = f.readline()  # 读取并保存第一行说明行
            data = pd.read_csv(f)  # 从第二行开始读取
        
        # 创建数据副本进行处理
        df = data.copy()
        
        # 判断文件整体状态
        file_status = checker.check_file_status(df)
        
        # 修改文件名，在文件名末尾添加新的下划线和状态
        file_base = os.path.splitext(file_name)[0]  # 获取不带扩展名的文件名
        new_file_name = f"{file_base}_{file_status}.csv"
        
        output_path = os.path.join(output_folder, new_file_name)
        
        # 添加新的recheck列来存储检查结果，保持原始pass列不变
        df['recheck'] = ''
        for index, row in df.iterrows():
            df.at[index, 'recheck'] = '' if checker.check_channel(row["chn"], row["rate"]) else 'fail'
        
        # 写入到输出文件
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(first_line)  # 写入原始说明行
            df.to_csv(f, index=False)
        
        print(f"已处理: {file_name} -> {new_file_name}")

def main():
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='按照指定标准重新检查区域积分值数据')
    parser.add_argument('input_folder', help='输入CSV文件所在文件夹')
    parser.add_argument('output_folder', help='输出CSV文件存放文件夹')
    parser.add_argument('--type', choices=MachineStandards.TYPES, default='正装版', help='机器类别')
    
    args = parser.parse_args()
    
    print(f"开始处理 {args.type} 类型的区域积分值数据...")
    process_files(args.input_folder, args.output_folder, args.type)
    print("处理完成！")

if __name__ == "__main__":
    main()
