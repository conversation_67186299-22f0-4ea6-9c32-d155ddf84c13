﻿# /******************************************************************************
#  * Copyright 2024 RoboSense All rights reserved.
#  * Suteng Innovation Technology Co., Ltd. www.robosense.ai

#  * This software is provided to you directly by RoboSense and might
#  * only be used to access RoboSense LiDAR. Any compilation,
#  * modification, exploration, reproduction and redistribution are
#  * restricted without RoboSense's prior consent.

#  * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
#  * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
#  * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
#  * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
#  * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONS<PERSON>QUE<PERSON>IAL DAMAGES
#  * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
#  * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
#  * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
#  * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
#  * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
#  * POSSIBILITY OF SUCH DAMAGE.
#  *****************************************************************************/

import pandas as pd
import matplotlib.pyplot as plt
import os
import numpy as np
import itertools
import json
import argparse
from pathlib import Path

from matplotlib import rcParams

# rcParams['font.family'] = 'sans-serif'
# rcParams['font.sans-serif'] = ['SimHei']  # 指定默认字体为黑体
# rcParams['axes.unicode_minus'] = False  # 解决保存图像是负号'-'显示为方块的问题

def load_and_plot_data(root_path, chn=1, input_type="board", show_plots=False, save_plots=False):
    # Define file paths based on the channel number
    # print all arg
    print(f"root_path: {root_path}")
    process_path = os.path.join(root_path, "process_data")
    print(f"chn: {chn}")
    print(f"input_type: {input_type}")
    print(f"show_plots: {show_plots}")
    print(f"save_plots: {save_plots}")

    if show_plots is False and save_plots is False:
        print("Error: Both show and save options area False")
        return

    dist_area_path = os.path.join(process_path, f'chn{chn}_dist_area.csv')
    info_path = os.path.join(process_path, f'chn{chn}_info.json')

    # Read the CSV files into pandas DataFrames
    chn_dist_area = pd.read_csv(dist_area_path)
    # chn_board_info = pd.read_csv(board_info_path)
    chn_info_json = json.load(open(info_path))
    # chn_board_info = pd.DataFrame(chn_info_json["board_info"])

    # Print a summary of the data
    print(f"chn{chn}_dist_area DataFrame:")
    print(chn_dist_area.head())
    print(f"\n chn{chn}_board_info DataFrame:")
    # print(chn_board_info.head())

    # Extract columns by name
    dist_vec = chn_dist_area["dist_vec"].to_numpy()
    area_vec = chn_dist_area["area_vec"].to_numpy()
    amp_vec = chn_dist_area["amp_vec"].to_numpy()
    code_mark_vec = chn_dist_area["code_mark_vec"].to_numpy()
    dist_comped_vec = chn_dist_area["dist_comped_vec"].to_numpy()

    dist_vec = dist_vec[~np.isnan(dist_vec)]
    amp_vec = amp_vec[~np.isnan(amp_vec)]
    code_mark_vec = code_mark_vec[~np.isnan(code_mark_vec)]
    area_vec = area_vec[~np.isnan(area_vec)]
    dist_comped_vec = dist_comped_vec[~np.isnan(dist_comped_vec)]

    dynamic_comp_result_0 = chn_dist_area["dynamic_comp_result_0"].to_numpy()
    dynamic_comp_result_1 = chn_dist_area["dynamic_comp_result_1"].to_numpy()
    dynamic_board_id = int(chn_info_json["dynamic_board_id"])
    dynamic_start_index = int(chn_info_json["dynamic_start_index"])
    dynamic_min_dist = chn_info_json["dynamic_min_dist"]
    dynamic_code_mark = chn_info_json["dynamic_code_mark"]

    static_board_id = int(chn_info_json["static_board_id"])
    static_comp = chn_info_json["static_comp"]

    refl_board_id_vec = chn_info_json["refl_board_id_vec"]
    abs_board_id_vec = chn_info_json["abs_board_id_vec"]

    board_20m_id = int(chn_info_json["board_20m_id"])
    board_10m_id = int(chn_info_json["board_10m_id"])

    abs_coe_k_vec = chn_info_json["abs_coe_k_vec"]
    abs_coe_b_vec = chn_info_json["abs_coe_b_vec"]
    dist_test_vec = chn_info_json["dist_test_vec"]

    total_length = len(dist_vec)
    angle = np.linspace(0, 360, total_length)

    unique_codes = np.unique(code_mark_vec)

    if "board" in input_type:
        # Track which board IDs have already been plotted
        print("Board IDs: ", chn_info_json["board"].keys())
        plotted_boards = set()
        colors = itertools.cycle(plt.cm.tab20.colors)
        fig, axes = plt.subplots(2, 1, figsize=(19.2, 10.8))

        # 创建双x轴
        ax1_twin = axes[0].twiny()
        ax2_twin = axes[1].twiny()

        # 设置index轴的范围
        index_array = np.arange(total_length)

        # Subplot 1: Scatter plot of dist_vec
        color = '#B1B1B1'  # 灰色
        axes[0].scatter(angle, dist_comped_vec, label=f'dist comped', color=color, s=1)
        ax1_twin.scatter(index_array, dist_comped_vec, alpha=0)  # 透明点用于设置范围


        # Scatter plot for classified data
        for i, code in enumerate(unique_codes):
            mask = code_mark_vec == code
            color = '#FB4141'
            color_text = 'red'
            if code == 1:
                color = '#5DB996'
                color_text = 'green'
            axes[0].scatter(angle[mask], dist_vec[mask], label=f'dist {color_text} code {code}', color=color, s=1)
            # 在twin轴上也画点（透明）以保持范围一致
            ax1_twin.scatter(index_array[mask], dist_vec[mask], alpha=0)
            ax2_twin.scatter(index_array[mask], area_vec[mask], alpha=0)
            axes[1].scatter(angle[mask], area_vec[mask], label=f'area {color_text} code {code}', color=color, s=1)
        # 设置标签
        axes[0].set_title(f'chn{chn} dist_vec Scatter Plot')
        ax1_twin.set_xlabel('Index')
        axes[0].set_xlabel('Angle (°)')
        axes[0].set_title(f'chn{chn} dist_vec Scatter Plot')
        axes[0].grid()

        axes[1].set_title(f'chn{chn} area_vec Scatter Plot')
        ax2_twin.set_xlabel('Index')
        axes[1].set_xlabel('Angle (°)')
        axes[1].set_title(f'chn{chn} area_vec Scatter Plot')

        # 调整布局以防止标签重叠
        plt.tight_layout()
        axes[1].grid()

        # Highlight board start and end positions
        for board_id in chn_info_json["board"]:
            board_info = chn_info_json["board"][board_id]
            if board_info['is_found'] == 1:
                start_idx = int(board_info['detected_data_start'])
                end_idx = int(board_info['detected_data_end'])
                if 0 <= start_idx < total_length and 0 <= end_idx < total_length:
                    color = next(colors)
                    axes[0].axvspan(angle[start_idx], angle[end_idx], color=color, alpha=0.3, label=f'Board {int(board_id)} ({round(angle[start_idx], 2)}-{round(angle[end_idx], 2)})')
                    axes[1].axvspan(angle[start_idx], angle[end_idx], color=color, alpha=0.3, label=f'Board {int(board_id)} ({round(angle[start_idx], 2)}-{round(angle[end_idx], 2)})')
                    # 在最底下位置显示text board_id
                    axes[0].text(angle[int(start_idx+(end_idx-start_idx)/2)], 0, f'{int(board_id)}', fontsize=9, ha='center', va='bottom')
                    axes[1].text(angle[int(start_idx+(end_idx-start_idx)/2)], 0, f'{int(board_id)}', fontsize=9, ha='center', va='bottom')
        axes[0].legend()
        axes[1].legend()
        axes[0].set_ylim(-200, 5000)
        plt.tight_layout()
        if save_plots:
            plt.savefig(os.path.join(root_path, "detect_board", f"chn{chn}_detect_board.png"))
        if show_plots:
            plt.show()

    if "dynamic" in input_type and dynamic_board_id is not None:
        plt.clf
        # Filter dynamic data only for dynamic_board_id
        # dyn_board_info = chn_board_info[(chn_board_info['board_id'] == dynamic_board_id) & (chn_board_info['is_found'] == 1)]
        dyn_board_info = chn_info_json["board"][str(dynamic_board_id)]
        board_20m_info = chn_info_json["board"][str(board_20m_id)]
        board_10m_info = chn_info_json["board"][str(board_10m_id)]
        if dyn_board_info is None or dyn_board_info['is_found'] == 0 or board_20m_info is None:
            print(f"Error: Board {dynamic_board_id} not found in the data.")
            return

        index_20m_10refl = int(board_20m_info["refl_board"]["10"]["area_index"])
        index_20m_40refl = int(board_20m_info["refl_board"]["40"]["area_index"])
        index_20m_90refl = int(board_20m_info["refl_board"]["90"]["area_index"])

        index_10m_10refl = int(board_10m_info["refl_board"]["10"]["area_index"])
        index_10m_40refl = int(board_10m_info["refl_board"]["40"]["area_index"])
        index_10m_90refl = int(board_10m_info["refl_board"]["90"]["area_index"])

        start_idx = dynamic_start_index
        end_idx = int(dyn_board_info['detected_data_end'])
        if 0 <= start_idx < total_length and 0 <= end_idx <= total_length:
            # Generate area steps with step=1
            # 如果dynamic_code_mark为0，则dynamic_comp_result = dynamic_comp_result_0，否则为dynamic_comp_result_1
            dynamic_comp_result = dynamic_comp_result_0
            if dynamic_code_mark == 1:
                dynamic_comp_result = dynamic_comp_result_1
            area_fit = np.arange(0, len(dynamic_comp_result), 64)
            recovered_fit_result = dynamic_comp_result[::64].copy()
            recovered_fit_result = np.round(recovered_fit_result)

            # Extract corresponding distance and area data
            area_orig = area_vec[start_idx:end_idx + 1]
            dist_orig = dist_vec[start_idx:end_idx + 1]-dyn_board_info["distance_05cm"] - dynamic_min_dist
            code_mark_orig = code_mark_vec[start_idx:end_idx + 1]

            points_20m_10refl_area_vec = area_vec[index_20m_10refl-50: index_20m_10refl+50]
            points_20m_10refl_dist_vec = dist_vec[index_20m_10refl-50: index_20m_10refl+50]- board_20m_info["refl_board"]["10"]["distance_05cm"] - dynamic_min_dist
            code_mark_20m_10refl = code_mark_vec[index_20m_10refl-50: index_20m_10refl+50]

            points_20m_40refl_area_vec = area_vec[index_20m_40refl-50: index_20m_40refl+50]
            points_20m_40refl_dist_vec = dist_vec[index_20m_40refl-50: index_20m_40refl+50]- board_20m_info["distance_05cm"] - dynamic_min_dist
            code_mark_20m_40refl = code_mark_vec[index_20m_40refl-50: index_20m_40refl+50]

            points_20m_90refl_area_vec = area_vec[index_20m_90refl-50: index_20m_90refl+50]
            points_20m_90refl_dist_vec = dist_vec[index_20m_90refl-50: index_20m_90refl+50]- board_20m_info["refl_board"]["90"]["distance_05cm"] - dynamic_min_dist
            code_mark_20m_90refl = code_mark_vec[index_20m_90refl-50: index_20m_90refl+50]

            points_10m_10refl_area_vec = area_vec[index_10m_10refl-50: index_10m_10refl+50]
            points_10m_10refl_dist_vec = dist_vec[index_10m_10refl-50: index_10m_10refl+50]- board_10m_info["refl_board"]["10"]["distance_05cm"] - dynamic_min_dist
            code_mark_10m_10refl = code_mark_vec[index_10m_10refl-50: index_10m_10refl+50]

            points_10m_40refl_area_vec = area_vec[index_10m_40refl-50: index_10m_40refl+50]
            points_10m_40refl_dist_vec = dist_vec[index_10m_40refl-50: index_10m_40refl+50]- board_10m_info["refl_board"]["40"]["distance_05cm"] - dynamic_min_dist
            code_mark_10m_40refl = code_mark_vec[index_10m_40refl-50: index_10m_40refl+50]

            points_10m_90refl_area_vec = area_vec[index_10m_90refl-50: index_10m_90refl+50]
            points_10m_90refl_dist_vec = dist_vec[index_10m_90refl-50: index_10m_90refl+50]- board_10m_info["distance_05cm"] - dynamic_min_dist
            code_mark_10m_90refl = code_mark_vec[index_10m_90refl-50: index_10m_90refl+50]

            # Unique code marks for classification
            colors = plt.cm.tab20(np.linspace(0, 1, len(unique_codes)+20))

            # Scatter plot for classified data
            plt.figure(figsize=(19.2, 10.8))
            for i, code in enumerate(unique_codes):
                mask = code_mark_orig == code
                mask_20m_10refl = code_mark_20m_10refl == code
                mask_20m_40refl = code_mark_20m_40refl == code
                mask_20m_90refl = code_mark_20m_90refl == code
                mask_10m_10refl = code_mark_10m_10refl == code
                mask_10m_40refl = code_mark_10m_40refl == code
                mask_10m_90refl = code_mark_10m_90refl == code
                plt.scatter(area_orig[mask], dist_orig[mask], label=f'Code {code}', color=colors[i+2], s=1)
                # 20m 10refl
                plt.scatter(points_20m_10refl_area_vec[mask_20m_10refl], points_20m_10refl_dist_vec[mask_20m_10refl], label=f'20m 10refl Code {code}', color=colors[i+4], s=5)

                # 20m 40refl
                plt.scatter(points_20m_40refl_area_vec[mask_20m_40refl], points_20m_40refl_dist_vec[mask_20m_40refl], label=f'20m 40refl Code {code}', color=colors[i+6], s=5)

                # 20m 90refl
                plt.scatter(points_20m_90refl_area_vec[mask_20m_90refl], points_20m_90refl_dist_vec[mask_20m_90refl], label=f'20m 90refl Code {code}', color=colors[i+8], s=5)

                # 10m 10refl
                plt.scatter(points_10m_10refl_area_vec[mask_10m_10refl], points_10m_10refl_dist_vec[mask_10m_10refl], label=f'10m 10refl Code {code}', color=colors[i+10], s=5)

                # 10m 40refl
                plt.scatter(points_10m_40refl_area_vec[mask_10m_40refl], points_10m_40refl_dist_vec[mask_10m_40refl], label=f'10m 40refl Code {code}', color=colors[i+12], s=5)

                # 10m 90refl
                plt.scatter(points_10m_90refl_area_vec[mask_10m_90refl], points_10m_90refl_dist_vec[mask_10m_90refl], label=f'10m 90refl Code {code}', color=colors[i+14], s=5)

            # 添加各个反射率点的标签
            # 20m 10refl
            if np.any(code_mark_20m_10refl):
                center_area_20m_10refl = np.mean(points_20m_10refl_area_vec)
                center_dist_20m_10refl = np.mean(points_20m_10refl_dist_vec)
                plt.text(center_area_20m_10refl, center_dist_20m_10refl, "20m-10%", fontsize=8, ha='center', va='center',
                        bbox=dict(facecolor='white', alpha=0.7, edgecolor='none', pad=1))

            # 20m 40refl
            if np.any(code_mark_20m_40refl):
                center_area_20m_40refl = np.mean(points_20m_40refl_area_vec)
                center_dist_20m_40refl = np.mean(points_20m_40refl_dist_vec)
                plt.text(center_area_20m_40refl, center_dist_20m_40refl, "20m-40%", fontsize=8, ha='center', va='center',
                        bbox=dict(facecolor='white', alpha=0.7, edgecolor='none', pad=1))

            # 20m 90refl
            if np.any(code_mark_20m_90refl):
                center_area_20m_90refl = np.mean(points_20m_90refl_area_vec)
                center_dist_20m_90refl = np.mean(points_20m_90refl_dist_vec)
                plt.text(center_area_20m_90refl, center_dist_20m_90refl, "20m-90%", fontsize=8, ha='center', va='center',
                        bbox=dict(facecolor='white', alpha=0.7, edgecolor='none', pad=1))

            # 10m 10refl
            if np.any(code_mark_10m_10refl):
                center_area_10m_10refl = np.mean(points_10m_10refl_area_vec)
                center_dist_10m_10refl = np.mean(points_10m_10refl_dist_vec)
                plt.text(center_area_10m_10refl, center_dist_10m_10refl, "10m-10%", fontsize=8, ha='center', va='center',
                        bbox=dict(facecolor='white', alpha=0.7, edgecolor='none', pad=1))

            # 10m 40refl
            if np.any(code_mark_10m_40refl):
                center_area_10m_40refl = np.mean(points_10m_40refl_area_vec)
                center_dist_10m_40refl = np.mean(points_10m_40refl_dist_vec)
                plt.text(center_area_10m_40refl, center_dist_10m_40refl, "10m-40%", fontsize=8, ha='center', va='center',
                        bbox=dict(facecolor='white', alpha=0.7, edgecolor='none', pad=1))

            # 10m 90refl
            if np.any(code_mark_10m_90refl):
                center_area_10m_90refl = np.mean(points_10m_90refl_area_vec)
                center_dist_10m_90refl = np.mean(points_10m_90refl_dist_vec)
                plt.text(center_area_10m_90refl, center_dist_10m_90refl, "10m-90%", fontsize=8, ha='center', va='center',
                        bbox=dict(facecolor='white', alpha=0.7, edgecolor='none', pad=1))

            # Plot the recovered fit result
            plt.scatter(area_fit, recovered_fit_result, label='Recovered Fit Result', color=colors[0], s=5)
            plt.ylim(-40, 125)
            plt.title(f'Chn{chn} Dynamic Data (Board {dynamic_board_id}): Area vs Distance')
            plt.xlabel('Area')
            plt.ylabel('Distance')
            plt.legend()
            plt.grid()
        if save_plots:
            plt.savefig(os.path.join(root_path, "dynamic", f"chn{chn}_dynamic.png"))
        if show_plots:
            plt.show()


    if "static" in input_type:
        plt.clf
        # static_board_info = chn_board_info[(chn_board_info['board_id'] == static_board_id) & (chn_board_info['is_found'] == 1)]
        static_board_info = chn_info_json["board"][str(static_board_id)]
        if static_board_info["is_found"] != 1:
            print(f"Error: Board {static_board_id} not found in the data.")
            return

        start_idx = int(static_board_info['detected_data_start'])
        end_idx = int(static_board_info['detected_data_end'])
        distance = int(static_board_info['distance_05cm'])
        if 0 <= start_idx < total_length and 0 <= end_idx <= total_length:
            # Extract corresponding distance and area data
            area_orig = area_vec[start_idx:end_idx + 1]
            dist_orig = dist_vec[start_idx:end_idx + 1].copy()
            code_mark_orig = code_mark_vec[start_idx:end_idx + 1]
            for i in range(len(dist_orig)):
                code_mark = code_mark_orig[i]
                dyn_comp = dynamic_comp_result_0[int(area_orig[i])]
                if code_mark == 1:
                    dyn_comp = dynamic_comp_result_1[int(area_orig[i])]
                dist_orig[i] = dist_orig[i] - dyn_comp - distance

            data_index = np.arange(start_idx, end_idx + 1)
            colors = plt.cm.tab20(np.linspace(0, 1, len(unique_codes)))

            # Scatter plot for classified data
            # two plots: one plot dist_orig , another plot area_orig
            fig, axes = plt.subplots(2, 1, figsize=(10, 10))
            axes[0].set_title(f'Static Data (Board {static_board_id}): Distance vs Code')
            axes[0].set_xlabel('Angle (°)')
            axes[0].set_ylabel('Distance')
            axes[0].grid()

            axes[1].set_title(f'Static Data (Board {static_board_id}): Area vs Code')
            axes[1].set_xlabel('Angle (°)')
            axes[1].set_ylabel('Area')
            axes[1].grid()

            for i, code in enumerate(unique_codes):
                mask = code_mark_orig == code
                angle_value = angle[data_index[mask]]
                axes[0].plot(angle_value,dist_orig[mask], label=f'Code {code}', color=colors[i], zorder=1)
                axes[1].plot(angle_value,area_orig[mask], label=f'Code {code}', color=colors[i], zorder=1)

            comp_mean = (static_comp[0] + static_comp[1]) / 2
            axes[0].set_ylim(comp_mean-50,comp_mean+100)
            # axes[1].set_ylim(10000, 20000)

            axes[0].scatter(angle[int(len(dist_orig)/2+start_idx)], static_comp[0], label=f'Static comp 0 {static_comp[0]:.2f}', color='red', s=15, zorder=2)
            axes[0].scatter(angle[int(len(dist_orig)/2+start_idx)], static_comp[1], label=f'Static comp 1 {static_comp[1]:.2f}', color='red', s=15, zorder=2)

            axes[0].legend()
            axes[1].legend()
            plt.tight_layout()
        if save_plots:
            plt.savefig(os.path.join(root_path, "static", f"chn{chn}_static.png"))
        if show_plots:
            plt.show()

    def plot_refl_amp_data(chn_info_json, board_id_vec, refl_amp_type, root_path, show_plots, save_plots):
        fig, axes = plt.subplots(2, 3, figsize=(19.2, 10.8))
        axes = axes.flatten()  # Flatten the 2x3 axes array for easy iteration
        subplot_idx = 0
        refl_vec = ["10", "40", "90", "255"]
        sensor_points = {sensor: {"x": [], "y": []} for sensor in refl_vec}

        print(f"{refl_amp_type.capitalize()} Board IDs: {board_id_vec}")

        for board_id in board_id_vec:
            if subplot_idx >= 5:
                break
            board_info = chn_info_json["board"][str(board_id)]
            start_index = int(board_info['detected_data_start'])
            end_index = int(board_info['detected_data_end'])
            center_index = int((end_index - start_index) / 2 + start_index)
            center_dist = dist_comped_vec[center_index] * 5 / 1000

            data_orig = chn_dist_area[refl_amp_type + "_vec"][start_index:end_index + 1].to_numpy()
            angle_orig = angle[start_index:end_index + 1]
            code_mark_orig = code_mark_vec[start_index:end_index + 1]

            for i, code in enumerate(unique_codes):
                mask = code_mark_orig == code
                color = '#f87070'
                color_text = 'red'
                if code == 1:
                    color = '#52616a'
                    color_text = 'gray'
                axes[subplot_idx].plot(angle_orig[mask], data_orig[mask], label=f'raw code {code} {color_text}',alpha=0.8, color=color, zorder=1)

            for refl in refl_vec:
                if refl not in board_info["refl_board"]:
                    continue
                refl_board = board_info["refl_board"][refl]
                center_index = refl_board[f"{refl_amp_type}_index"]
                center_angle = angle[int(center_index)]
                center_value = refl_board[f"{refl_amp_type}_mean_code_1"]
                dist_value = refl_board["dist_mean"] * 5 / 1000  # Scale to meters
                axes[subplot_idx].scatter([center_angle], [center_value], s=25, label=f'Refl {refl}', zorder=2)
                axes[subplot_idx].text(center_angle, center_value-center_value*0.15, f'Refl {refl}', fontsize=9, ha='center', va='top')
                sensor_points[refl]["x"].append(dist_value)
                sensor_points[refl]["y"].append(center_value)

            refl_90_40_factor = 0
            if "90" in board_info["refl_board"] and "40" in board_info["refl_board"]:
                refl_40_value = board_info["refl_board"]["40"][f"{refl_amp_type}_mean_code_1"]
                if refl_40_value != 0:  # 避免除零
                    refl_90_40_factor = board_info["refl_board"]["90"][f"{refl_amp_type}_mean_code_1"] / refl_40_value

            axes[subplot_idx].set_title(f'Board {int(board_id)} - Comp Distance {center_dist:.2f} 90/40 factor: {refl_90_40_factor:.2f}')
            axes[subplot_idx].set_xlabel('Angle (°)')
            axes[subplot_idx].set_ylabel(refl_amp_type.capitalize())
            axes[subplot_idx].legend()
            axes[subplot_idx].grid()
            subplot_idx += 1

        # Process compensation and refl points in the sixth subplot
        axes[5].set_title('Compensation Values')
        comp_fields = [f"{refl_amp_type}_comp_{sensor}" for sensor in refl_vec]

        for field in comp_fields:
            x_values = (chn_dist_area.index * 5 / 1000).to_numpy()
            y_values = chn_dist_area[field].to_numpy()
            mask = x_values <= 60
            axes[5].plot(x_values[mask], y_values[mask], label=field)

        for refl, points in sensor_points.items():
            axes[5].scatter(points["x"], points["y"], s=30, label=f'raw_{refl_amp_type}_{refl}')

        axes[5].set_xlabel('Distance (m)')
        axes[5].set_ylabel('Compensation Values')
        axes[5].legend()
        axes[5].grid()

        plt.suptitle(f'chn{chn} {refl_amp_type} Scatter Plot')
        plt.tight_layout()

        if save_plots:
            folder_name = 'refl'
            if refl_amp_type == 'amp':
                folder_name = 'amp'
            plt.savefig(os.path.join(root_path, folder_name, f"chn{chn}_{refl_amp_type}_board.png"))
        if show_plots:
            plt.show()

    if "refl" in input_type:
        plt.clf()
        plot_refl_amp_data(chn_info_json, refl_board_id_vec, "area", root_path, show_plots, save_plots)

    if "amp" in input_type:
        plt.clf()
        plot_refl_amp_data(chn_info_json, refl_board_id_vec, "amp", root_path, show_plots, save_plots)

    if "abs" in input_type:
        plt.clf()
        # Process refl boards
        dist_true_vec = np.array(chn_info_json["dist_true_vec"])
        dist_error_vec = np.array(chn_info_json["dist_error_vec"])
        # scatter
        # plt.scatter(dist_true_vec, dist_error_vec, label='Error Distance', s=10)
        print(len(dist_true_vec))
        print(len(dist_error_vec))

        fig, axes = plt.subplots(1, 2, figsize=(19.2, 10.8))

        test_value_index = 0
        for board_id in abs_board_id_vec:
            board_info = chn_info_json["board"][str(board_id)]
            if board_info["is_found"] != 1:
                print(f"Error: Board {board_id} not found in the data.")
                return
            start_idx = int(board_info['detected_data_start'])
            end_idx = int(board_info['detected_data_end'])
            distance = float(board_info['distance_05cm'])
            if 0 <= start_idx < total_length and 0 <= end_idx <= total_length:
                # Extract corresponding distance and area data
                area_orig = area_vec[start_idx:end_idx + 1]
                dist_orig = dist_vec[start_idx:end_idx + 1].copy()
                angle_orig = angle[start_idx:end_idx + 1]
                code_mark_orig = code_mark_vec[start_idx:end_idx + 1]
                # 根据当前点的面积补偿好动静标
                for i in range(len(dist_orig)):
                    area = area_orig[i]
                    dyn_comp = dynamic_comp_result_0[int(area)]
                    curr_static_comp = static_comp[0]
                    if code_mark_orig[i] == 1:
                        dyn_comp = dynamic_comp_result_1[int(area)]
                        curr_static_comp = static_comp[1]
                    dist_orig[i] = dist_orig[i] - dyn_comp - curr_static_comp

                # 取出中间一个点的值
                # dist_value = dist_orig[int(len(dist_orig)/2)]
                dist_value = dist_test_vec[test_value_index]
                index_value = angle_orig[int(len(dist_orig)/2)]
                test_value_index += 1

                # Scatter plot for classified data
                axes[0].scatter(angle_orig, dist_orig, label=f'Board {board_id} Distance {distance*5/1000}m', s=10)
                axes[0].scatter(index_value, dist_value, s=10)
                axes[0].annotate(f'abs:{dist_value:.2f},true:{distance:.2f}', xy=(index_value, dist_value), xytext=(index_value-10, dist_value+50))
                # axes[0].annotate(f'true: {distance:.2f}', xy=(index_value, dist_value), xytext=(index_value-10, dist_value-100))
        axes[0].set_title(f'chn{chn} abs_vec Scatter Plot')

        axes[0].legend()

        dist_true_vec_interpolation = np.arange(dist_true_vec[0],dist_true_vec[-1], 1)
        dist_error_interpolation = np.interp(dist_true_vec_interpolation, dist_true_vec, dist_error_vec)

        axes[1].plot(dist_true_vec_interpolation, dist_error_interpolation, label='Interpolation Error Distance' )
        axes[1].scatter(dist_true_vec, dist_error_vec, label='Error Distance')
        axes[1].set_title(f'chn{chn} True and Error Distance Scatter Plot')
        axes[1].set_xlabel('True Distance (5mm)')
        axes[1].set_ylabel('Error Distance (5mm)')
        axes[1].legend()


        if save_plots:
            plt.savefig(os.path.join(root_path, "abs", f"chn{chn}_abs.png"))
        if show_plots:
            plt.show()

def parse_arguments():
    parser = argparse.ArgumentParser(description='Process data files.')
    parser.add_argument('root_directory', type=str, nargs='?', default="/home/<USER>/development/projects",
                        help='Root directory path containing process data. Default is the debug path.')
    parser.add_argument('channel', type=int, nargs='?', default=1,
                        help='Channel number to process. Default is 1.')
    parser.add_argument('input_type', type=str, nargs='?', default="board,dynamic,static,refl,abs",
                        help="Input type to process, e.g., 'board,dyn,static,refl,abs'. Default includes all types.")
    parser.add_argument('--show', action='store_true', help='Show the plots in a window.')
    parser.add_argument('--save', action='store_true', help='Save the plots to files.')
    return parser.parse_args()

def main():
    args = parse_arguments()

    root_path = Path(args.root_directory)
    channel = args.channel
    input_type = args.input_type
    show_plots = args.show
    save_plots = args.save

    if os.path.exists(root_path):
        load_and_plot_data(root_path, channel, input_type, show_plots, save_plots)
    else:
        print("Error: The specified path does not exist.")

if __name__ == "__main__":
    main()